<template>
  <div class="dashboard-container" v-loading="loading">
    <div class="dashboard-header">
      <div class="dashboard-text">欢迎使用 WIFI共享商业系统管理后台</div>
      <el-button type="primary" icon="el-icon-refresh" @click="fetchDashboardData" :loading="loading">
        刷新数据
      </el-button>
    </div>
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>总用户数</span>
          </div>
          <div class="card-item">
            <div class="card-item-value">{{ userTotal }}</div>
            <div class="card-item-label">用户总数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>WIFI码总数</span>
          </div>
          <div class="card-item">
            <div class="card-item-value">{{ wifiTotal }}</div>
            <div class="card-item-label">WIFI码总数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>订单总数</span>
          </div>
          <div class="card-item">
            <div class="card-item-value">{{ orderTotal }}</div>
            <div class="card-item-label">订单总数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>总收入</span>
          </div>
          <div class="card-item">
            <div class="card-item-value">¥{{ totalIncome }}</div>
            <div class="card-item-label">总收入</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <div style="margin-top: 20px;">
      <el-card class="chart-card">
        <div slot="header" class="clearfix">
          <span>销售趋势</span>
          <el-radio-group v-model="chartType" size="mini" style="float: right;">
            <el-radio-button label="week">本周</el-radio-button>
            <el-radio-button label="month">本月</el-radio-button>
            <el-radio-button label="year">全年</el-radio-button>
          </el-radio-group>
        </div>
        <div class="chart-container">
          <div ref="salesChart" style="width: 100%; height: 400px;"></div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getPlatformOverview } from '@/api/platform'
import * as echarts from 'echarts'

export default {
  name: 'Dashboard',
  data () {
    return {
      userTotal: 0,
      wifiTotal: 0,
      orderTotal: 0,
      totalIncome: '0.00',
      chartType: 'week',
      chartInstance: null,
      loading: false
    }
  },
  mounted () {
    this.fetchDashboardData()
    this.initChart()
  },
  watch: {
    chartType () {
      this.updateChart()
    }
  },
  methods: {
    async fetchDashboardData () {
      try {
        this.loading = true
        const { data } = await getPlatformOverview()

        if (data) {
          this.userTotal = data.users?.total_users || 0
          this.wifiTotal = data.wifi?.total_wifi_codes || 0
          this.orderTotal = data.orders?.total_orders || 0
          this.totalIncome = data.revenue?.total_revenue || '0.00'

          // 只在手动刷新时显示成功消息
          if (this.loading) {
            this.$message.success('数据刷新成功')
          }
        }
      } catch (error) {
        console.error('获取仪表盘数据失败:', error)
        this.$message.error('获取仪表盘数据失败')
      } finally {
        this.loading = false
      }
    },

    initChart () {
      this.$nextTick(() => {
        if (this.$refs.salesChart) {
          this.chartInstance = echarts.init(this.$refs.salesChart)
          this.updateChart()
        }
      })
    },

    updateChart () {
      if (!this.chartInstance) return

      // 根据选择的时间类型生成模拟数据
      const data = this.generateChartData()

      const option = {
        title: {
          text: '销售趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['订单数', '收益'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: data.dates
        },
        yAxis: [
          {
            type: 'value',
            name: '订单数',
            position: 'left'
          },
          {
            type: 'value',
            name: '收益(元)',
            position: 'right'
          }
        ],
        series: [
          {
            name: '订单数',
            type: 'line',
            data: data.orders,
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '收益',
            type: 'line',
            yAxisIndex: 1,
            data: data.revenue,
            smooth: true,
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }

      this.chartInstance.setOption(option)
    },

    generateChartData () {
      const now = new Date()
      const dates = []
      const orders = []
      const revenue = []

      let days = 7
      if (this.chartType === 'month') days = 30
      else if (this.chartType === 'year') days = 365

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now)
        date.setDate(date.getDate() - i)

        if (this.chartType === 'year') {
          dates.push(`${date.getMonth() + 1}月`)
        } else {
          dates.push(`${date.getMonth() + 1}/${date.getDate()}`)
        }

        // 生成模拟数据，实际应该从API获取
        orders.push(Math.floor(Math.random() * 20) + 5)
        revenue.push((Math.random() * 1000 + 200).toFixed(2))
      }

      return { dates, orders, revenue }
    }
  },

  beforeDestroy () {
    if (this.chartInstance) {
      this.chartInstance.dispose()
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  &-container {
    margin: 30px;
  }
  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  &-text {
    font-size: 30px;
    line-height: 46px;
    margin: 0;
  }
}

.card-item {
  text-align: center;
  &-value {
    font-size: 28px;
    color: #409EFF;
    font-weight: bold;
    margin-bottom: 8px;
  }
  &-label {
    font-size: 14px;
    color: #666;
  }
}

.chart-container {
  position: relative;
  width: 100%;
  height: 400px;
}

.chart-card {
  margin-bottom: 20px;
}
</style>
