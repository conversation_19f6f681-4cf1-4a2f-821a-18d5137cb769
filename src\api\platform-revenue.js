import request from '@/utils/request'

// 获取平台收益概览
export function getPlatformRevenue() {
  return request({
    url: '/api/v1/admin/platform/revenue',
    method: 'get'
  })
}

// 获取收益趋势
export function getRevenueTrend(params) {
  return request({
    url: '/api/v1/admin/platform/revenue-trend',
    method: 'get',
    params
  })
}

// 获取业务类型分布
export function getBusinessDistribution(params) {
  return request({
    url: '/api/v1/admin/platform/business-distribution',
    method: 'get',
    params
  })
}

// 获取财务报表
export function getFinancialReport(params) {
  return request({
    url: '/api/v1/admin/platform/financial-report',
    method: 'get',
    params
  })
}

// 导出财务报表
export function exportFinancialReport(params) {
  return request({
    url: '/api/v1/admin/platform/export-report',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}
