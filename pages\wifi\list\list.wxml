<!--pages/wifi/list/list.wxml-->
<!--WiFi码列表页面模板-->

<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <view class="search-input-wrapper">
        <input 
          class="search-input"
          type="text"
          placeholder="搜索WiFi码..."
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          confirm-type="search"
        />
        <view class="search-icon">
          <text class="iconfont icon-search"></text>
        </view>
        <view 
          class="clear-icon {{searchKeyword ? 'show' : ''}}" 
          bindtap="onClearSearch"
        >
          <text class="iconfont icon-close"></text>
        </view>
      </view>
    </view>
  </view>

  <!-- 列表区域 -->
  <view class="list-section">
    <!-- 加载状态 -->
    <view class="loading-wrapper" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- WiFi码列表 -->
    <view class="wifi-list" wx:if="{{!loading && wifiList.length > 0}}">
      <view 
        class="wifi-item" 
        wx:for="{{wifiList}}" 
        wx:key="id"
        bindtap="onWifiItemTap"
        data-id="{{item.id}}"
      >
        <!-- WiFi码信息 -->
        <view class="wifi-info">
          <view class="wifi-header">
            <view class="wifi-title">{{item.title}}</view>
            <view class="wifi-status {{item.status === 'active' ? 'active' : 'inactive'}}">
              {{item.status === 'active' ? '活跃' : '未激活'}}
            </view>
          </view>
          
          <view class="wifi-details">
            <view class="wifi-detail-item">
              <text class="detail-label">WiFi名称:</text>
              <text class="detail-value">{{item.ssid}}</text>
            </view>
            <view class="wifi-detail-item">
              <text class="detail-label">商户名称:</text>
              <text class="detail-value">{{item.merchantName}}</text>
            </view>
          </view>

          <view class="wifi-stats">
            <view class="stat-item">
              <text class="stat-value">{{item.scanCount || 0}}</text>
              <text class="stat-label">扫码次数</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{item.connectCount || 0}}</text>
              <text class="stat-label">连接次数</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">¥{{item.totalEarnings || '0.00'}}</text>
              <text class="stat-label">累计收益</text>
            </view>
          </view>

          <view class="wifi-meta">
            <text class="create-time">创建时间: {{item.createTime}}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="wifi-actions" catchtap="true">
          <button 
            class="action-btn edit-btn" 
            size="mini"
            bindtap="onEditWifi"
            data-id="{{item.id}}"
          >
            编辑
          </button>
          <button 
            class="action-btn share-btn" 
            size="mini"
            bindtap="onShareWifi"
            data-id="{{item.id}}"
            data-title="{{item.title}}"
          >
            分享
          </button>
          <button 
            class="action-btn delete-btn" 
            size="mini"
            bindtap="onDeleteWifi"
            data-id="{{item.id}}"
            data-title="{{item.title}}"
          >
            删除
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && isEmpty}}">
      <image src="/assets/images/empty-wifi.png" class="empty-image" mode="aspectFit"></image>
      <text class="empty-title">还没有WiFi码</text>
      <text class="empty-subtitle">快去创建第一个WiFi码吧</text>
      <button class="empty-action-btn" bindtap="onCreateWifi">
        立即创建
      </button>
    </view>

    <!-- 加载更多状态 -->
    <view class="load-more" wx:if="{{!loading && wifiList.length > 0}}">
      <view class="load-more-wrapper" wx:if="{{loadingMore}}">
        <view class="loading-spinner small"></view>
        <text class="load-more-text">加载更多...</text>
      </view>
      <view class="load-more-end" wx:if="{{!loadingMore && !pageInfo.hasMore}}">
        <text class="load-more-text">没有更多了</text>
      </view>
    </view>
  </view>

  <!-- 悬浮创建按钮 -->
  <view class="fab-btn" bindtap="onCreateWifi">
    <text class="fab-icon">+</text>
  </view>
</view> 