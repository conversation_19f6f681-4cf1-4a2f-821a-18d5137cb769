const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const port = process.env.PORT || 3000;
const distPath = path.join(__dirname, 'dist');

// 创建日志目录
const logDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 日志函数
function writeLog(level, message, req = null) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    message,
    ...(req && {
      method: req.method,
      url: req.url,
      userAgent: req.headers['user-agent'],
      ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress
    })
  };

  const logLine = JSON.stringify(logEntry) + '\n';

  // 写入对应级别的日志文件
  const logFile = path.join(logDir, `${level}.log`);
  fs.appendFileSync(logFile, logLine);

  // 同时写入综合日志
  const combinedLogFile = path.join(logDir, 'wifi-share-admin.log');
  fs.appendFileSync(combinedLogFile, logLine);

  // 控制台输出
  console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
}

// MIME 类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.wav': 'audio/wav',
  '.mp4': 'video/mp4',
  '.woff': 'application/font-woff',
  '.ttf': 'application/font-ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'application/font-otf',
  '.wasm': 'application/wasm'
};

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url);
  let pathname = parsedUrl.pathname;

  // 记录请求日志
  writeLog('info', `${req.method} ${req.url}`, req);

  // 处理根路径
  if (pathname === '/') {
    pathname = '/index.html';
  }

  const filePath = path.join(distPath, pathname);

  // 检查文件是否存在
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // 文件不存在，返回 index.html (SPA 路由支持)
      writeLog('warn', `File not found: ${pathname}, serving index.html`, req);
      const indexPath = path.join(distPath, 'index.html');
      fs.readFile(indexPath, (err, data) => {
        if (err) {
          writeLog('error', `Failed to read index.html: ${err.message}`, req);
          res.writeHead(404, { 'Content-Type': 'text/plain' });
          res.end('404 Not Found');
          return;
        }
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end(data);
      });
      return;
    }

    // 文件存在，读取并返回
    fs.readFile(filePath, (err, data) => {
      if (err) {
        writeLog('error', `Failed to read file ${pathname}: ${err.message}`, req);
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('500 Internal Server Error');
        return;
      }

      const ext = path.extname(filePath);
      const contentType = mimeTypes[ext] || 'application/octet-stream';

      writeLog('info', `Successfully served: ${pathname} (${contentType})`, req);
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(data);
    });
  });
});

server.listen(port, () => {
  const startMessage = `WiFi共享管理后台启动成功 - 端口: ${port}`;
  writeLog('info', startMessage);
  writeLog('info', `访问地址: http://localhost:${port}`);
  writeLog('info', `静态文件目录: ${distPath}`);
  writeLog('info', `日志目录: ${logDir}`);

  console.log(`WiFi共享管理后台运行在端口 ${port}`);
  console.log(`访问地址: http://localhost:${port}`);
  console.log(`静态文件目录: ${distPath}`);
  console.log(`日志目录: ${logDir}`);
});

// 处理进程退出
process.on('SIGINT', () => {
  writeLog('info', 'WiFi共享管理后台正在关闭...');
  console.log('\nWiFi共享管理后台正在关闭...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  writeLog('info', 'WiFi共享管理后台收到SIGTERM信号，正在关闭...');
  console.log('WiFi共享管理后台收到SIGTERM信号，正在关闭...');
  process.exit(0);
});
