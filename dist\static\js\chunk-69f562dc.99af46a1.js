(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-69f562dc"],{"1f0c":function(t,e,a){},"2bf9":function(t,e,a){"use strict";a("1f0c")},"330e":function(t,e,a){"use strict";a("34b0")},"333d":function(t,e,a){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[e("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},i=[],l={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get(){return this.page},set(t){this.$emit("update:page",t)}},pageSize:{get(){return this.limit},set(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange(t){this.$emit("pagination",{page:this.currentPage,limit:t})},handleCurrentChange(t){this.$emit("pagination",{page:t,limit:this.pageSize})}}},n=l,r=(a("330e"),a("2877")),o=Object(r["a"])(n,s,i,!1,null,"11252b03",null);e["a"]=o.exports},"34b0":function(t,e,a){},3903:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("el-row",{staticClass:"stats-cards",attrs:{gutter:20}},[e("el-col",{attrs:{span:4}},[e("el-card",{staticClass:"stat-card"},[e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.stats.total_users))]),e("div",{staticClass:"stat-label"},[t._v("总用户数")])]),e("i",{staticClass:"el-icon-user stat-icon"})])],1),e("el-col",{attrs:{span:4}},[e("el-card",{staticClass:"stat-card"},[e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.stats.active_users))]),e("div",{staticClass:"stat-label"},[t._v("活跃用户")])]),e("i",{staticClass:"el-icon-check stat-icon"})])],1),e("el-col",{attrs:{span:4}},[e("el-card",{staticClass:"stat-card"},[e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.stats.leader_users))]),e("div",{staticClass:"stat-label"},[t._v("团长用户")])]),e("i",{staticClass:"el-icon-star-on stat-icon"})])],1),e("el-col",{attrs:{span:4}},[e("el-card",{staticClass:"stat-card"},[e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.stats.today_users))]),e("div",{staticClass:"stat-label"},[t._v("今日新增")])]),e("i",{staticClass:"el-icon-plus stat-icon"})])],1),e("el-col",{attrs:{span:4}},[e("el-card",{staticClass:"stat-card"},[e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.stats.month_users))]),e("div",{staticClass:"stat-label"},[t._v("本月新增")])]),e("i",{staticClass:"el-icon-date stat-icon"})])],1),e("el-col",{attrs:{span:4}},[e("el-card",{staticClass:"stat-card"},[e("div",{staticClass:"stat-content"},[e("div",{staticClass:"stat-number"},[t._v(t._s(t.stats.total_balance.toFixed(2)))]),e("div",{staticClass:"stat-label"},[t._v("总余额(元)")])]),e("i",{staticClass:"el-icon-money stat-icon"})])],1)],1),e("div",{staticClass:"filter-container"},[e("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"用户昵称/手机号",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.keyword,callback:function(e){t.$set(t.listQuery,"keyword",e)},expression:"listQuery.keyword"}}),e("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"状态",clearable:""},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},t._l(t.statusOptions,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),e("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"是否团长",clearable:""},model:{value:t.listQuery.is_leader,callback:function(e){t.$set(t.listQuery,"is_leader",e)},expression:"listQuery.is_leader"}},t._l(t.leaderOptions,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),e("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleFilter}},[t._v("搜索")]),e("el-button",{staticClass:"filter-item",attrs:{type:"success",icon:"el-icon-refresh"},on:{click:t.handleRefresh}},[t._v("刷新")])],1),t.selectedUsers.length>0?e("div",{staticClass:"batch-actions"},[e("span",{staticClass:"batch-info"},[t._v("已选择 "+t._s(t.selectedUsers.length)+" 个用户")]),e("el-button",{attrs:{size:"small",type:"success"},on:{click:t.handleBatchEnable}},[t._v("批量启用")]),e("el-button",{attrs:{size:"small",type:"warning"},on:{click:t.handleBatchDisable}},[t._v("批量禁用")]),e("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){t.showBatchLevelDialog=!0}}},[t._v("设置等级")]),e("el-button",{attrs:{size:"small",type:"info"},on:{click:function(e){t.showBatchTagDialog=!0}}},[t._v("分配标签")])],1):t._e(),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{data:t.list,"element-loading-text":"正在加载...",border:"",fit:"","highlight-current-row":""},on:{"selection-change":t.handleSelectionChange}},[e("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),e("el-table-column",{attrs:{label:"ID",prop:"id",align:"center",width:"80"}}),e("el-table-column",{attrs:{label:"头像",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function({row:t}){return[e("el-avatar",{attrs:{src:t.avatar,size:40}},[e("img",{attrs:{src:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"}})])]}}])}),e("el-table-column",{attrs:{label:"昵称",prop:"nickname",align:"center","min-width":"120"}}),e("el-table-column",{attrs:{label:"手机号",prop:"phone",align:"center","min-width":"120"}}),e("el-table-column",{attrs:{label:"余额",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("span",[t._v(t._s(a.balance)+" 元")])]}}])}),e("el-table-column",{attrs:{label:"是否团长",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("el-tag",{attrs:{type:1===a.is_leader?"success":"info"}},[t._v(" "+t._s(1===a.is_leader?"是":"否")+" ")])]}}])}),e("el-table-column",{attrs:{label:"用户等级",prop:"level",align:"center",width:"100"}}),e("el-table-column",{attrs:{label:"状态",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("el-tag",{attrs:{type:1===a.status?"success":"info"}},[t._v(" "+t._s(1===a.status?"启用":"禁用")+" ")])]}}])}),e("el-table-column",{attrs:{label:"注册时间",align:"center",width:"160"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("span",[t._v(t._s(a.created_at))])]}}])}),e("el-table-column",{attrs:{label:"操作",align:"center",width:"280","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.handleView(a)}}},[t._v("查看")]),e("el-button",{attrs:{type:"info",size:"mini"},on:{click:function(e){return t.handleEdit(a)}}},[t._v("编辑")]),e("el-button",{attrs:{type:"warning",size:"mini"},on:{click:function(e){return t.handleBalanceAdjust(a)}}},[t._v("余额")]),1===a.status?e("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.handleStatusChange(a,0)}}},[t._v("禁用")]):e("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(e){return t.handleStatusChange(a,1)}}},[t._v("启用")])]}}])})],1),e("pagination",{directives:[{name:"show",rawName:"v-show",value:t.safeTotal>0,expression:"safeTotal>0"}],attrs:{total:t.safeTotal,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}}),e("el-dialog",{attrs:{title:"编辑用户信息",visible:t.editDialogVisible,width:"500px"},on:{"update:visible":function(e){t.editDialogVisible=e}}},[e("el-form",{ref:"editForm",attrs:{model:t.editForm,rules:t.editRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"昵称",prop:"nickname"}},[e("el-input",{model:{value:t.editForm.nickname,callback:function(e){t.$set(t.editForm,"nickname",e)},expression:"editForm.nickname"}})],1),e("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[e("el-input",{model:{value:t.editForm.phone,callback:function(e){t.$set(t.editForm,"phone",e)},expression:"editForm.phone"}})],1),e("el-form-item",{attrs:{label:"用户等级",prop:"level"}},[e("el-input-number",{attrs:{min:0,max:10},model:{value:t.editForm.level,callback:function(e){t.$set(t.editForm,"level",e)},expression:"editForm.level"}})],1),e("el-form-item",{attrs:{label:"状态",prop:"status"}},[e("el-radio-group",{model:{value:t.editForm.status,callback:function(e){t.$set(t.editForm,"status",e)},expression:"editForm.status"}},[e("el-radio",{attrs:{label:1}},[t._v("启用")]),e("el-radio",{attrs:{label:0}},[t._v("禁用")])],1)],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.editDialogVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.submitEdit}},[t._v("确定")])],1)],1),e("el-dialog",{attrs:{title:"调整用户余额",visible:t.balanceDialogVisible,width:"400px"},on:{"update:visible":function(e){t.balanceDialogVisible=e}}},[e("el-form",{ref:"balanceForm",attrs:{model:t.balanceForm,rules:t.balanceRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"当前余额"}},[e("span",[t._v(t._s(t.currentUser.balance)+" 元")])]),e("el-form-item",{attrs:{label:"操作类型",prop:"type"}},[e("el-radio-group",{model:{value:t.balanceForm.type,callback:function(e){t.$set(t.balanceForm,"type",e)},expression:"balanceForm.type"}},[e("el-radio",{attrs:{label:"add"}},[t._v("增加")]),e("el-radio",{attrs:{label:"subtract"}},[t._v("减少")])],1)],1),e("el-form-item",{attrs:{label:"调整金额",prop:"amount"}},[e("el-input-number",{attrs:{min:.01,step:.01,precision:2},model:{value:t.balanceForm.amount,callback:function(e){t.$set(t.balanceForm,"amount",e)},expression:"balanceForm.amount"}})],1),e("el-form-item",{attrs:{label:"备注",prop:"remark"}},[e("el-input",{attrs:{type:"textarea",rows:3},model:{value:t.balanceForm.remark,callback:function(e){t.$set(t.balanceForm,"remark",e)},expression:"balanceForm.remark"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.balanceDialogVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.submitBalanceAdjust}},[t._v("确定")])],1)],1),e("el-dialog",{attrs:{title:"批量设置用户等级",visible:t.showBatchLevelDialog,width:"400px"},on:{"update:visible":function(e){t.showBatchLevelDialog=e}}},[e("el-form",{attrs:{"label-width":"100px"}},[e("el-form-item",{attrs:{label:"用户等级"}},[e("el-input-number",{attrs:{min:0,max:10},model:{value:t.batchLevel,callback:function(e){t.batchLevel=e},expression:"batchLevel"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showBatchLevelDialog=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.handleBatchSetLevel}},[t._v("确定")])],1)],1),e("el-dialog",{attrs:{title:"批量分配标签",visible:t.showBatchTagDialog,width:"500px"},on:{"update:visible":function(e){t.showBatchTagDialog=e}}},[e("el-form",{attrs:{"label-width":"100px"}},[e("el-form-item",{attrs:{label:"选择标签"}},[e("el-checkbox-group",{model:{value:t.selectedTags,callback:function(e){t.selectedTags=e},expression:"selectedTags"}},t._l(t.availableTags,(function(a){return e("el-checkbox",{key:a.id,attrs:{label:a.id}},[t._v(" "+t._s(a.name)+" ")])})),1)],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.showBatchTagDialog=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.handleBatchAssignTags}},[t._v("确定")])],1)],1)],1)},i=[],l=(a("14d9"),a("e9f5"),a("ab43"),a("92c2")),n=a("333d"),r={name:"UserList",components:{Pagination:n["a"]},data(){return{list:null,total:0,listLoading:!0,selectedUsers:[],stats:{total_users:0,active_users:0,leader_users:0,today_users:0,month_users:0,total_balance:0},listQuery:{page:1,limit:10,keyword:void 0,status:void 0,is_leader:void 0},statusOptions:[{label:"启用",value:1},{label:"禁用",value:0}],leaderOptions:[{label:"是",value:1},{label:"否",value:0}],editDialogVisible:!1,editForm:{id:null,nickname:"",phone:"",level:0,status:1},editRules:{nickname:[{max:50,message:"昵称长度不能超过50个字符",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},balanceDialogVisible:!1,balanceForm:{type:"add",amount:0,remark:""},balanceRules:{amount:[{required:!0,message:"请输入调整金额",trigger:"blur"},{type:"number",min:.01,message:"金额必须大于0",trigger:"blur"}]},currentUser:{},showBatchLevelDialog:!1,batchLevel:1,showBatchTagDialog:!1,selectedTags:[],availableTags:[]}},computed:{safeTotal(){if(null===this.total||void 0===this.total)return 0;if("string"===typeof this.total){const t=parseInt(this.total,10);return isNaN(t)?0:t}return"number"!==typeof this.total||isNaN(this.total)?0:Math.max(0,Math.floor(this.total))}},created(){this.getStats(),this.getList(),this.loadTags()},methods:{getStats(){Object(l["h"])().then(t=>{this.stats=t.data}).catch(()=>{})},getList(){this.listLoading=!0,Object(l["g"])(this.listQuery).then(t=>{this.list=t.data.list||[],this.total=t.data.total||0,this.listLoading=!1}).catch(t=>{console.error("获取用户列表失败:",t),this.list=[],this.total=0,this.listLoading=!1,this.$message.error("获取用户列表失败")})},loadTags(){Object(l["i"])().then(t=>{this.availableTags=t.data||[]}).catch(()=>{})},handleFilter(){this.listQuery.page=1,this.getList()},handleRefresh(){this.getStats(),this.getList()},handleSelectionChange(t){this.selectedUsers=t},handleView(t){this.$router.push("/user/detail/"+t.id)},handleEdit(t){this.editForm={id:t.id,nickname:t.nickname,phone:t.phone,level:t.level,status:t.status},this.editDialogVisible=!0},submitEdit(){this.$refs.editForm.validate(t=>{t&&Object(l["k"])(this.editForm.id,this.editForm).then(t=>{this.$message.success("更新成功"),this.editDialogVisible=!1,this.getList()}).catch(()=>{this.$message.error("更新失败")})})},handleStatusChange(t,e){Object(l["l"])(t.id,{status:e}).then(a=>{this.$message.success("状态更新成功"),t.status=e,this.getStats()}).catch(()=>{this.$message.error("状态更新失败")})},handleBalanceAdjust(t){this.currentUser=t,this.balanceForm={type:"add",amount:0,remark:""},this.balanceDialogVisible=!0},submitBalanceAdjust(){this.$refs.balanceForm.validate(t=>{t&&Object(l["a"])(this.currentUser.id,this.balanceForm).then(t=>{this.$message.success("余额调整成功"),this.balanceDialogVisible=!1,this.getList(),this.getStats()}).catch(()=>{this.$message.error("余额调整失败")})})},handleBatchEnable(){this.batchOperate("enable")},handleBatchDisable(){this.batchOperate("disable")},handleBatchSetLevel(){this.batchOperate("set_level",{level:this.batchLevel}),this.showBatchLevelDialog=!1},handleBatchAssignTags(){this.$message.success("标签分配功能开发中"),this.showBatchTagDialog=!1},batchOperate(t,e={}){const a=this.selectedUsers.map(t=>t.id);Object(l["b"])({user_ids:a,action:t,data:e}).then(t=>{this.$message.success("批量操作成功"),this.getList(),this.getStats()}).catch(()=>{this.$message.error("批量操作失败")})}}},o=r,c=(a("2bf9"),a("2877")),d=Object(c["a"])(o,s,i,!1,null,"e4d2af8c",null);e["default"]=d.exports},"92c2":function(t,e,a){"use strict";a.d(e,"g",(function(){return r})),a.d(e,"f",(function(){return o})),a.d(e,"k",(function(){return c})),a.d(e,"l",(function(){return d})),a.d(e,"c",(function(){return u})),a.d(e,"m",(function(){return m})),a.d(e,"d",(function(){return h})),a.d(e,"i",(function(){return b})),a.d(e,"h",(function(){return f})),a.d(e,"a",(function(){return v})),a.d(e,"b",(function(){return _})),a.d(e,"e",(function(){return y})),a.d(e,"j",(function(){return w}));a("14d9"),a("e9f5"),a("ab43");var s=a("b775");const i=!1,l="wifi_admin_user_tags",n=[{id:1,name:"VIP用户",description:"重要VIP客户",created_at:"2023-06-10 10:30:45"},{id:2,name:"新用户",description:"注册不满30天的用户",created_at:"2023-06-08 14:20:30"},{id:3,name:"商家",description:"拥有商铺的用户",created_at:"2023-06-05 09:15:00"},{id:4,name:"活跃用户",description:"近30天有登录的用户",created_at:"2023-06-01 16:40:20"}];function r(t){return i?new Promise(e=>{setTimeout(()=>{const a=[],s=10*t.pageSize||100;for(let e=1;e<=Math.min(t.pageSize||10,s);e++){const s=(t.pageNum-1)*(t.pageSize||10),i=s+e;a.push({id:i,openid:"oMKLx5M2xxxxxxxx"+i,nickname:["张三","李四","王五","赵六","刘七"][e%5]||"用户"+i,avatar:"/img/default-avatar.png",gender:e%3,phone:"1380013800"+e,balance:parseFloat((500*Math.random()).toFixed(2)),team_id:e%3===0?1:e%3===1?2:null,is_leader:e%7===0?1:0,level:e%5,status:e%10===0?0:1,created_at:`2023-05-${String(e%30+1).padStart(2,"0")} 10:20:30`,updated_at:`2023-06-${String(e%28+1).padStart(2,"0")} 15:30:45`})}e({code:200,data:{list:a,total:s},message:"获取用户列表成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/list",method:"get",params:t})}function o(t){return i?new Promise(e=>{setTimeout(()=>{const a=parseInt(t),s={id:a,openid:"oMKLx5M2xxxxxxxx"+t,nickname:["","张三","李四","王五","赵六"][a]||"用户"+t,avatar:"/img/default-avatar.png",gender:a%2===0?2:1,phone:"1380013800"+t,balance:parseFloat((500*Math.random()).toFixed(2)),team_id:a<=2?1:3===a?2:null,parent_id:2===a?1:null,is_leader:[1,3].includes(a)?1:0,level:[1,3].includes(a)?2:2===a?1:0,status:4===a?0:1,created_at:`2023-05-0${a>4?"1":a} 10:20:30`,updated_at:`2023-05-0${a>4?"1":a} 15:30:45`};let i=null;s.team_id&&(i={id:s.team_id,name:"团队"+s.team_id,member_count:Math.floor(20*Math.random())+5,wifi_count:Math.floor(10*Math.random())+2,total_profit:parseFloat((5e3*Math.random()).toFixed(2))});const l=[{id:10*a+1,title:s.nickname+"的WiFi-1",name:`Wifi_${a}_1`,use_count:Math.floor(100*Math.random())+10,created_at:"2023-06-01 09:30:00"},{id:10*a+2,title:s.nickname+"的WiFi-2",name:`Wifi_${a}_2`,use_count:Math.floor(100*Math.random())+10,created_at:"2023-06-02 14:20:00"}],n=[{id:100*a+1,order_no:`WF${Date.now()}${a}01`,total_amount:parseFloat((200*Math.random()+50).toFixed(2)),status:Math.floor(4*Math.random())+1,created_at:"2023-06-01 16:45:30"},{id:100*a+2,order_no:`WF${Date.now()}${a}02`,total_amount:parseFloat((200*Math.random()+50).toFixed(2)),status:Math.floor(4*Math.random())+1,created_at:"2023-06-02 10:15:20"}];e({code:200,data:{user_info:s,team_info:i,wifi_list:l,order_list:n},message:"获取用户详情成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/detail/"+t,method:"get"})}function c(t,e){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{},message:"更新用户成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/update/"+t,method:"put",data:e})}function d(t,e){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{},message:"更新用户状态成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/status/"+t,method:"put",data:e})}function u(t){return i?new Promise(e=>{setTimeout(()=>{const a=p(),s=a.length>0?Math.max(...a.map(t=>t.id))+1:1,i={id:s,name:t.name,description:t.description||"",created_at:(new Date).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")};a.push(i),g(a),e({code:200,data:{id:s},message:"创建用户标签成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/tag/create",method:"post",data:t})}function m(t,e){return i?new Promise(a=>{setTimeout(()=>{const s=p(),i=s.findIndex(e=>e.id===parseInt(t));-1!==i?(s[i].name=e.name,s[i].description=e.description||s[i].description,g(s),a({code:200,message:"更新用户标签成功"})):a({code:404,message:"标签不存在"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/tag/update/"+t,method:"put",data:e})}function h(t){return i?new Promise(e=>{setTimeout(()=>{const a=p(),s=a.findIndex(e=>e.id===parseInt(t));-1!==s?(a.splice(s,1),g(a),e({code:200,message:"删除用户标签成功"})):e({code:404,message:"标签不存在"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/tag/delete/"+t,method:"delete"})}function p(){try{const t=localStorage.getItem(l);return t?JSON.parse(t):n}catch(t){return console.warn("读取用户标签数据失败，使用默认数据:",t),n}}function g(t){try{localStorage.setItem(l,JSON.stringify(t))}catch(e){console.error("保存用户标签数据失败:",e)}}function b(){return i?new Promise(t=>{setTimeout(()=>{const e=p();t({code:200,data:[...e],message:"获取用户标签列表成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/tag/list",method:"get"})}function f(){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{total_users:1256,active_users:1180,leader_users:45,today_users:23,month_users:187,total_balance:125680.5,avg_balance:106.51},message:"获取用户统计成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/stats",method:"get"})}function v(t,e){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{before_balance:100,after_balance:"add"===e.type?100+parseFloat(e.amount):100-parseFloat(e.amount)},message:"余额调整成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/balance/"+t,method:"post",data:e})}function _(t){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{},message:"批量操作成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/batch",method:"post",data:t})}function y(t){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{total_orders:Math.floor(50*Math.random())+10,total_amount:parseFloat((5e3*Math.random()+1e3).toFixed(2)),avg_order_amount:parseFloat((200*Math.random()+50).toFixed(2)),last_order_time:"2023-06-15 14:30:20",favorite_category:"数码产品",monthly_stats:[{month:"2023-01",orders:3,amount:450},{month:"2023-02",orders:5,amount:680.5},{month:"2023-03",orders:2,amount:320},{month:"2023-04",orders:4,amount:590.3},{month:"2023-05",orders:6,amount:780.2},{month:"2023-06",orders:3,amount:420}]},message:"获取消费统计成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/consumption-stats/"+t,method:"get"})}function w(t){return i?new Promise(t=>{setTimeout(()=>{t({code:200,data:{total_wifi:Math.floor(20*Math.random())+5,total_scans:Math.floor(1e3*Math.random())+100,avg_scans_per_wifi:Math.floor(50*Math.random())+10,most_popular_wifi:{id:1,title:"星巴克WiFi",scan_count:156},recent_activity:[{date:"2023-06-15",scans:23},{date:"2023-06-14",scans:18},{date:"2023-06-13",scans:31},{date:"2023-06-12",scans:25},{date:"2023-06-11",scans:19}]},message:"获取WiFi统计成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/user/wifi-stats/"+t,method:"get"})}},ab43:function(t,e,a){"use strict";var s=a("23e7"),i=a("c65b"),l=a("59ed"),n=a("825a"),r=a("46c4"),o=a("c5cc"),c=a("9bdd"),d=a("2a62"),u=a("2baa"),m=a("f99f"),h=a("c430"),p=!h&&!u("map",(function(){})),g=!h&&!p&&m("map",TypeError),b=h||p||g,f=o((function(){var t=this.iterator,e=n(i(this.next,t)),a=this.done=!!e.done;if(!a)return c(t,this.mapper,[e.value,this.counter++],!0)}));s({target:"Iterator",proto:!0,real:!0,forced:b},{map:function(t){n(this);try{l(t)}catch(e){d(this,"throw",e)}return g?i(g,this,t):new f(r(this),{mapper:t})}})}}]);