(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-44411ae0"],{"13d5":function(t,e,a){"use strict";var s=a("23e7"),i=a("d58f").left,r=a("a640"),n=a("1212"),o=a("9adc"),l=!o&&n>79&&n<83,c=l||!r("reduce");s({target:"Array",proto:!0,forced:c},{reduce:function(t){var e=arguments.length;return i(this,t,e,e>1?arguments[1]:void 0)}})},"2edf":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"bill-detail"},[t.loading||0!==Object.keys(t.detail).length?e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("分润账单详情")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.goBack}},[t._v("返回")])],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("div",{staticClass:"info-section"},[e("h3",{staticClass:"section-title"},[t._v("基本信息")]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("分润ID：")]),e("span",{staticClass:"value"},[t._v(t._s(t.detail.id))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("分润金额：")]),e("span",{staticClass:"value profit-amount"},[t._v(t._s(t.detail.amount)+" 元")])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("分润类型：")]),e("span",{staticClass:"value"},[e("el-tag",{attrs:{type:t.getSourceTypeTag(t.detail.source_type)}},[t._v(t._s(t.getSourceTypeLabel(t.detail.source_type)))])],1)]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("来源ID：")]),e("span",{staticClass:"value"},[t._v(t._s(t.detail.source_id))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("创建时间：")]),e("span",{staticClass:"value"},[t._v(t._s(t.detail.created_at))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("备注：")]),e("span",{staticClass:"value"},[t._v(t._s(t.detail.remark||"-"))])])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"info-section"},[e("h3",{staticClass:"section-title"},[t._v("用户信息")]),e("div",{staticClass:"user-header"},[e("el-avatar",{attrs:{size:64,src:t.userInfo.avatar}},[e("img",{attrs:{src:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"}})]),e("h4",{staticClass:"nickname"},[t._v(t._s(t.userInfo.nickname))]),e("div",{staticClass:"user-role"},[1===t.userInfo.is_leader?e("el-tag",{attrs:{type:"warning"}},[t._v("团长")]):e("el-tag",{attrs:{type:"info"}},[t._v("成员")])],1)],1),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("用户ID：")]),e("span",{staticClass:"value"},[t._v(t._s(t.userInfo.id))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("手机号：")]),e("span",{staticClass:"value"},[t._v(t._s(t.userInfo.phone||"-"))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("账户余额：")]),e("span",{staticClass:"value"},[t._v(t._s(t.userInfo.balance||0)+" 元")])]),e("div",{staticClass:"info-item"},[e("el-button",{attrs:{type:"text"},on:{click:t.viewUser}},[t._v("查看用户详情")])],1)])])],1),1===t.detail.source_type?e("div",{staticClass:"source-detail"},[e("h3",{staticClass:"section-title"},[t._v("WiFi码分润详情")]),e("el-descriptions",{attrs:{column:2,border:""}},[e("el-descriptions-item",{attrs:{label:"WiFi标题"}},[t._v(t._s(t.wifiInfo.title))]),e("el-descriptions-item",{attrs:{label:"WiFi名称"}},[t._v(t._s(t.wifiInfo.name))]),e("el-descriptions-item",{attrs:{label:"商户名称"}},[t._v(t._s(t.wifiInfo.merchant_name))]),e("el-descriptions-item",{attrs:{label:"使用次数"}},[t._v(t._s(t.wifiInfo.use_count))])],1),e("div",{staticClass:"action-btn"},[e("el-button",{attrs:{type:"primary"},on:{click:t.viewWifi}},[t._v("查看WiFi码详情")])],1)],1):2===t.detail.source_type?e("div",{staticClass:"source-detail"},[e("h3",{staticClass:"section-title"},[t._v("订单分润详情")]),e("el-descriptions",{attrs:{column:2,border:""}},[e("el-descriptions-item",{attrs:{label:"订单号"}},[t._v(t._s(t.orderInfo.order_no))]),e("el-descriptions-item",{attrs:{label:"订单金额"}},[t._v(t._s(t.orderInfo.total_amount)+" 元")]),e("el-descriptions-item",{attrs:{label:"订单状态"}},[e("el-tag",{attrs:{type:t.getOrderStatusType(t.orderInfo.status)}},[t._v(t._s(t.getOrderStatusText(t.orderInfo.status)))])],1),e("el-descriptions-item",{attrs:{label:"下单时间"}},[t._v(t._s(t.orderInfo.created_at))])],1),e("div",{staticClass:"action-btn"},[e("el-button",{attrs:{type:"primary"},on:{click:t.viewOrder}},[t._v("查看订单详情")])],1)],1):3===t.detail.source_type?e("div",{staticClass:"source-detail"},[e("h3",{staticClass:"section-title"},[t._v("广告分润详情")]),e("el-descriptions",{attrs:{column:2,border:""}},[e("el-descriptions-item",{attrs:{label:"广告标题"}},[t._v(t._s(t.adInfo.title))]),e("el-descriptions-item",{attrs:{label:"广告位"}},[t._v(t._s(t.adInfo.space_name))]),e("el-descriptions-item",{attrs:{label:"点击次数"}},[t._v(t._s(t.adInfo.click_count))]),e("el-descriptions-item",{attrs:{label:"展示次数"}},[t._v(t._s(t.adInfo.view_count))])],1),e("div",{staticClass:"action-btn"},[e("el-button",{attrs:{type:"primary"},on:{click:t.viewAd}},[t._v("查看广告详情")])],1)],1):t._e(),e("div",{staticClass:"profit-detail"},[e("h3",{staticClass:"section-title"},[t._v("分润计算明细")]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.profitDetailList,border:""}},[e("el-table-column",{attrs:{prop:"role",label:"角色",width:"120",align:"center"}}),e("el-table-column",{attrs:{prop:"rate",label:"分润比例",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function({row:e}){return[t._v(t._s(e.rate)+"%")]}}])}),e("el-table-column",{attrs:{prop:"amount",label:"分润金额",align:"center"},scopedSlots:t._u([{key:"default",fn:function({row:e}){return[t._v(t._s(e.amount)+" 元")]}}])}),e("el-table-column",{attrs:{prop:"user_info",label:"分润用户",align:"center"}})],1)],1)],1):e("div",{staticStyle:{"text-align":"center",padding:"50px"}},[e("el-alert",{attrs:{title:"数据加载失败",type:"warning",description:"无法获取账单详情，请检查网络连接或稍后重试","show-icon":""}}),e("el-button",{staticStyle:{"margin-top":"20px"},attrs:{type:"primary"},on:{click:t.fetchData}},[t._v("重新加载")])],1)],1)])},i=[],r=(a("14d9"),a("f851")),n={name:"ProfitBillDetail",data(){return{loading:!0,billId:null,detail:{},userInfo:{},wifiInfo:{},orderInfo:{},adInfo:{},profitDetailList:[]}},created(){this.billId=parseInt(this.$route.params.id),this.fetchData()},methods:{fetchData(){this.loading=!0,console.log("开始获取账单详情，ID:",this.billId),Object(r["c"])(this.billId).then(t=>{console.log("API返回数据:",t);const{detail:e,user_info:a,source_info:s,profit_detail:i}=t.data;this.detail=e,this.userInfo=a,1===e.source_type?this.wifiInfo=s||{}:2===e.source_type?this.orderInfo=s||{}:3===e.source_type&&(this.adInfo=s||{}),this.profitDetailList=i||[],this.loading=!1,console.log("数据加载完成")}).catch(t=>{console.error("获取账单详情失败:",t),this.$message.error("获取账单详情失败："+(t.message||"未知错误")),this.loading=!1})},getSourceTypeLabel(t){const e={1:"WiFi分润",2:"商品分润",3:"广告分润"};return e[t]||"未知"},getSourceTypeTag(t){const e={1:"primary",2:"success",3:"warning"};return e[t]||"info"},getOrderStatusType(t){const e={0:"info",1:"primary",2:"warning",3:"success",4:"danger"};return e[t]||"info"},getOrderStatusText(t){const e={0:"待支付",1:"待发货",2:"待收货",3:"已完成",4:"已取消"};return e[t]||"未知状态"},goBack(){this.$router.push("/profit/bill")},viewUser(){this.$router.push("/user/detail/"+this.userInfo.id)},viewWifi(){this.$router.push("/wifi/detail/"+this.wifiInfo.id)},viewOrder(){this.$router.push("/mall/order/detail/"+this.orderInfo.id)},viewAd(){this.$message.info("广告详情查看功能暂未实现")}}},o=n,l=(a("5ac6"),a("2877")),c=Object(l["a"])(o,s,i,!1,null,"5d6d20c0",null);e["default"]=c.exports},"41d9":function(t,e,a){},"5ac6":function(t,e,a){"use strict";a("41d9")},8558:function(t,e,a){"use strict";var s=a("cfe9"),i=a("b5db"),r=a("c6b6"),n=function(t){return i.slice(0,t.length)===t};t.exports=function(){return n("Bun/")?"BUN":n("Cloudflare-Workers")?"CLOUDFLARE":n("Deno/")?"DENO":n("Node.js/")?"NODE":s.Bun&&"string"==typeof Bun.version?"BUN":s.Deno&&"object"==typeof Deno.version?"DENO":"process"===r(s.process)?"NODE":s.window&&s.document?"BROWSER":"REST"}()},9485:function(t,e,a){"use strict";var s=a("23e7"),i=a("2266"),r=a("59ed"),n=a("825a"),o=a("46c4"),l=a("2a62"),c=a("f99f"),u=a("2ba4"),d=a("d039"),_=TypeError,m=d((function(){[].keys().reduce((function(){}),void 0)})),p=!m&&c("reduce",_);s({target:"Iterator",proto:!0,real:!0,forced:m||p},{reduce:function(t){n(this);try{r(t)}catch(d){l(this,"throw",d)}var e=arguments.length<2,a=e?void 0:arguments[1];if(p)return u(p,this,e?[t]:[t,a]);var s=o(this),c=0;if(i(s,(function(s){e?(e=!1,a=s):a=t(a,s,c),c++}),{IS_RECORD:!0}),e)throw new _("Reduce of empty iterator with no initial value");return a}})},"9adc":function(t,e,a){"use strict";var s=a("8558");t.exports="NODE"===s},a640:function(t,e,a){"use strict";var s=a("d039");t.exports=function(t,e){var a=[][t];return!!a&&s((function(){a.call(null,e||function(){return 1},1)}))}},d58f:function(t,e,a){"use strict";var s=a("59ed"),i=a("7b0b"),r=a("44ad"),n=a("07fa"),o=TypeError,l="Reduce of empty array with no initial value",c=function(t){return function(e,a,c,u){var d=i(e),_=r(d),m=n(d);if(s(a),0===m&&c<2)throw new o(l);var p=t?m-1:0,f=t?-1:1;if(c<2)while(1){if(p in _){u=_[p],p+=f;break}if(p+=f,t?p<0:m<=p)throw new o(l)}for(;t?p>=0:m>p;p+=f)p in _&&(u=a(u,_[p],p,d));return u}};t.exports={left:c(!1),right:c(!0)}},f665:function(t,e,a){"use strict";var s=a("23e7"),i=a("c65b"),r=a("2266"),n=a("59ed"),o=a("825a"),l=a("46c4"),c=a("2a62"),u=a("f99f"),d=u("find",TypeError);s({target:"Iterator",proto:!0,real:!0,forced:d},{find:function(t){o(this);try{n(t)}catch(s){c(this,"throw",s)}if(d)return i(d,this,t);var e=l(this),a=0;return r(e,(function(e,s){if(t(e,a++))return s(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},f851:function(t,e,a){"use strict";a.d(e,"e",(function(){return h})),a.d(e,"h",(function(){return v})),a.d(e,"d",(function(){return w})),a.d(e,"c",(function(){return b})),a.d(e,"g",(function(){return y})),a.d(e,"f",(function(){return g})),a.d(e,"a",(function(){return I})),a.d(e,"b",(function(){return k}));a("d9e2"),a("13d5"),a("e9f5"),a("910d"),a("f665"),a("9485");var s=a("b775");const i=!1,r="wifi_admin_profit_rules",n="wifi_admin_profit_bills",o="wifi_admin_withdraw_list",l={wifi_share:{name:"WiFi分享",user_rate:70,platform_rate:30,status:1},goods_sale:{name:"商品销售",user_rate:10,leader_rate:5,platform_rate:85,status:1},advertisement:{name:"广告点击",user_rate:20,leader_rate:10,platform_rate:70,status:1},updated_at:"2025-07-08 16:30:00"};function c(){try{const t=localStorage.getItem(r);return t?JSON.parse(t):l}catch(t){return console.warn("读取分润规则数据失败，使用默认数据:",t),l}}function u(t){try{localStorage.setItem(r,JSON.stringify(t))}catch(e){console.error("保存分润规则数据失败:",e)}}const d=[{id:101,bill_no:"SB20250708001",type:"wifi_share",amount:.6,share_amount:.42,platform_amount:.18,user_id:1001,user_name:"张三",user_phone:"138****1001",source_id:1,source_type:"wifi",status:1,remark:"WiFi码分享使用收益",settle_time:null,created_at:"2025-07-08 10:15:22"},{id:102,bill_no:"SB20250708002",type:"wifi_share",amount:.4,share_amount:.28,platform_amount:.12,user_id:1002,user_name:"李四",user_phone:"138****1002",source_id:2,source_type:"wifi",status:2,remark:"WiFi码分享使用收益",settle_time:"2025-07-08 16:32:45",created_at:"2025-07-08 09:27:18"},{id:103,bill_no:"SB20250708003",type:"goods_sale",amount:25,share_amount:2.5,platform_amount:22.5,user_id:1003,user_name:"王五",user_phone:"138****1003",source_id:1,source_type:"order",status:1,remark:"商品销售分润",settle_time:null,created_at:"2025-07-08 14:52:36"},{id:104,bill_no:"SB20250708004",type:"advertisement",amount:5.6,share_amount:1.12,platform_amount:4.48,user_id:1001,user_name:"张三",user_phone:"138****1001",source_id:3,source_type:"ad_click",status:2,remark:"广告点击收益",settle_time:"2025-07-08 18:21:05",created_at:"2025-07-08 16:05:43"}];function _(){try{const t=localStorage.getItem(n);return t?JSON.parse(t):d}catch(t){return console.warn("读取账单数据失败，使用默认数据:",t),d}}const m=[{id:1,withdraw_no:"W2025070800001",user_id:1001,user_name:"张三",user_phone:"138****1001",amount:200,status:0,type:1,account_type:"支付宝",account_name:"张三",account_no:"<EMAIL>",bank_name:null,created_at:"2025-07-08 10:12:33",audit_time:null,audit_user:null,audit_remark:null,pay_time:null,pay_remark:null},{id:2,withdraw_no:"W2025070800002",user_id:1002,user_name:"李四",user_phone:"138****1002",amount:500,status:1,type:2,account_type:"银行卡",account_name:"李四",account_no:"6222xxxxxxx",bank_name:"工商银行",created_at:"2025-07-08 09:45:21",audit_time:"2025-07-08 11:23:45",audit_user:"admin",audit_remark:"审核通过",pay_time:null,pay_remark:null},{id:3,withdraw_no:"W2025070800003",user_id:1003,user_name:"王五",user_phone:"138****1003",amount:100,status:2,type:1,account_type:"支付宝",account_name:"王五",account_no:"<EMAIL>",bank_name:null,created_at:"2025-07-07 16:32:18",audit_time:"2025-07-08 09:15:30",audit_user:"admin",audit_remark:"金额不足，请重新提交",pay_time:null,pay_remark:null},{id:4,withdraw_no:"W2025070800004",user_id:1004,user_name:"赵六",user_phone:"138****1004",amount:300,status:3,type:2,account_type:"银行卡",account_name:"赵六",account_no:"6217xxxxxxx",bank_name:"招商银行",created_at:"2025-07-07 14:56:42",audit_time:"2025-07-07 16:28:35",audit_user:"admin",audit_remark:"审核通过",pay_time:"2025-07-08 10:35:12",pay_remark:"打款成功"}];function p(){try{const t=localStorage.getItem(o);return t?JSON.parse(t):m}catch(t){return console.warn("读取提现数据失败，使用默认数据:",t),m}}function f(t){try{localStorage.setItem(o,JSON.stringify(t))}catch(e){console.error("保存提现数据失败:",e)}}function h(){return i?new Promise(t=>{setTimeout(()=>{const e=c();t({code:200,data:e,message:"获取成功"})},200)}):Object(s["a"])({url:"/api/v1/admin/income/rules",method:"get"})}function v(t){return i?new Promise(e=>{setTimeout(()=>{const a=c(),s=Object.assign(a,t,{updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)});u(s),e({code:200,message:"更新成功"})},500)}):Object(s["a"])({url:"/api/v1/admin/income/rules/update",method:"post",data:t})}function w(t){return i?new Promise(e=>{setTimeout(()=>{const a=_(),s=parseInt(t.page)||1,i=parseInt(t.limit)||10;let r=[...a];t.keyword&&(r=r.filter(e=>e.user_name.includes(t.keyword)||e.user_phone.includes(t.keyword)||e.bill_no.includes(t.keyword))),t.type&&(r=r.filter(e=>e.type===t.type)),void 0!==t.status&&""!==t.status&&(r=r.filter(e=>e.status===parseInt(t.status))),t.start_date&&t.end_date&&(r=r.filter(e=>{const a=new Date(e.created_at),s=new Date(t.start_date),i=new Date(t.end_date);return i.setHours(23,59,59,999),a>=s&&a<=i}));const n=r.length,o=(s-1)*i,l=o+i,c=r.slice(o,l),u=r.reduce((t,e)=>t+e.amount,0),d=r.reduce((t,e)=>t+e.share_amount,0),m=r.reduce((t,e)=>t+e.platform_amount,0);e({code:200,data:{list:c,total:n,page:s,limit:i,stats:{total_amount:u.toFixed(2),total_share_amount:d.toFixed(2),total_platform_amount:m.toFixed(2)}},message:"获取成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/income/bill/list",method:"get",params:t})}function b(t){return i?new Promise((e,a)=>{setTimeout(()=>{try{const s=_(),i=s.find(e=>e.id===parseInt(t));if(!i)return void a(new Error("账单不存在"));const r={code:200,data:{detail:{id:i.id,amount:i.amount,source_type:"wifi_share"===i.type?1:"goods_sale"===i.type?2:3,source_id:i.bill_no,created_at:i.created_at,remark:i.remark},user_info:{id:i.user_id,nickname:i.user_name,phone:i.user_phone,avatar:"",is_leader:Math.random()>.5?1:0,balance:(1e3*Math.random()).toFixed(2)},source_info:"wifi_share"===i.type?{title:"WiFi示例",name:"Test_WiFi_"+i.id,merchant_name:"示例商户",use_count:Math.floor(100*Math.random())}:"goods_sale"===i.type?{order_no:i.bill_no,total_amount:i.amount,status:1,created_at:i.created_at}:{title:"广告示例",space_name:"首页广告位",click_count:Math.floor(1e3*Math.random()),view_count:Math.floor(5e3*Math.random())},profit_detail:[{role:"分享者",rate:70,amount:i.share_amount,user_info:`${i.user_name}(${i.user_phone})`},{role:"平台",rate:30,amount:i.platform_amount,user_info:"系统平台"}]},message:"success"};e(r)}catch(s){a(s)}},200)}):Object(s["a"])({url:"/api/v1/admin/income/bill/detail/"+t,method:"get"})}function y(t){return i?new Promise(e=>{setTimeout(()=>{const a=p(),s=parseInt(t.page)||1,i=parseInt(t.limit)||10;let r=[...a];t.keyword&&(r=r.filter(e=>e.user_name.includes(t.keyword)||e.user_phone.includes(t.keyword)||e.withdraw_no.includes(t.keyword))),void 0!==t.status&&""!==t.status&&(r=r.filter(e=>e.status===parseInt(t.status))),t.start_date&&t.end_date&&(r=r.filter(e=>{const a=new Date(e.created_at),s=new Date(t.start_date),i=new Date(t.end_date);return i.setHours(23,59,59,999),a>=s&&a<=i}));const n=r.length,o=(s-1)*i,l=o+i,c=r.slice(o,l);e({code:200,data:{list:c,total:n,page:s,limit:i},message:"获取成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/withdraw/list",method:"get",params:t})}function g(t){return i?new Promise((e,a)=>{setTimeout(()=>{const s=p(),i=s.find(e=>e.id===parseInt(t));if(i){const t={detail:{...i,card_holder:i.account_name,bank_name:i.bank_name||"支付宝",card_number:i.account_no,bank_branch:"工商银行"===i.bank_name?"北京市朝阳区支行":null,transfer_time:i.pay_time,transaction_id:3===i.status?"TRX202412270001":null,remark:i.audit_remark||i.pay_remark||null},user_info:{id:i.user_id,nickname:i.user_name,phone:i.user_phone,avatar:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",balance:1580.5,is_leader:101===i.user_id?1:0}};e({code:200,data:t,message:"获取成功"})}else a(new Error("提现申请不存在"))},200)}):Object(s["a"])({url:"/api/v1/admin/withdraw/detail/"+t,method:"get"})}function I(t,e){return i?new Promise((a,s)=>{setTimeout(()=>{const i=p(),r=i.findIndex(e=>e.id===parseInt(t));r>-1?(i[r].status=e.status,i[r].audit_time=(new Date).toISOString().replace("T"," ").slice(0,19),i[r].audit_user="admin",i[r].audit_remark=e.remark||(1===e.status?"审核通过":"审核拒绝"),f(i),a({code:200,message:"审核成功"})):s(new Error("提现申请不存在"))},500)}):Object(s["a"])({url:"/api/v1/admin/withdraw/audit/"+t,method:"post",data:e})}function k(t,e){return i?new Promise((a,s)=>{setTimeout(()=>{const i=p(),r=i.findIndex(e=>e.id===parseInt(t));r>-1?(i[r].status=3,i[r].pay_time=(new Date).toISOString().replace("T"," ").slice(0,19),i[r].pay_remark=e.remark||"已打款",i[r].transaction_id=e.transaction_id,f(i),a({code:200,message:"打款成功"})):s(new Error("提现申请不存在"))},500)}):Object(s["a"])({url:"/api/v1/admin/withdraw/confirm/"+t,method:"post",data:e})}}}]);