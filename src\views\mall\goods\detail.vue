<template>
  <div class="app-container">
    <div v-loading="loading" class="goods-detail">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>商品详情</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
        </div>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="goods-cover">
              <el-image
                style="width: 100%; height: 300px"
                :src="formatImageUrl(detail.cover)"
                fit="contain">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </div>
            <div class="goods-images" v-if="imageList.length > 0">
              <el-carousel :interval="4000" type="card" height="200px">
                <el-carousel-item v-for="(item, index) in imageList" :key="index">
                  <el-image :src="formatImageUrl(item)" style="width: 100%; height: 100%" fit="contain"></el-image>
                </el-carousel-item>
              </el-carousel>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="goods-info">
              <h2 class="goods-title">{{ detail.title }}</h2>
              <div class="goods-price">
                <span class="price-label">售价：</span>
                <span class="price-value">¥ {{ detail.price }}</span>
                <span class="original-price" v-if="detail.original_price && detail.original_price > detail.price">¥ {{ detail.original_price }}</span>
              </div>
              <div class="goods-item">
                <span class="label">分类：</span>
                <span class="value">{{ getCategoryName(detail.category_id) }}</span>
              </div>
              <div class="goods-item">
                <span class="label">库存：</span>
                <span class="value">{{ detail.stock }} 件</span>
              </div>
              <div class="goods-item">
                <span class="label">销量：</span>
                <span class="value">{{ detail.sales }} 件</span>
              </div>
              <div class="goods-item">
                <span class="label">推荐：</span>
                <el-tag v-if="detail.is_recommend === 1" type="success">是</el-tag>
                <el-tag v-else type="info">否</el-tag>
              </div>
              <div class="goods-item">
                <span class="label">热门：</span>
                <el-tag v-if="detail.is_hot === 1" type="success">是</el-tag>
                <el-tag v-else type="info">否</el-tag>
              </div>
              <div class="goods-item">
                <span class="label">新品：</span>
                <el-tag v-if="detail.is_new === 1" type="success">是</el-tag>
                <el-tag v-else type="info">否</el-tag>
              </div>
              <div class="goods-item">
                <span class="label">状态：</span>
                <el-tag :type="detail.status === 1 ? 'success' : 'info'">
                  {{ detail.status === 1 ? '上架' : '下架' }}
                </el-tag>
              </div>
              <div class="goods-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ detail.created_at }}</span>
              </div>
              <div class="goods-item">
                <span class="label">更新时间：</span>
                <span class="value">{{ detail.updated_at }}</span>
              </div>
              <div class="goods-actions">
                <el-button type="primary" @click="handleEdit">编辑</el-button>
                <el-button v-if="detail.status === 1" type="warning" @click="handleStatusChange(0)">下架</el-button>
                <el-button v-else type="success" @click="handleStatusChange(1)">上架</el-button>
                <el-button type="danger" @click="handleDelete">删除</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card description-card">
        <div slot="header" class="clearfix">
          <span>商品描述</span>
        </div>
        <div class="goods-description">
          {{ detail.description || '暂无描述' }}
        </div>
      </el-card>

      <el-card class="box-card details-card">
        <div slot="header" class="clearfix">
          <span>商品详情</span>
        </div>
        <div class="goods-details" v-html="detail.details"></div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getGoodsDetail, updateGoodsStatus, deleteGoods, formatImageUrl } from '@/api/goods'

export default {
  name: 'GoodsDetail',
  data () {
    return {
      loading: true,
      detail: {},
      imageList: [],
      goodsId: null,
      categoryOptions: [
        { label: '数码产品', value: 1 },
        { label: '家居用品', value: 2 },
        { label: '美妆护肤', value: 3 },
        { label: '食品饮料', value: 4 }
      ]
    }
  },
  created () {
    this.goodsId = parseInt(this.$route.params.id)
    this.fetchData()
  },
  methods: {
    formatImageUrl,
    fetchData () {
      this.loading = true
      getGoodsDetail(this.goodsId).then(response => {
        this.detail = response.data

        // 处理商品图片
        if (this.detail.images) {
          try {
            this.imageList = typeof this.detail.images === 'string' ? JSON.parse(this.detail.images) : this.detail.images
          } catch (e) {
            console.error('解析商品图片失败', e)
            this.imageList = []
          }
        }

        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    getCategoryName (categoryId) {
      const category = this.categoryOptions.find(item => item.value === categoryId)
      return category ? category.label : '未分类'
    },
    handleEdit () {
      this.$router.push(`/mall/goods/edit/${this.goodsId}`)
    },
    handleStatusChange (status) {
      this.$confirm(`确认要${status === 1 ? '上架' : '下架'}该商品吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateGoodsStatus(this.goodsId, { status }).then(response => {
          this.$message.success('状态更新成功')
          this.detail.status = status
        }).catch(() => {
          this.$message.error('状态更新失败')
        })
      }).catch(() => {
        // 取消操作
      })
    },
    handleDelete () {
      this.$confirm('确认要删除这个商品吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log(`正在调用删除API，商品ID: ${this.goodsId}`)
        deleteGoods(this.goodsId).then(response => {
          console.log('删除商品成功，响应:', response)
          this.$message.success('删除成功')
          this.goBack()
        }).catch((error) => {
          console.error('删除商品失败:', error)
          if (error.response && error.response.status === 404) {
            this.$message.error('商品不存在或已被删除')
            this.goBack()
          } else {
            this.$message.error(`删除失败: ${error.message || '未知错误'}`)
          }
        })
      }).catch(() => {
        // 取消删除
        console.log('用户取消了删除操作')
      })
    },
    goBack () {
      this.$router.push('/mall/goods')
    }
  }
}
</script>

<style scoped>
.goods-detail {
  margin-bottom: 20px;
}
.box-card {
  margin-bottom: 20px;
}
.goods-cover {
  margin-bottom: 20px;
  text-align: center;
}
.goods-title {
  font-size: 24px;
  margin-top: 0;
  margin-bottom: 20px;
}
.goods-price {
  margin-bottom: 20px;
}
.price-label {
  font-size: 16px;
}
.price-value {
  font-size: 24px;
  color: #f56c6c;
  font-weight: bold;
  margin-right: 10px;
}
.original-price {
  font-size: 16px;
  color: #909399;
  text-decoration: line-through;
}
.goods-item {
  margin-bottom: 15px;
  font-size: 14px;
}
.label {
  font-weight: bold;
  margin-right: 10px;
}
.value {
  color: #606266;
}
.goods-actions {
  margin-top: 30px;
}
.goods-description {
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
}
.goods-details {
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
}
</style>
