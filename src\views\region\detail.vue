<template>
  <div class="region-detail">
    <div class="detail-header">
      <el-button @click="goBack" icon="el-icon-arrow-left">返回</el-button>
      <h2>地区详情</h2>
    </div>

    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>基本信息</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleEdit">编辑</el-button>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="detail-item">
            <label>地区ID：</label>
            <span>{{ regionInfo.id }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label>地区名称：</label>
            <span>{{ regionInfo.name }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <div class="detail-item">
            <label>地区代码：</label>
            <span>{{ regionInfo.code }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label>父级地区：</label>
            <span>{{ regionInfo.parent_name || '无' }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <div class="detail-item">
            <label>级别：</label>
            <el-tag :type="regionInfo.level === 1 ? 'primary' : regionInfo.level === 2 ? 'success' : 'info'">
              {{ getLevelText(regionInfo.level) }}
            </el-tag>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label>状态：</label>
            <el-tag :type="regionInfo.status === 1 ? 'success' : 'danger'">
              {{ regionInfo.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </div>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <div class="detail-item">
            <label>创建时间：</label>
            <span>{{ regionInfo.created_at }}</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="detail-item">
            <label>更新时间：</label>
            <span>{{ regionInfo.updated_at }}</span>
          </div>
        </el-col>
      </el-row>

      <el-row v-if="regionInfo.description">
        <el-col :span="24">
          <div class="detail-item">
            <label>描述：</label>
            <p>{{ regionInfo.description }}</p>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 子地区列表 -->
    <el-card class="box-card" style="margin-top: 20px;" v-if="subRegions.length > 0">
      <div slot="header" class="clearfix">
        <span>子地区列表</span>
      </div>
      
      <el-table :data="subRegions" border style="width: 100%">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="地区名称"></el-table-column>
        <el-table-column prop="code" label="地区代码"></el-table-column>
        <el-table-column label="级别" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.level === 1 ? 'primary' : scope.row.level === 2 ? 'success' : 'info'">
              {{ getLevelText(scope.row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewSubRegion(scope.row)">查看</el-button>
            <el-button type="text" size="small" @click="editSubRegion(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getRegionDetail } from '@/api/region'

export default {
  name: 'RegionDetail',
  data() {
    return {
      regionInfo: {},
      subRegions: []
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      const id = this.$route.params.id
      getRegionDetail(id).then(response => {
        this.regionInfo = response.data.region
        this.subRegions = response.data.subRegions || []
      }).catch(error => {
        console.error('获取地区详情失败:', error)
        this.$message.error('获取地区详情失败')
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    handleEdit() {
      this.$router.push(`/region/edit/${this.regionInfo.id}`)
    },
    viewSubRegion(region) {
      this.$router.push(`/region/detail/${region.id}`)
    },
    editSubRegion(region) {
      this.$router.push(`/region/edit/${region.id}`)
    },
    getLevelText(level) {
      const levelMap = {
        1: '省级',
        2: '市级',
        3: '区县级'
      }
      return levelMap[level] || '未知'
    }
  }
}
</script>

<style scoped>
.region-detail {
  padding: 20px;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.detail-header h2 {
  margin: 0 0 0 10px;
}

.box-card {
  margin-bottom: 20px;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.detail-item label {
  font-weight: bold;
  margin-right: 10px;
  min-width: 80px;
}

.detail-item p {
  margin: 0;
  line-height: 1.5;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
