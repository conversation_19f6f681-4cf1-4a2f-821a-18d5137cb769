import request from '@/utils/request'

// 获取广告统计概览
export function getAdStats() {
  return request({
    url: '/api/v1/admin/advertisement/stats',
    method: 'get'
  })
}

// 获取广告趋势数据
export function getAdTrend(params) {
  return request({
    url: '/api/v1/admin/advertisement/trend',
    method: 'get',
    params
  })
}

// 获取广告位点击排行
export function getAdSpaceRank(params) {
  return request({
    url: '/api/v1/admin/advertisement/space-rank',
    method: 'get',
    params
  })
}

// 获取广告内容效果排行
export function getAdContentRank(params) {
  return request({
    url: '/api/v1/admin/advertisement/content-rank',
    method: 'get',
    params
  })
}

// 获取广告位详细统计
export function getAdSpaceStats() {
  return request({
    url: '/api/v1/admin/advertisement/space-stats',
    method: 'get'
  })
}

// 获取广告内容详细统计
export function getAdContentStats() {
  return request({
    url: '/api/v1/admin/advertisement/content-stats',
    method: 'get'
  })
}

// 获取广告点击记录
export function getAdClickLogs(params) {
  return request({
    url: '/api/v1/admin/advertisement/click-logs',
    method: 'get',
    params
  })
}
