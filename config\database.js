const mysql = require('mysql2');

// 创建数据库连接池
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'wo587129955',
  database: process.env.DB_NAME || 'mall',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
});

// 使用promise包装以使用async/await
const promisePool = pool.promise();

module.exports = {
  pool: promisePool,
  
  // 执行单条SQL语句
  query: async (sql, params) => {
    try {
      const [rows] = await promisePool.execute(sql, params);
      return rows;
    } catch (error) {
      console.error('数据库查询错误:', error.message);
      throw error;
    }
  },

  // 获取单条记录
  getOne: async (sql, params) => {
    try {
      const [rows] = await promisePool.execute(sql, params);
      return rows[0];
    } catch (error) {
      console.error('数据库查询错误:', error.message);
      throw error;
    }
  },
  
  // 事务处理
  transaction: async (callback) => {
    const connection = await promisePool.getConnection();
    await connection.beginTransaction();
    
    try {
      const result = await callback(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }
}; 