<template>
  <div class="team-members">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator-class="el-icon-arrow-right" class="breadcrumb">
      <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/team/list' }">团队管理</el-breadcrumb-item>
      <el-breadcrumb-item>团队成员</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 团队基本信息 -->
    <el-card class="team-info-card">
      <div class="team-info">
        <div class="info-item">
          <span class="label">团队名称：</span>
          <span class="value">{{ teamInfo.name }}</span>
        </div>
        <div class="info-item">
          <span class="label">团长：</span>
          <span class="value">{{ teamInfo.leader_name }}</span>
        </div>
        <div class="info-item">
          <span class="label">成员数量：</span>
          <span class="value">{{ teamInfo.member_count }}</span>
        </div>
        <div class="info-item">
          <span class="label">创建时间：</span>
          <span class="value">{{ formatDate(teamInfo.created_at) }}</span>
        </div>
      </div>
    </el-card>

    <!-- 成员列表 -->
    <el-card class="member-list-card">
      <div slot="header" class="card-header">
        <span>成员列表</span>
        <el-button
          type="primary"
          size="small"
          @click="showAddMemberDialog"
          icon="el-icon-plus"
        >
          添加成员
        </el-button>
      </div>

      <!-- 成员表格 -->
      <el-table
        :data="memberList"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column
          prop="user_id"
          label="用户ID"
          width="80"
        />
        <el-table-column
          label="头像"
          width="80"
        >
          <template slot-scope="scope">
            <el-avatar
              :size="40"
              :src="scope.row.avatar || '/img/default-avatar.png'"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="nickname"
          label="昵称"
          min-width="120"
        />
        <el-table-column
          prop="phone"
          label="手机号"
          width="120"
        />
        <el-table-column
          label="角色"
          width="100"
        >
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.role === 'leader' ? 'danger' : 'info'"
              size="small"
            >
              {{ scope.row.role === 'leader' ? '团长' : '成员' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="加入时间"
          width="160"
        >
          <template slot-scope="scope">
            {{ formatDate(scope.row.joined_at) }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="100"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.role !== 'leader'"
              type="text"
              size="small"
              @click="removeMember(scope.row)"
            >
              移除
            </el-button>
            <span v-else class="no-action">-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.page"
        :limit.sync="queryParams.limit"
        @pagination="getMembers"
      />
    </el-card>

    <!-- 添加成员对话框 -->
    <el-dialog
      title="添加团队成员"
      :visible.sync="addMemberDialog"
      width="500px"
    >
      <el-form
        ref="addForm"
        :model="addForm"
        :rules="addRules"
        label-width="100px"
      >
        <el-form-item label="选择用户" prop="user_id">
          <el-select
            v-model="addForm.user_id"
            placeholder="请选择要添加的用户"
            filterable
            remote
            :remote-method="searchUsers"
            :loading="searchLoading"
            style="width: 100%"
          >
            <el-option
              v-for="user in availableUsers"
              :key="user.id"
              :label="`${user.nickname} (${user.phone})`"
              :value="user.id"
            >
              <div class="user-option">
                <el-avatar
                  :size="30"
                  :src="user.avatar || '/img/default-avatar.png'"
                  style="margin-right: 10px"
                />
                <div>
                  <div>{{ user.nickname }}</div>
                  <div style="font-size: 12px; color: #999">{{ user.phone }}</div>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="addMemberDialog = false">取消</el-button>
        <el-button type="primary" @click="addMember">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTeamDetail, getTeamMembers, addTeamMember, removeTeamMember } from '@/api/team'
import { getUserList } from '@/api/user-manage'
import { formatDate } from '@/utils/date'
import Pagination from '@/components/Pagination'

export default {
  name: 'TeamMembers',
  components: {
    Pagination
  },
  data() {
    return {
      teamId: null,
      teamInfo: {},
      loading: false,
      memberList: [],
      total: 0,
      queryParams: {
        page: 1,
        limit: 10
      },
      addMemberDialog: false,
      addForm: {
        user_id: null
      },
      addRules: {
        user_id: [
          { required: true, message: '请选择用户', trigger: 'change' }
        ]
      },
      availableUsers: [],
      searchLoading: false
    }
  },
  created() {
    this.teamId = this.$route.params.id
    this.getTeamInfo()
    this.getMembers()
  },
  methods: {
    formatDate,
    
    // 获取团队信息
    async getTeamInfo() {
      try {
        const res = await getTeamDetail(this.teamId)
        this.teamInfo = res.data
      } catch (error) {
        this.$message.error('获取团队信息失败')
      }
    },
    
    // 获取成员列表
    async getMembers() {
      this.loading = true
      try {
        const res = await getTeamMembers(this.teamId, this.queryParams)
        this.memberList = res.data.list || []
        this.total = (res.data.pagination && res.data.pagination.total) || res.data.total || 0
      } catch (error) {
        console.error('获取成员列表失败:', error)
        this.memberList = []
        this.total = 0
        this.$message.error('获取成员列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 显示添加成员对话框
    showAddMemberDialog() {
      this.addMemberDialog = true
      this.addForm.user_id = null
      this.searchUsers('')
    },
    
    // 搜索用户
    async searchUsers(query) {
      this.searchLoading = true
      try {
        const res = await getUserList({
          page: 1,
          limit: 20,
          keyword: query
        })
        // 过滤掉已经在团队中的用户
        const memberIds = this.memberList.map(m => m.user_id)
        this.availableUsers = res.data.list.filter(u => !memberIds.includes(u.id))
      } catch (error) {
        this.$message.error('搜索用户失败')
      } finally {
        this.searchLoading = false
      }
    },
    
    // 添加成员
    async addMember() {
      this.$refs.addForm.validate(async valid => {
        if (!valid) return
        
        try {
          await addTeamMember(this.teamId, this.addForm.user_id)
          this.$message.success('添加成员成功')
          this.addMemberDialog = false
          this.getMembers()
          this.getTeamInfo()
        } catch (error) {
          this.$message.error(error.message || '添加成员失败')
        }
      })
    },
    
    // 移除成员
    removeMember(member) {
      this.$confirm(`确定要将 ${member.nickname} 移出团队吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await removeTeamMember(this.teamId, member.user_id)
          this.$message.success('移除成员成功')
          this.getMembers()
          this.getTeamInfo()
        } catch (error) {
          this.$message.error(error.message || '移除成员失败')
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.team-members {
  padding: 20px;
  
  .breadcrumb {
    margin-bottom: 20px;
  }
  
  .team-info-card {
    margin-bottom: 20px;
    
    .team-info {
      display: flex;
      flex-wrap: wrap;
      
      .info-item {
        width: 25%;
        margin-bottom: 10px;
        
        .label {
          color: #999;
          margin-right: 5px;
        }
        
        .value {
          color: #333;
          font-weight: 500;
        }
      }
    }
  }
  
  .member-list-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .no-action {
      color: #999;
    }
  }
  
  .user-option {
    display: flex;
    align-items: center;
  }
}
</style> 