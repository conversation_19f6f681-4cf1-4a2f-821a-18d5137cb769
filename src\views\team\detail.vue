<template>
  <div class="team-detail-container">
    <!-- 基本信息 -->
    <el-card class="info-card">
      <div slot="header" class="card-header">
        <span>团队基本信息</span>
        <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
      </div>
      
      <el-descriptions :column="2" border v-loading="loading">
        <el-descriptions-item label="团队ID">{{ teamInfo.id }}</el-descriptions-item>
        <el-descriptions-item label="团队名称">{{ teamInfo.name }}</el-descriptions-item>
        <el-descriptions-item label="团长">
          <div class="user-info" v-if="teamInfo.leader_name">
            <el-avatar :size="30" :src="teamInfo.leader_avatar || '/img/default-avatar.png'"></el-avatar>
            <span class="user-name">{{ teamInfo.leader_name }}</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="成员数量">
          <el-tag type="info">{{ teamInfo.member_count || 0 }} 人</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="团队状态">
          <el-tag :type="teamInfo.status === 1 ? 'success' : 'danger'">
            {{ teamInfo.status === 1 ? '正常' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(teamInfo.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="团队简介" :span="2">
          {{ teamInfo.description || '暂无简介' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 收益统计 -->
    <el-card class="stats-card">
      <div slot="header">收益统计</div>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">¥{{ (teamStats.total_revenue || 0).toFixed(2) }}</div>
            <div class="stat-label">总收益</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">¥{{ (teamStats.month_revenue || 0).toFixed(2) }}</div>
            <div class="stat-label">本月收益</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ teamStats.total_orders || 0 }}</div>
            <div class="stat-label">总订单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ teamStats.month_orders || 0 }}</div>
            <div class="stat-label">本月订单</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 成员列表 -->
    <el-card class="members-card">
      <div slot="header" class="card-header">
        <span>团队成员</span>
        <el-button type="primary" size="small" @click="handleAddMember">添加成员</el-button>
      </div>
      
      <el-table :data="memberList" border style="width: 100%">
        <el-table-column prop="id" label="用户ID" width="80" align="center"></el-table-column>
        <el-table-column prop="nickname" label="成员昵称" min-width="150">
          <template slot-scope="scope">
            <div class="user-info">
              <el-avatar :size="30" :src="scope.row.avatar || '/img/default-avatar.png'"></el-avatar>
              <span class="user-name">{{ scope.row.nickname || '未知' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" width="120"></el-table-column>
        <el-table-column prop="contribution" label="贡献收益" width="120" align="right">
          <template slot-scope="scope">
            <span class="revenue">¥{{ (scope.row.contribution || 0).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="joined_at" label="加入时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.joined_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleRemoveMember(scope.row)" style="color: #F56C6C;">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50]"
        :page-size="pagination.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import { getTeamDetail, getTeamStats, getTeamMembers, removeMember } from '@/api/team'
import { formatDate } from '@/utils/date'

export default {
  name: 'TeamDetail',
  data() {
    return {
      loading: false,
      teamId: null,
      teamInfo: {},
      teamStats: {},
      memberList: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      }
    }
  },
  created() {
    this.teamId = this.$route.params.id
    this.fetchData()
  },
  methods: {
    // 获取团队详情
    async fetchData() {
      this.loading = true
      try {
        // 获取基本信息
        const detailRes = await getTeamDetail(this.teamId)
        if (detailRes.status === 'success') {
          this.teamInfo = detailRes.data
        }

        // 获取统计数据
        const statsRes = await getTeamStats(this.teamId)
        if (statsRes.status === 'success') {
          this.teamStats = statsRes.data
        }
        
        // 获取成员列表
        this.fetchMembers()
      } catch (error) {
        this.$message.error('获取团队信息失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取成员列表
    async fetchMembers() {
      try {
        const params = {
          team_id: this.teamId,
          page: this.pagination.page,
          limit: this.pagination.limit
        }
        const res = await getTeamMembers(params)
        if (res.status === 'success') {
          this.memberList = res.data.list || []
          this.pagination.total = res.data.total || 0
        }
      } catch (error) {
        this.$message.error('获取成员列表失败')
      }
    },
    
    // 编辑团队
    handleEdit() {
      this.$router.push(`/team/edit/${this.teamId}`)
    },
    
    // 添加成员
    handleAddMember() {
      this.$router.push(`/team/members/${this.teamId}/add`)
    },
    
    // 移除成员
    handleRemoveMember(member) {
      this.$confirm(`确定要将 ${member.nickname} 移出团队吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await removeMember(this.teamId, member.id)
          if (res.code === 0) {
            this.$message.success('移除成功')
            this.fetchMembers()
            // 更新成员数量
            this.teamInfo.member_count = (this.teamInfo.member_count || 1) - 1
          }
        } catch (error) {
          this.$message.error('移除失败')
        }
      }).catch(() => {})
    },
    
    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.limit = val
      this.fetchMembers()
    },
    
    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.page = val
      this.fetchMembers()
    },
    
    // 格式化日期
    formatDate
  }
}
</script>

<style lang="scss" scoped>
.team-detail-container {
  padding: 20px;
  
  .el-card {
    margin-bottom: 20px;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .user-info {
    display: flex;
    align-items: center;
    
    .user-name {
      margin-left: 10px;
    }
  }
  
  .stats-card {
    .stat-item {
      text-align: center;
      padding: 20px 0;
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 10px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
  
  .revenue {
    color: #F56C6C;
    font-weight: bold;
  }
  
  .el-pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style> 