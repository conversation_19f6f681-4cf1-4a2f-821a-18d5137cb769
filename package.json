{"name": "wifi-share-admin", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "serve:dev": "vue-cli-service serve --mode development", "serve:staging": "vue-cli-service serve --mode staging", "serve:prod": "vue-cli-service serve --mode production", "build": "vue-cli-service build", "build:staging": "vue-cli-service build --mode staging", "build:prod": "vue-cli-service build --mode production", "preview": "npm run build:prod && serve -s dist", "start": "node server.js", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.1", "connect-history-api-fallback": "^2.0.0", "core-js": "^3.6.5", "echarts": "^5.1.2", "element-ui": "^2.15.6", "express": "^4.18.2", "js-cookie": "^3.0.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^8.2.0", "qrcode": "^1.5.4", "script-ext-html-webpack-plugin": "^2.1.5", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "sass": "^1.26.5", "sass-loader": "^8.0.2", "serve": "^14.2.4", "vue-template-compiler": "^2.6.11"}}