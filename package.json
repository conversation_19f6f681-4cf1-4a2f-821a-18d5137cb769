{"name": "wifi-share-server", "version": "1.0.0", "description": "WiFi共享商业系统后端服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "cross-env NODE_ENV=development nodemon app.js", "test": "cross-env NODE_ENV=test jest", "prod": "cross-env NODE_ENV=production node app.js", "lint": "eslint .", "pm2": "pm2 start ecosystem.config.js --env production"}, "keywords": ["wifi", "share", "business", "api"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.11.0", "bcrypt": "^5.1.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "cos-nodejs-sdk-v5": "^2.12.6", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.2", "node-cron": "^3.0.3", "qrcode": "^1.5.3", "uuid": "^11.1.0", "wechatpay-node-v3": "^2.2.1", "winston": "^3.9.0"}, "devDependencies": {"cross-env": "^7.0.3", "eslint": "^8.44.0", "jest": "^29.6.1", "nodemon": "^3.0.1", "supertest": "^6.3.3"}}