(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2ee25716"],{"2f90":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{staticClass:"filter-container"},[e("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"订单号/收货人",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.keyword,callback:function(e){t.$set(t.listQuery,"keyword",e)},expression:"listQuery.keyword"}}),e("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"订单状态",clearable:""},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},t._l(t.statusOptions,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),e("el-date-picker",{staticClass:"filter-item",staticStyle:{width:"240px"},attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},on:{change:t.handleDateRangeChange},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}}),e("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleFilter}},[t._v("搜索")])],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{data:t.list,"element-loading-text":"正在加载...",border:"",fit:"","highlight-current-row":""}},[e("el-table-column",{attrs:{label:"订单号",prop:"order_no",align:"center","min-width":"180"}}),e("el-table-column",{attrs:{label:"用户",align:"center",width:"150"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("div",[t._v(t._s(a.user_nickname))])]}}])}),e("el-table-column",{attrs:{label:"收货人",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("div",[t._v(t._s(a.receiver_name))]),e("div",[t._v(t._s(a.receiver_phone))])]}}])}),e("el-table-column",{attrs:{label:"订单金额",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("span",[t._v(t._s(a.total_amount)+" 元")])]}}])}),e("el-table-column",{attrs:{label:"支付方式",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[1===a.payment_method?e("el-tag",{attrs:{type:"primary"}},[t._v("微信支付")]):2===a.payment_method?e("el-tag",{attrs:{type:"success"}},[t._v("余额支付")]):e("el-tag",{attrs:{type:"info"}},[t._v("其他")])]}}])}),e("el-table-column",{attrs:{label:"订单状态",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("el-tag",{attrs:{type:t.getOrderStatusType(a.status)}},[t._v(t._s(t.getOrderStatusText(a.status)))])]}}])}),e("el-table-column",{attrs:{label:"创建时间",prop:"created_at",align:"center",width:"160"}}),e("el-table-column",{attrs:{label:"操作",align:"center",width:"200","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.handleView(a)}}},[t._v("查看")]),1===a.status?e("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(e){return t.handleShip(a)}}},[t._v("发货")]):t._e(),0===a.status?e("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(e){return t.handleCancel(a)}}},[t._v("取消")]):t._e()]}}])})],1),e("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}}),e("el-dialog",{attrs:{title:"订单发货",visible:t.shipDialogVisible,width:"500px"},on:{"update:visible":function(e){t.shipDialogVisible=e}}},[e("el-form",{ref:"shipForm",attrs:{model:t.shipForm,rules:t.shipRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"物流公司",prop:"logistics_company"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择物流公司"},model:{value:t.shipForm.logistics_company,callback:function(e){t.$set(t.shipForm,"logistics_company",e)},expression:"shipForm.logistics_company"}},t._l(t.logisticsOptions,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),e("el-form-item",{attrs:{label:"物流单号",prop:"logistics_no"}},[e("el-input",{attrs:{placeholder:"请输入物流单号"},model:{value:t.shipForm.logistics_no,callback:function(e){t.$set(t.shipForm,"logistics_no",e)},expression:"shipForm.logistics_no"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.shipDialogVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.confirmShip}},[t._v("确定")])],1)],1)],1)},l=[],s=(a("14d9"),a("f8b7")),n=a("333d"),r={name:"OrderList",components:{Pagination:n["a"]},data(){return{list:null,total:0,listLoading:!0,listQuery:{page:1,limit:10,keyword:void 0,status:void 0,start_date:void 0,end_date:void 0},dateRange:null,statusOptions:[{label:"待支付",value:0},{label:"待发货",value:1},{label:"待收货",value:2},{label:"已完成",value:3},{label:"已取消",value:4}],logisticsOptions:[{label:"顺丰速运",value:"SF"},{label:"中通快递",value:"ZTO"},{label:"圆通速递",value:"YTO"},{label:"申通快递",value:"STO"},{label:"韵达快递",value:"YD"},{label:"天天快递",value:"TTKD"},{label:"百世快递",value:"HTKY"},{label:"邮政快递包裹",value:"YZPY"},{label:"EMS",value:"EMS"}],shipDialogVisible:!1,shipForm:{order_id:null,logistics_company:"",logistics_no:""},shipRules:{logistics_company:[{required:!0,message:"请选择物流公司",trigger:"change"}],logistics_no:[{required:!0,message:"请输入物流单号",trigger:"blur"},{min:5,message:"物流单号长度不能少于5个字符",trigger:"blur"}]}}},created(){this.getList()},methods:{getList(){this.listLoading=!0,Object(s["b"])(this.listQuery).then(t=>{this.list=t.data.list,this.total=t.data.total,this.listLoading=!1}).catch(()=>{this.listLoading=!1})},handleFilter(){this.listQuery.page=1,this.getList()},handleDateRangeChange(t){t?(this.listQuery.start_date=t[0],this.listQuery.end_date=t[1]):(this.listQuery.start_date=void 0,this.listQuery.end_date=void 0)},handleView(t){this.$router.push("/mall/order/detail/"+t.id)},handleShip(t){this.shipForm={order_id:t.id,logistics_company:"",logistics_no:""},this.shipDialogVisible=!0},handleCancel(t){this.$confirm("确认要取消该订单吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(s["d"])(t.id,{status:4}).then(t=>{this.$message.success("订单取消成功"),this.getList()}).catch(()=>{this.$message.error("订单取消失败")})}).catch(()=>{})},confirmShip(){this.$refs.shipForm.validate(t=>{t&&Object(s["c"])(this.shipForm.order_id,{status:2,logistics_company:this.shipForm.logistics_company,logistics_no:this.shipForm.logistics_no}).then(t=>{this.$message.success("发货成功"),this.shipDialogVisible=!1,this.getList()}).catch(()=>{this.$message.error("发货失败")})})},getOrderStatusType(t){const e={0:"info",1:"primary",2:"warning",3:"success",4:"danger"};return e[t]||"info"},getOrderStatusText(t){const e={0:"待支付",1:"待发货",2:"待收货",3:"已完成",4:"已取消"};return e[t]||"未知状态"}}},o=r,u=a("2877"),c=Object(u["a"])(o,i,l,!1,null,null,null);e["default"]=c.exports},"330e":function(t,e,a){"use strict";a("34b0")},"333d":function(t,e,a){"use strict";var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[e("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},l=[],s={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get(){return this.page},set(t){this.$emit("update:page",t)}},pageSize:{get(){return this.limit},set(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange(t){this.$emit("pagination",{page:this.currentPage,limit:t})},handleCurrentChange(t){this.$emit("pagination",{page:t,limit:this.pageSize})}}},n=s,r=(a("330e"),a("2877")),o=Object(r["a"])(n,i,l,!1,null,"11252b03",null);e["a"]=o.exports},"34b0":function(t,e,a){},f8b7:function(t,e,a){"use strict";a.d(e,"b",(function(){return l})),a.d(e,"a",(function(){return s})),a.d(e,"d",(function(){return n})),a.d(e,"c",(function(){return r}));var i=a("b775");function l(t){return Object(i["a"])({url:"/api/v1/admin/order/list",method:"get",params:t})}function s(t){return Object(i["a"])({url:"/api/v1/admin/order/detail/"+t,method:"get"})}function n(t,e){return Object(i["a"])({url:"/api/v1/admin/order/status/"+t,method:"put",data:e})}function r(t,e){return Object(i["a"])({url:"/api/v1/admin/order/logistics/"+t,method:"put",data:e})}}}]);