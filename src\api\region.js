import request from '@/utils/request'

// 获取地区列表
export function getRegionList(params) {
  return request({
    url: '/api/v1/admin/region/list',
    method: 'get',
    params
  })
}

// 获取地区详情
export function getRegionDetail(id) {
  return request({
    url: `/api/v1/admin/region/detail/${id}`,
    method: 'get'
  })
}

// 创建地区
export function createRegion(data) {
  return request({
    url: '/api/v1/admin/region/create',
    method: 'post',
    data
  })
}

// 更新地区
export function updateRegion(id, data) {
  return request({
    url: `/api/v1/admin/region/update/${id}`,
    method: 'put',
    data
  })
}

// 删除地区
export function deleteRegion(id) {
  return request({
    url: `/api/v1/admin/region/delete/${id}`,
    method: 'delete'
  })
}

// 获取地区树形结构
export function getRegionTree() {
  return request({
    url: '/api/v1/admin/region/tree',
    method: 'get'
  })
}
