{"description": "WiFi共享商城小程序", "packOptions": {"ignore": [{"value": ".eslintrc.js", "type": "file"}], "include": []}, "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": false, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "enableEngineNative": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "minifyWXML": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "compileWorklet": false, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": true, "swc": false, "disableSWC": true, "checkInvalidKey": true, "checkSiteMap": true}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "wxd57d522936cb95a1", "projectname": "wifi-share-miniapp", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "3.8.9-1"}, "projectArchitecture": "multiPlatform"}