

-- Active: 1751475729838@@127.0.0.1@3306@mall

-- 创建数据库
CREATE DATABASE IF NOT EXISTS mall DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE mall;

-- 管理员用户表
CREATE TABLE IF NOT EXISTS `admin_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(64) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `role_id` int(11) DEFAULT NULL COMMENT '角色ID',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员用户表';

-- 初始管理员数据，密码为 hh20250701 的MD5加密
-- 先检查是否存在，不存在则插入
INSERT INTO `admin_user` (`username`, `password`, `real_name`, `status`) 
SELECT 'mrx0927', '5eb688175b8e2581bab3e2c657880533', '超级管理员', 1 
FROM dual 
WHERE NOT EXISTS (SELECT 1 FROM `admin_user` WHERE `username` = 'mrx0927');

-- 角色表
CREATE TABLE IF NOT EXISTS `admin_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 权限表
CREATE TABLE IF NOT EXISTS `admin_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `description` varchar(255) DEFAULT NULL COMMENT '权限描述',
  `path` varchar(100) DEFAULT NULL COMMENT '权限路径',
  `parent_id` int(11) DEFAULT '0' COMMENT '父权限ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS `admin_role_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  `permission_id` int(11) NOT NULL COMMENT '权限ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  KEY `permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- ========================================
-- 团队表字段更新脚本
-- ========================================

-- 1. 检查当前团队表结构
DESCRIBE team;

-- 2. 安全添加邀请码字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mall' AND TABLE_NAME = 'team' AND COLUMN_NAME = 'invite_code') = 0,
    'ALTER TABLE team ADD COLUMN invite_code VARCHAR(20) UNIQUE COMMENT "团队邀请码"',
    'SELECT "invite_code字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 安全添加团队等级字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mall' AND TABLE_NAME = 'team' AND COLUMN_NAME = 'level') = 0,
    'ALTER TABLE team ADD COLUMN level INT DEFAULT 1 COMMENT "团队等级"',
    'SELECT "level字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 为现有团队生成邀请码
UPDATE team
SET invite_code = CONCAT('TEAM', LPAD(id, 6, '0'))
WHERE invite_code IS NULL OR invite_code = '';

-- 5. 设置默认等级
UPDATE team SET level = 1 WHERE level IS NULL;

-- 6. 验证结果
SELECT id, name, invite_code, level, member_count, wifi_count, total_profit FROM team;

-- 7. 如果团队表为空，插入测试数据
INSERT INTO team (name, leader_id, member_count, wifi_count, total_profit)
SELECT '我的团队', 1, 1, 6, 0.00
WHERE NOT EXISTS (SELECT 1 FROM team WHERE name = '我的团队');

-- 8. 为新插入的数据生成邀请码
UPDATE team
SET invite_code = CONCAT('TEAM', LPAD(id, 6, '0'))
WHERE invite_code IS NULL OR invite_code = '';

-- 9. 最终验证
SELECT
    id,
    name as '团队名称',
    invite_code as '邀请码',
    level as '等级',
    member_count as '成员数',
    wifi_count as 'WiFi数',
    total_profit as '总收益'
FROM team;


-- 用户表
CREATE TABLE IF NOT EXISTS `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(50) NOT NULL COMMENT '微信openid',
  `unionid` varchar(50) DEFAULT NULL COMMENT '微信unionid',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `gender` tinyint(1) DEFAULT '0' COMMENT '性别：0未知，1男，2女',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '账户余额',
  `team_id` int(11) DEFAULT NULL COMMENT '所属团队ID',
  `parent_id` int(11) DEFAULT NULL COMMENT '上级用户ID',
  `is_leader` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否团长：0否，1是',
  `level` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户等级',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小程序用户表';




-- WiFi表
CREATE TABLE IF NOT EXISTS `wifi` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'WiFiID',
  `title` varchar(100) NOT NULL COMMENT 'WiFi标题',
  `name` varchar(100) NOT NULL COMMENT 'WiFi名称',
  `password` varchar(100) NOT NULL COMMENT 'WiFi密码',
  `merchant_name` varchar(100) DEFAULT NULL COMMENT '商户名称',
  `qrcode` varchar(255) DEFAULT NULL COMMENT '二维码图片URL',
  `use_count` int(11) NOT NULL DEFAULT '0' COMMENT '使用次数',
  `user_id` int(11) NOT NULL COMMENT '创建用户ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='WiFi信息表';

-- 商品分类表
CREATE TABLE IF NOT EXISTS `category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `parent_id` int(11) DEFAULT '0' COMMENT '父分类ID',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

-- 商品表
CREATE TABLE IF NOT EXISTS `goods` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `title` varchar(100) NOT NULL COMMENT '商品标题',
  `description` text COMMENT '商品描述',
  `price` decimal(10,2) NOT NULL COMMENT '售价',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存',
  `sales` int(11) NOT NULL DEFAULT '0' COMMENT '销量',
  `cover` varchar(255) DEFAULT NULL COMMENT '封面图',
  `images` text COMMENT '商品图片，JSON格式',
  `details` text COMMENT '详情',
  `is_recommend` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推荐：0否，1是',
  `is_hot` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否热门：0否，1是',
  `is_new` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否新品：0否，1是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0下架，1上架',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 商品规格表
CREATE TABLE IF NOT EXISTS `goods_specs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '规格ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `name` varchar(50) NOT NULL COMMENT '规格名称',
  `value` varchar(255) NOT NULL COMMENT '规格值',
  `price` decimal(10,2) NOT NULL COMMENT '规格价格',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '规格库存',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品规格表';

-- 购物车表
CREATE TABLE IF NOT EXISTS `cart` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '购物车ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `specs_id` int(11) DEFAULT NULL COMMENT '规格ID',
  `quantity` int(11) NOT NULL DEFAULT '1' COMMENT '数量',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='购物车表';

-- 收货地址表
CREATE TABLE IF NOT EXISTS `address` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `phone` varchar(20) NOT NULL COMMENT '收货人电话',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) NOT NULL COMMENT '区县',
  `detail` varchar(255) NOT NULL COMMENT '详细地址',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认地址：0否，1是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收货地址表';

-- 订单表
CREATE TABLE IF NOT EXISTS `order` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `goods_amount` decimal(10,2) NOT NULL COMMENT '商品总金额',
  `shipping_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '运费',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `address_id` int(11) NOT NULL COMMENT '收货地址ID',
  `receiver_name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) NOT NULL COMMENT '收货人电话',
  `receiver_address` varchar(255) NOT NULL COMMENT '收货地址',
  `payment_method` tinyint(1) NOT NULL DEFAULT '1' COMMENT '支付方式：1微信支付，2余额支付',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `shipping_time` datetime DEFAULT NULL COMMENT '发货时间',
  `completion_time` datetime DEFAULT NULL COMMENT '完成时间',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态：0待支付，1已支付待发货，2已发货待收货，3已完成，4已取消',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 订单商品表
CREATE TABLE IF NOT EXISTS `order_goods` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单商品ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `goods_title` varchar(100) NOT NULL COMMENT '商品标题',
  `goods_cover` varchar(255) DEFAULT NULL COMMENT '商品封面',
  `specs_id` int(11) DEFAULT NULL COMMENT '规格ID',
  `specs_name` varchar(50) DEFAULT NULL COMMENT '规格名称',
  `price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `quantity` int(11) NOT NULL COMMENT '商品数量',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品表';

-- 团队表
CREATE TABLE IF NOT EXISTS `team` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '团队ID',
  `name` varchar(50) NOT NULL COMMENT '团队名称',
  `leader_id` int(11) NOT NULL COMMENT '团长用户ID',
  `member_count` int(11) NOT NULL DEFAULT '0' COMMENT '成员数量',
  `wifi_count` int(11) NOT NULL DEFAULT '0' COMMENT 'WiFi数量',
  `total_profit` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总收益',
  `invite_code` varchar(20) UNIQUE COMMENT '团队邀请码',
  `level` int(11) NOT NULL DEFAULT '1' COMMENT '团队等级',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `invite_code` (`invite_code`),
  KEY `leader_id` (`leader_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队表';

-- 团队申请表
CREATE TABLE IF NOT EXISTS `team_apply` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `user_id` int(11) NOT NULL COMMENT '申请用户ID',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `area` varchar(100) DEFAULT NULL COMMENT '区域',
  `description` text COMMENT '简介',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待审核，1已通过，2已拒绝',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队申请表';

-- 分润规则表
CREATE TABLE IF NOT EXISTS `profit_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `type` varchar(20) NOT NULL UNIQUE COMMENT '业务类型：wifi_share,goods_sale,advertisement',
  `name` varchar(50) NOT NULL COMMENT '规则名称',
  `platform_rate` int(11) NOT NULL DEFAULT '0' COMMENT '平台分润比例（百分比）',
  `leader_rate` int(11) NOT NULL DEFAULT '0' COMMENT '团长分润比例（百分比）',
  `user_rate` int(11) NOT NULL DEFAULT '0' COMMENT '用户分润比例（百分比）',
  `min_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最小分润金额',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分润规则表';

-- 插入默认分润规则
INSERT INTO `profit_rule` (`type`, `name`, `platform_rate`, `leader_rate`, `user_rate`, `min_amount`, `status`) VALUES
('wifi_share', 'WiFi分享', 40, 40, 20, 1.00, 1),
('goods_sale', '商品销售', 50, 30, 20, 5.00, 1),
('advertisement', '广告点击', 60, 30, 10, 0.10, 1)
ON DUPLICATE KEY UPDATE
  `name` = VALUES(`name`),
  `platform_rate` = VALUES(`platform_rate`),
  `leader_rate` = VALUES(`leader_rate`),
  `user_rate` = VALUES(`user_rate`),
  `min_amount` = VALUES(`min_amount`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 分润配置表（系统级配置）
CREATE TABLE IF NOT EXISTS `profit_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(50) NOT NULL UNIQUE COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string,number,json,boolean',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分润配置表';

-- 插入默认分润配置
INSERT INTO `profit_config` (`config_key`, `config_value`, `config_type`, `description`) VALUES
('min_withdraw_amount', '50', 'number', '最小提现金额'),
('withdraw_fee_rate', '1', 'number', '提现手续费比例（百分比）'),
('max_daily_withdraw', '10000', 'number', '每日最大提现金额'),
('profit_settle_delay', '24', 'number', '分润结算延迟时间（小时）'),
('team_level_bonus', '{"1": 0, "2": 5, "3": 10, "4": 15, "5": 20}', 'json', '团队等级奖励比例'),
('auto_settle_enabled', 'true', 'boolean', '是否启用自动结算'),
('profit_audit_enabled', 'false', 'boolean', '是否启用分润审核')
ON DUPLICATE KEY UPDATE
  `config_value` = VALUES(`config_value`),
  `description` = VALUES(`description`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 分润记录表
CREATE TABLE IF NOT EXISTS `profit_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `team_id` int(11) DEFAULT NULL COMMENT '团队ID',
  `amount` decimal(10,2) NOT NULL COMMENT '分润金额',
  `source_type` tinyint(1) NOT NULL COMMENT '来源类型：1WiFi分润，2商品分润，3广告分润',
  `source_id` int(11) NOT NULL COMMENT '来源ID',
  `order_no` varchar(50) DEFAULT NULL COMMENT '关联订单号',
  `profit_type` varchar(20) NOT NULL COMMENT '分润类型：wifi_share,goods_sale,advertisement',
  `role` varchar(20) NOT NULL COMMENT '角色：leader,user,platform',
  `rate` int(11) NOT NULL COMMENT '分润比例（百分比）',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待结算，1已结算，2已取消',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `team_id` (`team_id`),
  KEY `source_type` (`source_type`),
  KEY `profit_type` (`profit_type`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分润记录表';

-- 团队等级分润规则表
CREATE TABLE IF NOT EXISTS `team_level_profit` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `level` int(11) NOT NULL COMMENT '团队等级',
  `level_name` varchar(50) NOT NULL COMMENT '等级名称',
  `profit_type` varchar(20) NOT NULL COMMENT '分润类型',
  `bonus_rate` int(11) NOT NULL DEFAULT '0' COMMENT '额外奖励比例（百分比）',
  `min_members` int(11) NOT NULL DEFAULT '0' COMMENT '最少成员数要求',
  `min_monthly_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最少月度业绩要求',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `level_type` (`level`, `profit_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队等级分润规则表';

-- 插入默认团队等级分润规则
INSERT INTO `team_level_profit` (`level`, `level_name`, `profit_type`, `bonus_rate`, `min_members`, `min_monthly_amount`) VALUES
(1, '初级团队', 'wifi_share', 0, 1, 0.00),
(1, '初级团队', 'goods_sale', 0, 1, 0.00),
(1, '初级团队', 'advertisement', 0, 1, 0.00),
(2, '中级团队', 'wifi_share', 5, 5, 1000.00),
(2, '中级团队', 'goods_sale', 5, 5, 1000.00),
(2, '中级团队', 'advertisement', 5, 5, 1000.00),
(3, '高级团队', 'wifi_share', 10, 10, 3000.00),
(3, '高级团队', 'goods_sale', 10, 10, 3000.00),
(3, '高级团队', 'advertisement', 10, 10, 3000.00),
(4, '精英团队', 'wifi_share', 15, 20, 8000.00),
(4, '精英团队', 'goods_sale', 15, 20, 8000.00),
(4, '精英团队', 'advertisement', 15, 20, 8000.00),
(5, '王者团队', 'wifi_share', 20, 50, 20000.00),
(5, '王者团队', 'goods_sale', 20, 50, 20000.00),
(5, '王者团队', 'advertisement', 20, 50, 20000.00)
ON DUPLICATE KEY UPDATE
  `level_name` = VALUES(`level_name`),
  `bonus_rate` = VALUES(`bonus_rate`),
  `min_members` = VALUES(`min_members`),
  `min_monthly_amount` = VALUES(`min_monthly_amount`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 提现申请表
CREATE TABLE IF NOT EXISTS `withdraw` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '提现ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `bank_card_id` int(11) DEFAULT NULL COMMENT '银行卡ID',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待审核，1已通过，2已拒绝',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现申请表';

-- 银行卡表
CREATE TABLE IF NOT EXISTS `bank_card` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '银行卡ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `bank_name` varchar(50) NOT NULL COMMENT '银行名称',
  `card_number` varchar(50) NOT NULL COMMENT '卡号',
  `card_holder` varchar(50) NOT NULL COMMENT '持卡人姓名',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认：0否，1是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行卡表';

-- 广告位表
CREATE TABLE IF NOT EXISTS `ad_space` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '广告位ID',
  `name` varchar(50) NOT NULL COMMENT '广告位名称',
  `code` varchar(50) NOT NULL COMMENT '广告位编码',
  `width` int(11) NOT NULL COMMENT '宽度',
  `height` int(11) NOT NULL COMMENT '高度',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='广告位表';

-- 广告表
CREATE TABLE IF NOT EXISTS `advertisement` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '广告ID',
  `space_id` int(11) NOT NULL COMMENT '广告位ID',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `image` varchar(255) NOT NULL COMMENT '图片',
  `url` varchar(255) DEFAULT NULL COMMENT '链接',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `click_count` int(11) NOT NULL DEFAULT '0' COMMENT '点击次数',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '查看次数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0下线，1上线',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `space_id` (`space_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='广告表';

-- 优惠券表
CREATE TABLE IF NOT EXISTS `coupon` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '优惠券ID',
  `name` varchar(50) NOT NULL COMMENT '优惠券名称',
  `type` tinyint(1) NOT NULL COMMENT '类型：1满减，2折扣',
  `amount` decimal(10,2) NOT NULL COMMENT '优惠金额/折扣',
  `min_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最低消费金额',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `total` int(11) NOT NULL DEFAULT '0' COMMENT '发放总量',
  `used` int(11) NOT NULL DEFAULT '0' COMMENT '已使用数量',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券表';

-- 用户优惠券表
CREATE TABLE IF NOT EXISTS `user_coupon` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coupon_id` int(11) NOT NULL COMMENT '优惠券ID',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已使用：0否，1是',
  `used_time` datetime DEFAULT NULL COMMENT '使用时间',
  `order_id` int(11) DEFAULT NULL COMMENT '使用订单ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `coupon_id` (`coupon_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券表';

-- 评价表
CREATE TABLE IF NOT EXISTS `comment` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `content` text COMMENT '评价内容',
  `rating` tinyint(1) NOT NULL DEFAULT '5' COMMENT '评分：1-5',
  `images` text COMMENT '评价图片，JSON格式',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0隐藏，1显示',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `goods_id` (`goods_id`),
  KEY `order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评价表';

-- 系统消息表
CREATE TABLE IF NOT EXISTS `message` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID，为NULL表示全局消息',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '类型：1系统通知，2订单消息，3团队消息',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读：0否，1是',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统消息表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS `system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `key` varchar(50) NOT NULL COMMENT '配置键',
  `value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS `operation_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `admin_id` int(11) DEFAULT NULL COMMENT '管理员ID',
  `module` varchar(50) NOT NULL COMMENT '模块',
  `action` varchar(50) NOT NULL COMMENT '操作',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `content` text COMMENT '操作内容',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `admin_id` (`admin_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 登录日志表
CREATE TABLE IF NOT EXISTS `login_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_type` tinyint(1) NOT NULL COMMENT '用户类型：1管理员，2小程序用户',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `device` varchar(255) DEFAULT NULL COMMENT '设备信息',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0失败，1成功',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';

-- 用户标签表
CREATE TABLE IF NOT EXISTS `user_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `description` varchar(255) DEFAULT NULL COMMENT '标签描述',
  `color` varchar(20) DEFAULT '#409EFF' COMMENT '标签颜色',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户标签表';

-- 用户标签关联表
CREATE TABLE IF NOT EXISTS `user_tag_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `tag_id` int(11) NOT NULL COMMENT '标签ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_tag` (`user_id`, `tag_id`),
  KEY `user_id` (`user_id`),
  KEY `tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户标签关联表';

-- 初始化一些用户标签数据
INSERT INTO `user_tag` (`name`, `description`, `color`) VALUES
('VIP用户', '消费金额超过1000元的用户', '#ff6b6b'),
('新用户', '注册时间不足30天的用户', '#45b7d1'),
('高频用户', '月均登录次数超过20次的用户', '#4ecdc4'),
('团长', '具有团长身份的用户', '#f9ca24'),
('活跃用户', '近期活跃度较高的用户', '#26d0ce');