const express = require('express');
const router = express.Router();

// 引入各个模块的路由
const systemRouter = require('./system');
const userRouter = require('./user');
const wifiRouter = require('./wifi');
const goodsRouter = require('./goods');
const orderRouter = require('./order');
const paymentRouter = require('./payment'); // 添加支付路由器
const paymentConfigRouter = require('./paymentConfig'); // 支付配置路由
const logisticsRouter = require('./logistics'); // 物流路由
const authRouter = require('./auth');
const logRouter = require('./log');
const advertisementRouter = require('./advertisement');
const uploadRouter = require('./upload');
const incomeRouter = require('./income');
const withdrawRouter = require('./withdraw');
const cartRouter = require('./cart');
const adsRouter = require('./ads');
const teamRouter = require('./team');
const clientTeamRouter = require('./client-team'); // 新增客户端团队路由
const allianceRouter = require('./admin-alliance'); // 联盟管理路由
const platformStatsRouter = require('./platform-stats'); // 平台统计路由
const regionRouter = require('./region'); // 地区管理路由
const walletRouter = require('./wallet'); // 钱包管理路由
const profitRouter = require('./profit-management'); // 分润管理路由

// 引入商品控制器（用于无认证访问）
const goodsController = require('../controllers/goods');
// 引入上传控制器
const uploadController = require('../controllers/upload');
// 引入数据库连接
const db = require('../../config/database');
// 引入中间件和响应工具
const { verifyToken } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const config = require('../../config');

// 管理端路由
router.use('/admin/system', systemRouter);
router.use('/admin/user', userRouter);
router.use('/admin/wifi', wifiRouter);
router.use('/admin/goods', goodsRouter);
router.use('/admin/order', orderRouter);
router.use('/admin/auth', authRouter);
router.use('/admin/log', logRouter);
router.use('/admin/advertisement', advertisementRouter);
router.use('/admin/upload', uploadRouter);
router.use('/admin/income', incomeRouter);
router.use('/admin/withdraw', withdrawRouter);
router.use('/admin/team', teamRouter);
router.use('/admin/alliance', allianceRouter); // 联盟管理路由
router.use('/admin/platform', platformStatsRouter); // 平台统计路由
router.use('/admin/region', regionRouter); // 地区管理路由
router.use('/admin/wallet', walletRouter); // 钱包管理路由
router.use('/admin/profit', profitRouter); // 分润管理路由
router.use('/admin/payment-config', paymentConfigRouter);

// 直接处理客户端商品公开API
router.get('/client/goods/list', (req, res) => {
  console.log('直接处理客户端商品列表请求，无需认证');
  goodsController.getGoodsList(req, res);
});

// 增加推荐商品API路由
router.get('/client/goods/recommend', (req, res) => {
  console.log('直接处理客户端推荐商品请求，无需认证');
  // 从查询参数获取limit
  const { limit = 3 } = req.query;
  
  // 修改req.query来获取推荐商品
  req.query.isRecommend = true;
  req.query.limit = limit;
  
  goodsController.getGoodsList(req, res);
});

// 先定义具体路径
router.get('/client/goods/categories', (req, res) => {
  console.log('直接处理客户端商品分类请求，无需认证');
  goodsController.getCategories(req, res);
});

// 增加查询参数形式的商品详情路由
router.get('/client/goods/detail', (req, res) => {
  console.log('直接处理客户端商品详情查询参数请求，无需认证');
  // 从查询参数获取ID
  const { id } = req.query;
  if (!id) {
    return res.status(400).json({
      status: 'error',
      message: '商品ID不能为空'
    });
  }
  
  // 将ID添加到params
  req.params.id = id;
  goodsController.getGoodsDetail(req, res);
});

// 修复正确的商品详情路径 detail/:id 形式
router.get('/client/goods/detail/:id', (req, res) => {
  console.log('直接处理客户端商品详情detail路由请求，无需认证, ID:', req.params.id);
  goodsController.getGoodsDetail(req, res);
});

// 最后定义通配符路径
router.get('/client/goods/:id', (req, res) => {
  console.log('直接处理客户端商品详情参数请求，无需认证, ID:', req.params.id);
  // 将ID添加到params
  goodsController.getGoodsDetail(req, res);
});

// 直接处理客户端购物车公开API
router.get('/client/cart/list', verifyToken, async (req, res) => {
  console.log('直接处理客户端购物车列表请求');
  try {
    // 从请求中获取用户ID
    const userId = req.user.id;
    
    if (!userId) {
      return error(res, '未授权，请先登录', 401);
    }
    
    console.log('获取用户购物车数据，用户ID:', userId);
    
    // 查询购物车数据
    const cartItems = await db.query(`
      SELECT c.id, c.goods_id as goodsId, c.quantity, c.specification_id as specificationId, c.selected,
             g.title as name, g.price, g.cover, g.stock
      FROM cart c
      JOIN goods g ON c.goods_id = g.id
      WHERE c.user_id = ?
      ORDER BY c.created_at DESC
    `, [userId]);
    
    console.log('查询到的购物车数据:', cartItems);
    
    // 如果购物车为空，返回空数组
    if (!cartItems || cartItems.length === 0) {
      return success(res, {
        list: [],
        totalPrice: 0,
        totalQuantity: 0
      }, '购物车为空');
    }
    
    // 添加服务器URL前缀到图片路径
    const serverUrl = `${req.protocol}://${req.get('host')}`;
    const processedCartItems = cartItems.map(item => {
      // 如果cover以/uploads开头，添加服务器URL前缀
      if (item.cover && item.cover.startsWith('/uploads')) {
        item.cover = `${serverUrl}${item.cover}`;
      }
      return item;
    });
    
    // 计算总价和总数量
    const totalPrice = processedCartItems.reduce((sum, item) => 
      item.selected ? sum + (parseFloat(item.price) * item.quantity) : sum, 0);
    
    const totalQuantity = processedCartItems.reduce((sum, item) => 
      item.selected ? sum + item.quantity : sum, 0);
    
    return success(res, {
      list: processedCartItems,
      totalPrice,
      totalQuantity
    }, '获取购物车列表成功');
  } catch (err) {
    console.error('获取购物车数据失败:', err);
    return error(res, '获取购物车数据失败', 500);
  }
});

// 添加购物车添加商品API
router.post('/client/cart/add', verifyToken, async (req, res) => {
  console.log('直接处理客户端购物车添加商品请求，参数:', req.body);
  try {
    const userId = req.user.id;
    
    if (!userId) {
      return error(res, '无效的用户信息', 401);
    }
    
    const { goodsId, quantity, specificationId = 0 } = req.body;
    
    console.log('添加商品到购物车，用户ID:', userId, '商品ID:', goodsId, '数量:', quantity);
    
    // 查询商品是否存在
    const goodsExists = await db.query('SELECT id, title, price, stock FROM goods WHERE id = ? AND status = 1', [goodsId]);
    
    if (!goodsExists || goodsExists.length === 0) {
      return error(res, '商品不存在或已下架', 404);
    }
    
    const goods = goodsExists[0];
    
    // 调试日志，检查库存值和类型
    console.log('商品库存信息:', {
      id: goods.id,
      title: goods.title,
      stock: goods.stock,
      stockType: typeof goods.stock,
      quantity: quantity,
      quantityType: typeof quantity
    });
    
    // 确保库存是数字类型
    const stockNum = parseInt(goods.stock || 0);
    const quantityNum = parseInt(quantity || 0);
    
    // 检查库存，添加宽松条件，只有库存明确为0时才拒绝
    if (stockNum <= 0) {
      return error(res, '商品库存不足', 400);
    }
    
    // 如果库存数值合理但小于请求数量，也要提示
    if (stockNum < quantityNum) {
      return error(res, `商品库存不足，当前库存${stockNum}，请减少购买数量`, 400);
    }
    
    // 查询购物车中是否已有该商品
    const existingCartItem = await db.query(
      'SELECT id, quantity FROM cart WHERE user_id = ? AND goods_id = ? AND specification_id = ?', 
      [userId, goodsId, specificationId]
    );
    
    let cartItem;
    
    // 如果购物车中已有该商品，更新数量
    if (existingCartItem && existingCartItem.length > 0) {
      const newQuantity = parseInt(existingCartItem[0].quantity || 0) + quantityNum;
      
      // 再次检查库存
      if (stockNum < newQuantity) {
        return error(res, `商品库存不足，当前库存${stockNum}，购物车已有${existingCartItem[0].quantity}件`, 400);
      }
      
      await db.query(
        'UPDATE cart SET quantity = ?, updated_at = NOW() WHERE id = ?',
        [newQuantity, existingCartItem[0].id]
      );
      
      cartItem = {
        id: existingCartItem[0].id,
        goodsId,
        quantity: newQuantity,
        specificationId,
        selected: 1
      };
    } 
    // 如果购物车中没有该商品，添加新记录
    else {
      const result = await db.query(
        'INSERT INTO cart (user_id, goods_id, quantity, specification_id, selected) VALUES (?, ?, ?, ?, 1)',
        [userId, goodsId, quantityNum, specificationId]
      );
      
      if (!result || !result.insertId) {
        return error(res, '添加到购物车失败', 500);
      }
      
      cartItem = {
        id: result.insertId,
        goodsId,
        quantity: quantityNum,
        specificationId,
        selected: 1
      };
    }
    
    return success(res, cartItem, '商品已成功添加到购物车');
  } catch (err) {
    console.error('添加购物车失败:', err);
    return error(res, '添加到购物车失败', 500);
  }
});

// 添加购物车更新商品API
router.post('/client/cart/update', verifyToken, async (req, res) => {
  console.log('直接处理客户端购物车更新商品请求，参数:', req.body);
  try {
    const userId = req.user.id;

    if (!userId) {
      return error(res, '无效的用户信息', 401);
    }

    const { id, quantity, selected } = req.body;

    if (!id) {
      return error(res, '购物车项ID不能为空', 400);
    }

    console.log('更新购物车商品，用户ID:', userId, '购物车项ID:', id, '数量:', quantity, '选中状态:', selected);

    // 查询购物车项是否存在且属于该用户
    const existingCartItem = await db.query(
      'SELECT c.id, c.goods_id, g.stock FROM cart c JOIN goods g ON c.goods_id = g.id WHERE c.id = ? AND c.user_id = ?',
      [id, userId]
    );

    if (!existingCartItem || existingCartItem.length === 0) {
      return error(res, '购物车项不存在', 404);
    }

    const cartItem = existingCartItem[0];

    // 构建更新字段
    let updateFields = [];
    let updateValues = [];

    if (quantity !== undefined) {
      const quantityNum = parseInt(quantity);
      if (quantityNum < 1) {
        return error(res, '商品数量必须大于0', 400);
      }

      // 检查库存
      if (quantityNum > cartItem.stock) {
        return error(res, `商品库存不足，当前库存：${cartItem.stock}`, 400);
      }

      updateFields.push('quantity = ?');
      updateValues.push(quantityNum);
    }

    if (selected !== undefined) {
      updateFields.push('selected = ?');
      updateValues.push(selected ? 1 : 0);
    }

    if (updateFields.length === 0) {
      return error(res, '没有需要更新的字段', 400);
    }

    // 更新购物车项
    updateValues.push(id, userId);
    const updateResult = await db.query(
      `UPDATE cart SET ${updateFields.join(', ')}, updated_at = NOW() WHERE id = ? AND user_id = ?`,
      updateValues
    );

    if (!updateResult || updateResult.affectedRows === 0) {
      return error(res, '更新购物车失败', 500);
    }

    // 查询更新后的购物车项
    const updatedCartItem = await db.query(
      'SELECT c.*, g.title as name, g.price, g.cover FROM cart c JOIN goods g ON c.goods_id = g.id WHERE c.id = ?',
      [id]
    );

    return success(res, updatedCartItem[0], '购物车更新成功');
  } catch (err) {
    console.error('更新购物车失败:', err);
    return error(res, '更新购物车失败', 500);
  }
});

// 添加购物车删除商品API
router.delete('/client/cart/remove', verifyToken, async (req, res) => {
  console.log('🔥 DELETE /client/cart/remove 路由被调用！');
  console.log('🔥 请求方法:', req.method);
  console.log('🔥 请求路径:', req.path);
  console.log('🔥 请求体:', req.body);
  console.log('🔥 请求查询参数:', req.query);
  console.log('🔥 用户信息:', req.user);
  console.log('直接处理客户端购物车删除商品请求，参数:', req.body);
  try {
    const userId = req.user.id;

    if (!userId) {
      return error(res, '无效的用户信息', 401);
    }

    const { ids } = req.body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return error(res, '请选择要删除的商品', 400);
    }

    console.log('删除购物车商品，用户ID:', userId, '购物车项IDs:', ids);

    // 删除购物车项
    const placeholders = ids.map(() => '?').join(',');
    const deleteResult = await db.query(
      `DELETE FROM cart WHERE id IN (${placeholders}) AND user_id = ?`,
      [...ids, userId]
    );

    if (!deleteResult) {
      return error(res, '删除购物车商品失败', 500);
    }

    return success(res, { deletedCount: deleteResult.affectedRows }, '删除购物车商品成功');
  } catch (err) {
    console.error('删除购物车商品失败:', err);
    return error(res, '删除购物车商品失败', 500);
  }
});

// 添加购物车清空API
router.post('/client/cart/clear', verifyToken, async (req, res) => {
  console.log('直接处理客户端购物车清空请求');
  try {
    const userId = req.user.id;

    if (!userId) {
      return error(res, '无效的用户信息', 401);
    }

    console.log('清空购物车，用户ID:', userId);

    // 清空用户的购物车
    const deleteResult = await db.query(
      'DELETE FROM cart WHERE user_id = ?',
      [userId]
    );

    if (!deleteResult) {
      return error(res, '清空购物车失败', 500);
    }

    return success(res, { deletedCount: deleteResult.affectedRows }, '购物车已清空');
  } catch (err) {
    console.error('清空购物车失败:', err);
    return error(res, '清空购物车失败', 500);
  }
});

// 直接处理客户端广告公开API
router.get('/client/ads/banner', (req, res) => {
  console.log('直接处理客户端首页轮播图广告请求，无需认证');
  adsRouter.handle({ 
    method: 'GET', 
    url: '/client/banner',
    baseUrl: '',
    originalUrl: '/api/v1/client/ads/banner',
    path: '/client/banner',
    params: {},
    query: req.query,
    headers: req.headers
  }, res);
});

// 直接处理客户端WiFi列表API - 必须放在/client/wifi/:id路由之前
router.get('/client/wifi/list', (req, res) => {
  console.log('直接处理客户端WiFi列表请求，无需认证');
  // 添加调试日志，记录参数
  const { page, pageSize, keyword } = req.query;
  console.log('处理客户端WiFi列表请求，参数：', { page, pageSize, keyword });
  
  // 明确指定路由处理函数
  wifiRouter.handle({ 
    method: 'GET', 
    url: '/client/list',
    baseUrl: '',
    originalUrl: '/api/v1/client/wifi/list',
    path: '/client/list',
    params: {},
    query: req.query,
    headers: req.headers,
    user: req.user || null // 传递用户信息，如果有的话
  }, res);
});

// 处理客户端WiFi的GET请求，用于展示WiFi码
router.get('/client/wifi/:id', async (req, res) => {
  console.log('直接处理客户端WiFi码请求，ID:', req.params.id);
  
  // 确保id不是list，如果是list，直接返回错误
  if (req.params.id === 'list') {
    return error(res, '无效的请求', 400);
  }
  
  try {
    const { id } = req.params;
    
    if (!id || isNaN(parseInt(id))) {
      console.log('无效的ID参数:', id);
      return error(res, '无效的ID参数', 400);
    }
    
    // 记录查询参数
    console.log('查询WiFi数据，ID:', parseInt(id));
    
    // 确保查询返回的是正确格式 [rows, fields]
    const results = await db.query('SELECT * FROM wifi WHERE id = ?', [parseInt(id)]);
    console.log('WiFi查询结果:', results);
    
    // 检查查询结果格式
    if (!results || !Array.isArray(results)) {
      console.log('WiFi查询结果无效，非数组格式');
      return error(res, 'WiFi查询结果格式错误', 500);
    }
    
    // 处理MySQL2返回的 [rows, fields] 格式
    const rows = Array.isArray(results[0]) ? results[0] : results;
    
    // 检查是否有查询结果
    if (!rows || !Array.isArray(rows) || rows.length === 0) {
      console.log('WiFi不存在，ID:', id);
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 获取第一条记录
    const wifiRecord = rows[0];
    
    // 检查记录是否有效
    if (!wifiRecord || typeof wifiRecord !== 'object') {
      console.log('WiFi记录无效:', wifiRecord);
      return error(res, 'WiFi记录格式错误', 500);
    }
    
    console.log('找到WiFi记录:', wifiRecord);
    
    // 创建默认值对象，以防某些字段为null
    const defaultRecord = {
      id: parseInt(id),
      title: '',
      name: '',
      password: '',
      merchant_name: '',
      qrcode: '',
      use_count: 0,
      user_id: 0,
      status: 1,
      created_at: new Date(),
      updated_at: new Date(),
      ad_enabled: false,
      ad_type: 'video',
      ad_content: '',
      ad_duration: 5,
      ad_title: '推荐内容',
      ad_link: '',
      ad_view_count: 0,
      ad_click_count: 0
    };
    
    // 添加广告相关字段，使用默认值对象确保不会有undefined
    // 并将字段名改为前端期望的格式
    const enhancedWifiRecord = {
      ...defaultRecord,
      ...wifiRecord,
      // 改名：将name映射为ssid，merchant_name映射为merchantName
      id: wifiRecord.id || parseInt(id),
      title: wifiRecord.title || '',
      ssid: wifiRecord.name || '', // 映射name到ssid
      password: wifiRecord.password || '',
      merchantName: wifiRecord.merchant_name || '', // 映射merchant_name到merchantName
      qrcode: wifiRecord.qrcode || '',
      use_count: wifiRecord.use_count || 0,
      user_id: wifiRecord.user_id || 0,
      status: wifiRecord.status || 0,
      createTime: wifiRecord.created_at || new Date(), // 映射created_at到createTime
      updateTime: wifiRecord.updated_at || new Date(), // 映射updated_at到updateTime
      ad_enabled: wifiRecord.ad_enabled !== undefined ? wifiRecord.ad_enabled : (wifiRecord.ad_required === 1),
      ad_type: wifiRecord.ad_type || 'video',
      ad_content: wifiRecord.ad_content || wifiRecord.ad_url || '',
      ad_duration: wifiRecord.ad_duration || 5,
      ad_title: wifiRecord.ad_title || '推荐内容',
      ad_link: wifiRecord.ad_link || '',
      ad_view_count: wifiRecord.ad_view_count || 0,
      ad_click_count: wifiRecord.ad_click_count || 0,
      // 添加统计数据
      stats: {
        scanCount: wifiRecord.use_count || 0,
        connectCount: wifiRecord.use_count || 0,
        todayCount: 0,
        totalEarnings: 0
      }
    };
    
    console.log('返回增强的WiFi详情:', enhancedWifiRecord);
    return success(res, enhancedWifiRecord, '获取WiFi码详情成功');
  } catch (err) {
    console.error('获取WiFi详情失败:', err);
    console.error('错误堆栈:', err.stack);
    
    // 确保即使出错也返回一个包含ID的基本对象
    try {
      const id = parseInt(req.params.id);
      const basicRecord = {
        id: id,
        title: 'WiFi详情',
        ssid: '',  // 使用前端期望的字段名ssid，而不是name
        password: '',
        merchantName: '', // 使用前端期望的字段名merchantName，而不是merchant_name
        status: 1,
        createTime: new Date(), // 使用前端期望的字段名createTime
        stats: {
          scanCount: 0,
          connectCount: 0,
          todayCount: 0,
          totalEarnings: 0
        }
      };
      return success(res, basicRecord, '获取WiFi码详情成功，但数据不完整');
    } catch (innerErr) {
      console.error('生成基本记录失败:', innerErr);
    }
    
    return error(res, '获取WiFi详情失败: ' + err.message, 500);
  }
});

router.delete('/client/wifi/:id', async (req, res) => {
  console.log('直接处理客户端WiFi码删除请求');
  try {
    const { id } = req.params;
    
    // 检查WiFi是否存在
    const [wifi] = await db.query('SELECT * FROM wifi WHERE id = ?', [id]);
    
    if (!wifi || wifi.length === 0) {
      return error(res, 'WiFi码不存在', 404);
    }
    
    // 删除WiFi码
    await db.query('DELETE FROM wifi WHERE id = ?', [id]);
    
    console.log('删除WiFi码, ID:', id);
    
    return success(res, { id: parseInt(id) }, 'WiFi码删除成功');
  } catch (err) {
    console.error('删除WiFi码失败:', err);
    return error(res, '删除WiFi码失败: ' + err.message, 500);
  }
});

// 添加客户端文件上传API
router.post('/client/upload', 
  require('../middlewares/auth').verifyToken, 
  uploadController.uploadMiddleware, 
  uploadController.uploadSingle
);

// 客户端API路由
// 导入client-team路由，确保文件名正确
try {
  const clientTeamRouter = require('./client-team');
  console.log('✅ 成功导入客户端团队路由');
  
  // 客户端API路由
  router.use('/client/wifi', wifiRouter);
  router.use('/client/user', userRouter);
  router.use('/client/auth', authRouter);
  router.use('/client/order', orderRouter);
  router.use('/client/payment', paymentRouter); // 修复：使用正确的支付路由器
  router.use('/client/logistics', logisticsRouter); // 物流路由
  router.use('/client/team', clientTeamRouter); // 使用客户端团队路由
  router.use('/client/income', incomeRouter);
  router.use('/client/withdraw', withdrawRouter);
  router.use('/client/ads', adsRouter); // 添加客户端广告路由
  console.log('✅ 成功注册客户端团队路由: /api/v1/client/team');
  console.log('✅ 成功注册客户端广告路由: /api/v1/client/ads');
} catch (err) {
  console.error('❌ 导入客户端团队路由失败:', err);
}

// 添加客户端地址相关API
router.get('/client/address/default', verifyToken, async (req, res) => {
  console.log('处理客户端默认地址请求');

  try {
    const userId = req.user.id;
    console.log('查询用户默认地址，用户ID:', userId);

    // 查询用户的默认地址
    const addresses = await db.query(
      'SELECT * FROM user_address WHERE user_id = ? AND is_default = 1 ORDER BY id DESC LIMIT 1',
      [userId]
    );

    console.log('默认地址查询结果:', addresses);

    if (addresses && addresses.length > 0) {
      const address = addresses[0];
      const addressData = {
        id: address.id,
        name: address.name,
        phone: address.phone,
        province: address.province,
        city: address.city,
        district: address.district,
        address: address.address,
        isDefault: address.is_default === 1
      };

      console.log('返回默认地址数据:', addressData);

      return res.json({
        code: 0,
        message: '获取默认地址成功',
        data: addressData
      });
    } else {
      // 如果没有默认地址，查询第一个地址
      console.log('没有默认地址，查询第一个地址...');
      const firstAddresses = await db.query(
        'SELECT * FROM user_address WHERE user_id = ? ORDER BY id DESC LIMIT 1',
        [userId]
      );

      if (firstAddresses && firstAddresses.length > 0) {
        const address = firstAddresses[0];
        const addressData = {
          id: address.id,
          name: address.name,
          phone: address.phone,
          province: address.province,
          city: address.city,
          district: address.district,
          address: address.address,
          isDefault: false
        };

        console.log('返回第一个地址数据:', addressData);

        return res.json({
          code: 0,
          message: '获取默认地址成功',
          data: addressData
        });
      } else {
        console.log('用户没有任何地址');
        return res.json({
          code: 404,
          message: '暂无收货地址',
          data: null
        });
      }
    }
  } catch (error) {
    console.error('获取默认地址失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取默认地址失败',
      data: null
    });
  }
});

router.get('/client/address/list', verifyToken, async (req, res) => {
  console.log('处理客户端地址列表请求');

  try {
    const userId = req.user.id;
    console.log('查询用户地址列表，用户ID:', userId);

    // 查询用户的收货地址列表
    const addresses = await db.query(
      'SELECT * FROM user_address WHERE user_id = ? ORDER BY is_default DESC, id DESC',
      [userId]
    );

    console.log('地址列表查询结果:', addresses);

    const addressList = addresses.map(address => ({
      id: address.id,
      name: address.name,
      phone: address.phone,
      province: address.province,
      city: address.city,
      district: address.district,
      address: address.address,
      isDefault: address.is_default === 1
    }));

    console.log('返回地址列表数据:', addressList);

    return res.json({
      code: 0,
      message: '获取地址列表成功',
      data: addressList
    });
  } catch (error) {
    console.error('获取地址列表失败:', error);
    return res.status(500).json({
      code: 500,
      message: '获取地址列表失败',
      data: []
    });
  }
});

// 添加商品详情API
router.get('/client/goods/detail', (req, res) => {
  console.log('处理客户端商品详情请求，商品ID:', req.query.id);
  
  // 返回模拟的商品详情数据
  return res.json({
    code: 0,
    status: "success",
    message: '获取商品详情成功',
    data: {
      id: req.query.id || 1,
      name: '商品示例 - ' + (req.query.id || 1),
      price: 99.00,
      originalPrice: 199.00,
      sales: 100,
      stock: 999,
      description: '这是一个示例商品描述',
      images: [
        'https://img.example.com/goods1.jpg',
        'https://img.example.com/goods2.jpg'
      ],
      specifications: [
        {
          id: 1,
          name: '默认规格',
          price: 99.00,
          stock: 999,
          size: 'M'
        },
        {
          id: 2,
          name: '大号',
          price: 109.00,
          stock: 500,
          size: 'L'
        },
        {
          id: 3,
          name: '加大号',
          price: 119.00,
          stock: 300,
          size: 'XL'
        }
      ],
      categoryId: 1,
      categoryName: '示例分类',
      isNew: true,
      isHot: true,
      status: 1
    }
  });
});

// WiFi二维码专用路由 - 使用完全不同的路径避免冲突
router.get('/client/wifi-qrcode', async (req, res) => {
  console.log('处理独立WiFi二维码生成请求，参数:', req.query);
  try {
    const { ssid, password, encryption = 'WPA', hidden = 'false', adEnabled = 'false' } = req.query;
    
    if (!ssid || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'SSID和密码不能为空'
      });
    }
    
    // 检查是否启用广告模式
    const useAdMode = adEnabled === 'true' || adEnabled === '1';
    
    let qrCodeData;
    let qrServerUrl;
    
    if (useAdMode) {
      // 广告模式：生成指向广告页面的URL
      const adPageUrl = `/pages/wifi/ad-view/ad-view?ssid=${encodeURIComponent(ssid)}&pwd=${encodeURIComponent(password)}&type=video`;
      qrCodeData = adPageUrl;
      console.log('使用广告模式生成二维码，数据:', qrCodeData);
    } else {
      // 直连模式：生成标准WiFi连接格式
      qrCodeData = `WIFI:T:${encryption};S:${ssid};P:${password};H:${hidden};;`;
      console.log('使用直连模式生成二维码，数据:', qrCodeData);
    }
    
    // 使用第三方服务生成二维码
    qrServerUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(qrCodeData)}&size=300x300&format=png`;
    
    console.log('生成WiFi二维码成功，URL:', qrServerUrl);
    
    // 返回二维码URL
    return res.json({
      status: 'success',
      message: '二维码生成成功',
      data: {
        qrcode_url: qrServerUrl,
        wifi_string: qrCodeData,
        ad_enabled: useAdMode
      }
    });
  } catch (err) {
    console.error('生成WiFi二维码失败:', err);
    return res.status(500).json({
      status: 'error',
      message: '生成WiFi二维码失败: ' + err.message
    });
  }
});

// WiFi海报生成路由 (通用接口)
router.get('/client/wifi-poster', async (req, res) => {
  console.log('处理WiFi海报生成请求，参数:', req.query);
  try {
    const { 
      ssid, 
      password, 
      title = '免费WiFi', 
      merchant = '', 
      logo = '', 
      style = 'default',
      encryption = 'WPA', 
      hidden = 'false' 
    } = req.query;
    
    if (!ssid || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'SSID和密码不能为空'
      });
    }
    
    // 构建WiFi连接字符串
    const wifiString = `WIFI:T:${encryption};S:${ssid};P:${password};H:${hidden};;`;
    
    // 构建二维码URL
    const qrcodeUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(wifiString)}&size=300x300&format=png`;
    
    // 选择海报模板 - 使用本地模板URL而不是外部API
    let posterTemplate;
    let posterBackground;
    let textColor;
    let accentColor;
    
    switch (style) {
      case 'business':
        posterTemplate = '/wifi-posters/business-template.png';
        posterBackground = '#2C3E50';
        textColor = '#FFFFFF';
        accentColor = '#3498DB';
        break;
      case 'cafe':
        posterTemplate = '/wifi-posters/cafe-template.png';
        posterBackground = '#5D4037';
        textColor = '#FFFFFF';
        accentColor = '#8D6E63';
        break;
      case 'modern':
        posterTemplate = '/wifi-posters/modern-template.png';
        posterBackground = '#212121';
        textColor = '#FFFFFF';
        accentColor = '#00BCD4';
        break;
      case 'colorful':
        posterTemplate = '/wifi-posters/colorful-template.png';
        posterBackground = '#673AB7';
        textColor = '#FFFFFF';
        accentColor = '#FFC107';
        break;
      case 'minimal':
        posterTemplate = '/wifi-posters/minimal-template.png';
        posterBackground = '#FFFFFF';
        textColor = '#212121';
        accentColor = '#607D8B';
        break;
      default:
        posterTemplate = '/wifi-posters/default-template.png';
        posterBackground = '#1976D2';
        textColor = '#FFFFFF';
        accentColor = '#BBDEFB';
    }
    
    // 构建海报数据 - 注意不包含密码
    const posterData = {
      title: title || 'WiFi连接',
      ssid: ssid,
      // 不包含密码，符合要求
      merchant: merchant,
      logo: logo,
      style: style,
      qrcode: qrcodeUrl,
      template: posterTemplate,
      background: posterBackground,
      textColor: textColor,
      accentColor: accentColor
    };
    
    // 生成海报预览URL
    // 在实际项目中，这里应该使用Canvas或图像处理库在服务器端生成真实的海报图像
    // 这里我们返回一个模拟的海报数据结构，前端将负责实际渲染
    
    console.log('生成WiFi海报成功');
    
    // 返回海报数据
    return res.json({
      status: 'success',
      message: '海报生成成功',
      data: {
        poster_data: posterData,
        available_styles: [
          { id: 'default', name: '默认蓝色', color: '#1976D2' },
          { id: 'business', name: '商务风格', color: '#2C3E50' },
          { id: 'cafe', name: '咖啡馆', color: '#5D4037' },
          { id: 'modern', name: '现代简约', color: '#212121' },
          { id: 'colorful', name: '缤纷色彩', color: '#673AB7' },
          { id: 'minimal', name: '极简风格', color: '#FFFFFF' }
        ],
        wifi_string: wifiString
      }
    });
  } catch (err) {
    console.error('生成WiFi海报失败:', err);
    return res.status(500).json({
      status: 'error',
      message: '生成WiFi海报失败: ' + err.message
    });
  }
});

// 直接处理admin WIFI详情请求（测试用）
router.get('/admin/wifi/detail/:id', async (req, res) => {
  console.log('直接处理管理端WiFi码详情请求（v1.js中）', req.params);
  try {
    const { id } = req.params;
    
    if (!id || isNaN(parseInt(id))) {
      console.log('无效的ID参数:', id);
      return error(res, '无效的ID参数', 400);
    }
    
    // 添加调试日志
    console.log('正在查询WiFi记录，ID:', id);
    
    try {
      // 修改查询方式，确保返回的是数组
      const wifiResults = await db.query('SELECT * FROM wifi WHERE id = ?', [parseInt(id)]);
      console.log('查询结果类型:', typeof wifiResults);
      console.log('查询结果:', wifiResults);
      
      if (!wifiResults || !Array.isArray(wifiResults) || wifiResults.length === 0) {
        console.log('WiFi查询结果无效');
        return error(res, 'WiFi查询结果无效', 500);
      }
      
      // MySQL2返回的结果可能是[rows, fields]格式
      const rows = Array.isArray(wifiResults[0]) ? wifiResults[0] : wifiResults;
      
      // 检查查询结果
      if (!rows || rows.length === 0) {
        console.log('WiFi不存在(v1.js):', id);
        return error(res, 'WiFi码不存在', 404);
      }
      
      // 获取第一条记录
      const wifiRecord = rows[0];
      console.log('返回WiFi详情(v1.js):', wifiRecord);
      
      if (!wifiRecord) {
        console.log('WiFi记录为空:', id);
        return error(res, 'WiFi记录无效', 500);
      }
      
      // 添加或转换广告相关字段
      const enhancedWifiRecord = {
        id: wifiRecord.id || 0,
        title: wifiRecord.title || '',
        name: wifiRecord.name || '',
        password: wifiRecord.password || '',
        merchant_name: wifiRecord.merchant_name || '',
        qrcode: wifiRecord.qrcode || '',
        use_count: wifiRecord.use_count || 0,
        user_id: wifiRecord.user_id || 0,
        status: wifiRecord.status || 0,
        created_at: wifiRecord.created_at || new Date(),
        updated_at: wifiRecord.updated_at || new Date(),
        ad_enabled: wifiRecord.ad_enabled !== undefined ? wifiRecord.ad_enabled : (wifiRecord.ad_required === 1),
        ad_type: wifiRecord.ad_type || 'video',
        ad_content: wifiRecord.ad_content || wifiRecord.ad_url || '',
        ad_duration: wifiRecord.ad_duration || 5,
        ad_title: wifiRecord.ad_title || '推荐内容',
        ad_link: wifiRecord.ad_link || '',
        ad_view_count: wifiRecord.ad_view_count || 0,
        ad_click_count: wifiRecord.ad_click_count || 0
      };
      
      return success(res, enhancedWifiRecord, '获取WiFi码详情成功');
    } catch (dbErr) {
      console.error('数据库查询出错:', dbErr);
      return error(res, '数据库查询失败: ' + dbErr.message, 500);
    }
  } catch (err) {
    console.error('获取WiFi详情失败(v1.js)，详细错误:', err);
    console.error('错误堆栈:', err.stack);
    return error(res, '获取WiFi详情失败: ' + err.message, 500);
  }
});

// WiFi海报生成路由 (按WiFi ID)
router.post('/client/wifi/:id/poster', verifyToken, async (req, res) => {
  console.log('处理WiFi海报生成请求，WiFi ID:', req.params.id);
  try {
    const wifiId = req.params.id;

    // 从数据库获取WiFi信息
    const db = require('../database');
    const wifi = await db.query(
      'SELECT * FROM wifi WHERE id = ? AND user_id = ?',
      [wifiId, req.user.id]
    );

    if (!wifi || wifi.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: '未找到该WiFi码或无权限访问'
      });
    }

    const wifiInfo = wifi[0];
    const {
      title = wifiInfo.title || '免费WiFi',
      style = 'default'
    } = req.body;

    // 构建WiFi连接字符串
    const wifiString = `WIFI:T:${wifiInfo.encryption || 'WPA'};S:${wifiInfo.ssid};P:${wifiInfo.password};H:${wifiInfo.hidden || 'false'};;`;

    // 构建二维码URL
    const qrcodeUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(wifiString)}&size=300x300&format=png`;

    // 选择海报模板
    let posterTemplate;
    let posterBackground;
    let textColor;
    let accentColor;

    switch (style) {
      case 'business':
        posterTemplate = '/wifi-posters/business-template.png';
        posterBackground = '#2C3E50';
        textColor = '#FFFFFF';
        accentColor = '#3498DB';
        break;
      case 'cafe':
        posterTemplate = '/wifi-posters/cafe-template.png';
        posterBackground = '#5D4037';
        textColor = '#FFFFFF';
        accentColor = '#8D6E63';
        break;
      case 'modern':
        posterTemplate = '/wifi-posters/modern-template.png';
        posterBackground = '#212121';
        textColor = '#FFFFFF';
        accentColor = '#00BCD4';
        break;
      case 'colorful':
        posterTemplate = '/wifi-posters/colorful-template.png';
        posterBackground = '#673AB7';
        textColor = '#FFFFFF';
        accentColor = '#FFC107';
        break;
      case 'minimal':
        posterTemplate = '/wifi-posters/minimal-template.png';
        posterBackground = '#FFFFFF';
        textColor = '#212121';
        accentColor = '#607D8B';
        break;
      default:
        posterTemplate = '/wifi-posters/default-template.png';
        posterBackground = '#1976D2';
        textColor = '#FFFFFF';
        accentColor = '#BBDEFB';
    }

    // 构建海报数据
    const posterData = {
      title: title,
      ssid: wifiInfo.ssid,
      merchant: wifiInfo.merchant_name || '',
      logo: wifiInfo.logo || '',
      style: style,
      qrcode: qrcodeUrl,
      template: posterTemplate,
      background: posterBackground,
      textColor: textColor,
      accentColor: accentColor
    };

    console.log('生成WiFi海报成功');

    // 返回海报数据
    return res.json({
      success: true,
      status: 'success',
      message: '海报生成成功',
      data: {
        posterUrl: qrcodeUrl, // 临时返回二维码URL作为海报URL
        poster_data: posterData,
        available_styles: [
          { id: 'default', name: '默认蓝色', color: '#1976D2' },
          { id: 'business', name: '商务风格', color: '#2C3E50' },
          { id: 'cafe', name: '咖啡馆', color: '#5D4037' },
          { id: 'modern', name: '现代简约', color: '#212121' },
          { id: 'colorful', name: '缤纷色彩', color: '#673AB7' },
          { id: 'minimal', name: '极简风格', color: '#FFFFFF' }
        ],
        wifi_string: wifiString
      }
    });
  } catch (err) {
    console.error('生成WiFi海报失败:', err);
    return res.status(500).json({
      status: 'error',
      message: '生成WiFi海报失败: ' + err.message
    });
  }
});

module.exports = router;