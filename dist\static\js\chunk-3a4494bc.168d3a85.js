(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3a4494bc"],{"37b6":function(t,e,a){"use strict";a("507d")},"507d":function(t,e,a){},ac98:function(t,e,a){"use strict";a.d(e,"e",(function(){return i})),a.d(e,"d",(function(){return n})),a.d(e,"b",(function(){return r})),a.d(e,"j",(function(){return l})),a.d(e,"c",(function(){return o})),a.d(e,"g",(function(){return c})),a.d(e,"f",(function(){return d})),a.d(e,"a",(function(){return m})),a.d(e,"i",(function(){return u})),a.d(e,"h",(function(){return p}));var s=a("b775");function i(t){return Object(s["a"])({url:"/api/v1/admin/team/list",method:"get",params:t})}function n(t){return Object(s["a"])({url:"/api/v1/admin/team/detail/"+t,method:"get"})}function r(t){return Object(s["a"])({url:"/api/v1/admin/team/create",method:"post",data:t})}function l(t,e){return Object(s["a"])({url:"/api/v1/admin/team/update/"+t,method:"put",data:e})}function o(t){return Object(s["a"])({url:"/api/v1/admin/team/delete/"+t,method:"delete"})}function c(t){return Object(s["a"])({url:"/api/v1/admin/team/stats/"+t,method:"get"})}function d(t,e){return Object(s["a"])({url:`/api/v1/admin/team/${t}/members`,method:"get",params:e})}function m(t,e){return Object(s["a"])({url:`/api/v1/admin/team/${t}/members`,method:"post",data:{user_id:e}})}function u(t,e){return Object(s["a"])({url:`/api/v1/admin/team/${t}/members/${e}`,method:"delete"})}function p(t,e){return u(t,e)}},c466:function(t,e,a){"use strict";function s(t,e="YYYY-MM-DD HH:mm:ss"){if(!t)return"";const a=new Date(t);if(isNaN(a.getTime()))return"";const s=a.getFullYear(),i=String(a.getMonth()+1).padStart(2,"0"),n=String(a.getDate()).padStart(2,"0"),r=String(a.getHours()).padStart(2,"0"),l=String(a.getMinutes()).padStart(2,"0"),o=String(a.getSeconds()).padStart(2,"0");return e.replace("YYYY",s).replace("MM",i).replace("DD",n).replace("HH",r).replace("mm",l).replace("ss",o)}a.d(e,"a",(function(){return s}))},d034:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"team-detail-container"},[e("el-card",{staticClass:"info-card"},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("团队基本信息")]),e("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.handleEdit}},[t._v("编辑")])],1),e("el-descriptions",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{column:2,border:""}},[e("el-descriptions-item",{attrs:{label:"团队ID"}},[t._v(t._s(t.teamInfo.id))]),e("el-descriptions-item",{attrs:{label:"团队名称"}},[t._v(t._s(t.teamInfo.name))]),e("el-descriptions-item",{attrs:{label:"团长"}},[t.teamInfo.leader_name?e("div",{staticClass:"user-info"},[e("el-avatar",{attrs:{size:30,src:t.teamInfo.leader_avatar||"/img/default-avatar.png"}}),e("span",{staticClass:"user-name"},[t._v(t._s(t.teamInfo.leader_name))])],1):t._e()]),e("el-descriptions-item",{attrs:{label:"成员数量"}},[e("el-tag",{attrs:{type:"info"}},[t._v(t._s(t.teamInfo.member_count||0)+" 人")])],1),e("el-descriptions-item",{attrs:{label:"团队状态"}},[e("el-tag",{attrs:{type:1===t.teamInfo.status?"success":"danger"}},[t._v(" "+t._s(1===t.teamInfo.status?"正常":"禁用")+" ")])],1),e("el-descriptions-item",{attrs:{label:"创建时间"}},[t._v(t._s(t.formatDate(t.teamInfo.created_at)))]),e("el-descriptions-item",{attrs:{label:"团队简介",span:2}},[t._v(" "+t._s(t.teamInfo.description||"暂无简介")+" ")])],1)],1),e("el-card",{staticClass:"stats-card"},[e("div",{attrs:{slot:"header"},slot:"header"},[t._v("收益统计")]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v("¥"+t._s((t.teamStats.total_revenue||0).toFixed(2)))]),e("div",{staticClass:"stat-label"},[t._v("总收益")])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v("¥"+t._s((t.teamStats.month_revenue||0).toFixed(2)))]),e("div",{staticClass:"stat-label"},[t._v("本月收益")])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.teamStats.total_orders||0))]),e("div",{staticClass:"stat-label"},[t._v("总订单数")])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-value"},[t._v(t._s(t.teamStats.month_orders||0))]),e("div",{staticClass:"stat-label"},[t._v("本月订单")])])])],1)],1),e("el-card",{staticClass:"members-card"},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("团队成员")]),e("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.handleAddMember}},[t._v("添加成员")])],1),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.memberList,border:""}},[e("el-table-column",{attrs:{prop:"id",label:"用户ID",width:"80",align:"center"}}),e("el-table-column",{attrs:{prop:"nickname",label:"成员昵称","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticClass:"user-info"},[e("el-avatar",{attrs:{size:30,src:a.row.avatar||"/img/default-avatar.png"}}),e("span",{staticClass:"user-name"},[t._v(t._s(a.row.nickname||"未知"))])],1)]}}])}),e("el-table-column",{attrs:{prop:"phone",label:"手机号",width:"120"}}),e("el-table-column",{attrs:{prop:"contribution",label:"贡献收益",width:"120",align:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticClass:"revenue"},[t._v("¥"+t._s((a.row.contribution||0).toFixed(2)))])]}}])}),e("el-table-column",{attrs:{prop:"joined_at",label:"加入时间",width:"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.joined_at))+" ")]}}])}),e("el-table-column",{attrs:{label:"操作",width:"120",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{staticStyle:{color:"#F56C6C"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.handleRemoveMember(a.row)}}},[t._v(" 移除 ")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pagination.page,"page-sizes":[10,20,50],"page-size":t.pagination.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.pagination.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)},i=[],n=(a("14d9"),a("ac98")),r=a("c466"),l={name:"TeamDetail",data(){return{loading:!1,teamId:null,teamInfo:{},teamStats:{},memberList:[],pagination:{page:1,limit:10,total:0}}},created(){this.teamId=this.$route.params.id,this.fetchData()},methods:{async fetchData(){this.loading=!0;try{const t=await Object(n["d"])(this.teamId);"success"===t.status&&(this.teamInfo=t.data);const e=await Object(n["g"])(this.teamId);"success"===e.status&&(this.teamStats=e.data),this.fetchMembers()}catch(t){this.$message.error("获取团队信息失败")}finally{this.loading=!1}},async fetchMembers(){try{const t={team_id:this.teamId,page:this.pagination.page,limit:this.pagination.limit},e=await Object(n["f"])(t);"success"===e.status&&(this.memberList=e.data.list||[],this.pagination.total=e.data.total||0)}catch(t){this.$message.error("获取成员列表失败")}},handleEdit(){this.$router.push("/team/edit/"+this.teamId)},handleAddMember(){this.$router.push(`/team/members/${this.teamId}/add`)},handleRemoveMember(t){this.$confirm(`确定要将 ${t.nickname} 移出团队吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await Object(n["h"])(this.teamId,t.id);0===e.code&&(this.$message.success("移除成功"),this.fetchMembers(),this.teamInfo.member_count=(this.teamInfo.member_count||1)-1)}catch(e){this.$message.error("移除失败")}}).catch(()=>{})},handleSizeChange(t){this.pagination.limit=t,this.fetchMembers()},handleCurrentChange(t){this.pagination.page=t,this.fetchMembers()},formatDate:r["a"]}},o=l,c=(a("37b6"),a("2877")),d=Object(c["a"])(o,s,i,!1,null,"1f7d674e",null);e["default"]=d.exports}}]);