<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.keyword" placeholder="订单号/收货人" clearable class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.status" placeholder="订单状态" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        class="filter-item"
        style="width: 240px;"
        @change="handleDateRangeChange"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="正在加载..."
      border
      fit
      highlight-current-row
    >
      <el-table-column label="订单号" prop="order_no" align="center" min-width="180" />
      <el-table-column label="用户" align="center" width="150">
        <template slot-scope="{row}">
          <div>{{ row.user_nickname }}</div>
        </template>
      </el-table-column>
      <el-table-column label="收货人" align="center" width="120">
        <template slot-scope="{row}">
          <div>{{ row.receiver_name }}</div>
          <div>{{ row.receiver_phone }}</div>
        </template>
      </el-table-column>
      <el-table-column label="订单金额" align="center" width="120">
        <template slot-scope="{row}">
          <span>{{ row.total_amount }} 元</span>
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" width="120">
        <template slot-scope="{row}">
          <el-tag v-if="row.payment_method === 1" type="primary">微信支付</el-tag>
          <el-tag v-else-if="row.payment_method === 2" type="success">余额支付</el-tag>
          <el-tag v-else type="info">其他</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" width="100">
        <template slot-scope="{row}">
          <el-tag :type="getOrderStatusType(row.status)">{{ getOrderStatusText(row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="created_at" align="center" width="160" />
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleView(row)">查看</el-button>
          <el-button v-if="row.status === 1" type="success" size="mini" @click="handleShip(row)">发货</el-button>
          <el-button v-if="row.status === 0" type="danger" size="mini" @click="handleCancel(row)">取消</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 发货对话框 -->
    <el-dialog title="订单发货" :visible.sync="shipDialogVisible" width="500px">
      <el-form ref="shipForm" :model="shipForm" :rules="shipRules" label-width="100px">
        <el-form-item label="物流公司" prop="logistics_company">
          <el-select v-model="shipForm.logistics_company" placeholder="请选择物流公司" style="width: 100%">
            <el-option v-for="item in logisticsOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号" prop="logistics_no">
          <el-input v-model="shipForm.logistics_no" placeholder="请输入物流单号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="shipDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmShip">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOrderList, updateOrderStatus, updateOrderLogistics } from '@/api/order'
import Pagination from '@/components/Pagination'

export default {
  name: 'OrderList',
  components: { Pagination },
  data () {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        keyword: undefined,
        status: undefined,
        start_date: undefined,
        end_date: undefined
      },
      dateRange: null,
      statusOptions: [
        { label: '待支付', value: 0 },
        { label: '待发货', value: 1 },
        { label: '待收货', value: 2 },
        { label: '已完成', value: 3 },
        { label: '已取消', value: 4 }
      ],
      logisticsOptions: [
        { label: '顺丰速运', value: 'SF' },
        { label: '中通快递', value: 'ZTO' },
        { label: '圆通速递', value: 'YTO' },
        { label: '申通快递', value: 'STO' },
        { label: '韵达快递', value: 'YD' },
        { label: '天天快递', value: 'TTKD' },
        { label: '百世快递', value: 'HTKY' },
        { label: '邮政快递包裹', value: 'YZPY' },
        { label: 'EMS', value: 'EMS' }
      ],
      shipDialogVisible: false,
      shipForm: {
        order_id: null,
        logistics_company: '',
        logistics_no: ''
      },
      shipRules: {
        logistics_company: [
          { required: true, message: '请选择物流公司', trigger: 'change' }
        ],
        logistics_no: [
          { required: true, message: '请输入物流单号', trigger: 'blur' },
          { min: 5, message: '物流单号长度不能少于5个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.listLoading = true
      getOrderList(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleFilter () {
      this.listQuery.page = 1
      this.getList()
    },
    handleDateRangeChange (val) {
      if (val) {
        this.listQuery.start_date = val[0]
        this.listQuery.end_date = val[1]
      } else {
        this.listQuery.start_date = undefined
        this.listQuery.end_date = undefined
      }
    },
    handleView (row) {
      this.$router.push(`/mall/order/detail/${row.id}`)
    },
    handleShip (row) {
      this.shipForm = {
        order_id: row.id,
        logistics_company: '',
        logistics_no: ''
      }
      this.shipDialogVisible = true
    },
    handleCancel (row) {
      this.$confirm('确认要取消该订单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateOrderStatus(row.id, { status: 4 }).then(response => {
          this.$message.success('订单取消成功')
          this.getList()
        }).catch(() => {
          this.$message.error('订单取消失败')
        })
      }).catch(() => {
        // 取消操作
      })
    },
    confirmShip () {
      this.$refs.shipForm.validate(valid => {
        if (valid) {
          updateOrderLogistics(this.shipForm.order_id, {
            status: 2, // 更新为已发货状态
            logistics_company: this.shipForm.logistics_company,
            logistics_no: this.shipForm.logistics_no
          }).then(response => {
            this.$message.success('发货成功')
            this.shipDialogVisible = false
            this.getList()
          }).catch(() => {
            this.$message.error('发货失败')
          })
        }
      })
    },
    getOrderStatusType (status) {
      const statusMap = {
        0: 'info',
        1: 'primary',
        2: 'warning',
        3: 'success',
        4: 'danger'
      }
      return statusMap[status] || 'info'
    },
    getOrderStatusText (status) {
      const statusMap = {
        0: '待支付',
        1: '待发货',
        2: '待收货',
        3: '已完成',
        4: '已取消'
      }
      return statusMap[status] || '未知状态'
    }
  }
}
</script>
