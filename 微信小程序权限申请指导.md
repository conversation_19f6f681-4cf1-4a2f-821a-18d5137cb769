# 微信小程序 wx.chooseAddress 权限申请指导

## 🎯 问题说明

小程序使用了 `wx.chooseAddress` 接口来获取用户收货地址，但需要在微信公众平台申请相应权限才能正常使用。

## 📋 当前配置状态

### ✅ 代码配置已完成
- `app.json` 中已正确配置权限声明
- `pages/user/address/edit.js` 中功能代码已恢复
- `pages/user/address/edit.wxml` 中按钮已恢复显示

### 🔧 需要申请的权限
- **接口名称**: `wx.chooseAddress`
- **功能说明**: 获取用户收货地址
- **使用场景**: 商城购物时快速填写收货地址

## 🚀 权限申请步骤

### 第一步：登录微信公众平台
1. 访问 [微信公众平台](https://mp.weixin.qq.com/)
2. 使用小程序管理员账号登录
3. 选择对应的小程序

### 第二步：进入接口设置
1. 在左侧菜单中找到 **"开发管理"**
2. 点击 **"接口设置"**
3. 在接口列表中找到 **"收货地址"** 相关接口

### 第三步：申请权限
1. 找到 **"wx.chooseAddress"** 接口
2. 点击 **"申请开通"** 或 **"申请权限"**
3. 填写申请理由和使用场景

### 第四步：填写申请信息
**申请理由示例：**
```
我们的WiFi共享商城小程序需要使用收货地址功能，用于：

1. 商城购物时快速获取用户收货地址
2. 提升用户购物体验，减少手动输入
3. 确保地址信息准确性，提高配送成功率

具体使用场景：
- 用户在地址管理页面可以一键导入微信收货地址
- 订单确认时可以快速选择收货地址
- 避免用户重复输入地址信息

该功能仅在用户主动点击"使用微信收货地址"时调用，
不会在用户不知情的情况下获取地址信息。
```

### 第五步：等待审核
- 提交申请后等待微信官方审核
- 审核时间通常为 1-3 个工作日
- 审核通过后接口即可正常使用

## 📱 功能使用说明

### 用户操作流程
1. 用户进入 **"地址管理"** → **"添加/编辑地址"** 页面
2. 点击 **"使用微信收货地址"** 按钮
3. 系统调用 `wx.chooseAddress` 接口
4. 用户在微信弹窗中选择收货地址
5. 地址信息自动填入表单

### 技术实现
```javascript
// pages/user/address/edit.js
onUseWechatAddress: function() {
  wx.chooseAddress({
    success: (res) => {
      // 自动填充地址信息
      this.setData({
        address: {
          name: res.userName,
          phone: res.telNumber,
          province: res.provinceName,
          city: res.cityName,
          district: res.countyName,
          address: res.detailInfo,
          is_default: this.data.address.is_default
        },
        region: [res.provinceName, res.cityName, res.countyName]
      });
    },
    fail: (err) => {
      // 处理失败情况
      if (err.errMsg !== 'chooseAddress:fail cancel') {
        wx.showToast({
          title: '获取微信地址失败',
          icon: 'none'
        });
      }
    }
  });
}
```

## ⚠️ 注意事项

### 1. 权限申请要点
- **明确说明使用场景**：详细描述为什么需要这个权限
- **强调用户体验**：说明如何提升用户使用体验
- **保证用户知情**：说明只在用户主动操作时调用

### 2. 审核可能被拒的原因
- 申请理由不充分
- 使用场景描述不清楚
- 小程序类目不符合要求
- 功能实现不规范

### 3. 如果申请被拒
- 仔细阅读拒绝理由
- 完善申请材料
- 重新提交申请

## 🎉 申请成功后

### 验证权限
1. 重新上传小程序代码
2. 提交审核时不会再出现权限警告
3. 用户可以正常使用"使用微信收货地址"功能

### 用户体验提升
- ✅ 一键导入微信地址
- ✅ 减少手动输入错误
- ✅ 提高购物流程效率
- ✅ 提升整体用户满意度

## 📞 如需帮助

如果在申请过程中遇到问题：
1. 查看微信官方文档
2. 联系微信客服
3. 参考其他成功案例

**记住：权限申请是为了更好的用户体验，合理的申请理由和清晰的使用场景说明是成功的关键！** 🚀
