<template>
  <div class="app-container">
    <div v-loading="loading" class="withdraw-detail">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>提现申请详情</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-section">
              <h3 class="section-title">基本信息</h3>
              <div class="info-item">
                <span class="label">提现ID：</span>
                <span class="value">{{ detail.id }}</span>
              </div>
              <div class="info-item">
                <span class="label">申请时间：</span>
                <span class="value">{{ detail.created_at }}</span>
              </div>
              <div class="info-item">
                <span class="label">状态：</span>
                <span class="value">
                  <el-tag :type="getStatusType(detail.status)">{{ getStatusLabel(detail.status) }}</el-tag>
                </span>
              </div>
              <div class="info-item" v-if="detail.status !== 0">
                <span class="label">审核时间：</span>
                <span class="value">{{ detail.audit_time || '-' }}</span>
              </div>
              <div class="info-item" v-if="detail.status === 3">
                <span class="label">打款时间：</span>
                <span class="value">{{ detail.transfer_time || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">备注：</span>
                <span class="value">{{ detail.remark || '-' }}</span>
              </div>
            </div>

            <div class="info-section">
              <h3 class="section-title">提现信息</h3>
              <div class="info-item">
                <span class="label">提现金额：</span>
                <span class="value withdraw-amount">{{ detail.amount }} 元</span>
              </div>
              <div class="info-item">
                <span class="label">手续费：</span>
                <span class="value">{{ detail.fee || 0 }} 元</span>
              </div>
              <div class="info-item">
                <span class="label">实际到账：</span>
                <span class="value actual-amount">{{ (detail.amount - (detail.fee || 0)).toFixed(2) }} 元</span>
              </div>
              <div v-if="detail.status === 3" class="info-item">
                <span class="label">流水号：</span>
                <span class="value">{{ detail.transaction_id }}</span>
              </div>
            </div>
          </el-col>

          <el-col :span="12">
            <div class="info-section">
              <h3 class="section-title">用户信息</h3>
              <div class="user-header">
                <el-avatar :size="64" :src="userInfo.avatar">
                  <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"/>
                </el-avatar>
                <h4 class="nickname">{{ userInfo.nickname }}</h4>
                <div class="user-role">
                  <el-tag v-if="userInfo.is_leader === 1" type="warning">团长</el-tag>
                  <el-tag v-else type="info">成员</el-tag>
                </div>
              </div>
              <div class="info-item">
                <span class="label">用户ID：</span>
                <span class="value">{{ userInfo.id }}</span>
              </div>
              <div class="info-item">
                <span class="label">手机号：</span>
                <span class="value">{{ userInfo.phone || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">当前余额：</span>
                <span class="value">{{ userInfo.balance || 0 }} 元</span>
              </div>
              <div class="info-item">
                <el-button type="text" @click="viewUser">查看用户详情</el-button>
              </div>
            </div>

            <div class="info-section">
              <h3 class="section-title">银行卡信息</h3>
              <div class="info-item">
                <span class="label">持卡人：</span>
                <span class="value">{{ detail.card_holder }}</span>
              </div>
              <div class="info-item">
                <span class="label">银行名称：</span>
                <span class="value">{{ detail.bank_name }}</span>
              </div>
              <div class="info-item">
                <span class="label">卡号：</span>
                <span class="value card-number">{{ detail.card_number }}</span>
              </div>
              <div class="info-item">
                <span class="label">开户行：</span>
                <span class="value">{{ detail.bank_branch || '-' }}</span>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 操作按钮 -->
        <div class="action-buttons" v-if="detail.status === 0 || detail.status === 1">
          <el-button v-if="detail.status === 0" type="success" @click="handleAudit(1)">通过审核</el-button>
          <el-button v-if="detail.status === 0" type="danger" @click="handleAudit(2)">拒绝申请</el-button>
          <el-button v-if="detail.status === 1" type="warning" @click="handleConfirm">确认打款</el-button>
        </div>
      </el-card>
    </div>

    <!-- 审核对话框 -->
    <el-dialog :title="auditForm.status === 1 ? '审核通过' : '审核拒绝'" :visible.sync="auditDialogVisible" width="500px">
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="auditForm.remark"
            type="textarea"
            :rows="3"
            :placeholder="auditForm.status === 1 ? '请输入审核通过备注（选填）' : '请输入拒绝原因（必填）'"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAudit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 确认打款对话框 -->
    <el-dialog title="确认打款" :visible.sync="confirmDialogVisible" width="500px">
      <el-form ref="confirmForm" :model="confirmForm" :rules="confirmRules" label-width="120px">
        <el-form-item label="转账流水号" prop="transaction_id">
          <el-input v-model="confirmForm.transaction_id" placeholder="请输入转账流水号" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="confirmForm.remark" type="textarea" :rows="3" placeholder="请输入备注（选填）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="confirmDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitConfirm">确认打款</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWithdrawDetail, auditWithdraw, confirmWithdraw } from '@/api/profit'

export default {
  name: 'WithdrawDetail',
  data () {
    const validateRemarkForReject = (rule, value, callback) => {
      if (this.auditForm.status === 2 && (!value || value.trim() === '')) {
        callback(new Error('拒绝时，请输入拒绝原因'))
      } else {
        callback()
      }
    }

    return {
      loading: true,
      withdrawId: null,
      detail: {},
      userInfo: {},
      auditDialogVisible: false,
      auditForm: {
        status: 1,
        remark: ''
      },
      auditRules: {
        remark: [
          { validator: validateRemarkForReject, trigger: 'blur' }
        ]
      },
      confirmDialogVisible: false,
      confirmForm: {
        transaction_id: '',
        remark: ''
      },
      confirmRules: {
        transaction_id: [
          { required: true, message: '请输入转账流水号', trigger: 'blur' },
          { min: 5, message: '流水号长度不能少于5个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.withdrawId = parseInt(this.$route.params.id)
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      getWithdrawDetail(this.withdrawId).then(response => {
        const { detail, user_info } = response.data
        this.detail = detail
        this.userInfo = user_info
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    getStatusLabel (status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已拒绝',
        3: '已打款'
      }
      return statusMap[status] || '未知状态'
    },
    getStatusType (status) {
      const typeMap = {
        0: 'info',
        1: 'primary',
        2: 'danger',
        3: 'success'
      }
      return typeMap[status] || 'info'
    },
    goBack () {
      this.$router.push('/profit/withdraw')
    },
    viewUser () {
      this.$router.push(`/user/detail/${this.userInfo.id}`)
    },
    handleAudit (status) {
      this.auditForm = {
        status: status,
        remark: ''
      }
      this.auditDialogVisible = true
    },
    submitAudit () {
      this.$refs.auditForm.validate(valid => {
        if (valid) {
          auditWithdraw(this.withdrawId, {
            status: this.auditForm.status,
            remark: this.auditForm.remark
          }).then(response => {
            this.$message.success(this.auditForm.status === 1 ? '审核通过成功' : '审核拒绝成功')
            this.auditDialogVisible = false
            this.fetchData()
          }).catch(() => {
            this.$message.error('审核操作失败')
          })
        }
      })
    },
    handleConfirm () {
      this.confirmForm = {
        transaction_id: '',
        remark: ''
      }
      this.confirmDialogVisible = true
    },
    submitConfirm () {
      this.$refs.confirmForm.validate(valid => {
        if (valid) {
          confirmWithdraw(this.withdrawId, {
            transaction_id: this.confirmForm.transaction_id,
            remark: this.confirmForm.remark
          }).then(response => {
            this.$message.success('确认打款成功')
            this.confirmDialogVisible = false
            this.fetchData()
          }).catch(() => {
            this.$message.error('确认打款失败')
          })
        }
      })
    }
  }
}
</script>

<style scoped>
.section-title {
  font-size: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
  margin-top: 20px;
  margin-bottom: 15px;
}
.info-section {
  margin-bottom: 20px;
}
.info-item {
  margin-bottom: 10px;
  display: flex;
}
.label {
  font-weight: bold;
  width: 100px;
  color: #606266;
}
.value {
  flex: 1;
  color: #303133;
}
.withdraw-amount {
  font-weight: bold;
  font-size: 18px;
}
.actual-amount {
  color: #67c23a;
  font-weight: bold;
}
.card-number {
  font-family: monospace;
  letter-spacing: 1px;
}
.user-header {
  text-align: center;
  margin-bottom: 20px;
}
.nickname {
  margin: 10px 0;
}
.user-role {
  margin-bottom: 10px;
}
.action-buttons {
  margin-top: 30px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px dashed #ebeef5;
}
</style>
