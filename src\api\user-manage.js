import request from '@/utils/request'

// 本地模拟数据，用于开发测试
const useMock = false  // 禁用模拟数据

// 用户标签的本地存储管理
const USER_TAGS_STORAGE_KEY = 'wifi_admin_user_tags'

// 默认用户标签数据
const defaultUserTagsData = [
  {
    id: 1,
    name: 'VIP用户',
    description: '重要VIP客户',
    created_at: '2023-06-10 10:30:45'
  },
  {
    id: 2,
    name: '新用户',
    description: '注册不满30天的用户',
    created_at: '2023-06-08 14:20:30'
  },
  {
    id: 3,
    name: '商家',
    description: '拥有商铺的用户',
    created_at: '2023-06-05 09:15:00'
  },
  {
    id: 4,
    name: '活跃用户',
    description: '近30天有登录的用户',
    created_at: '2023-06-01 16:40:20'
  }
]

// 获取用户列表
export function getUserList (query) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟数据生成
        const list = []
        const total = query.pageSize * 10 || 100
        
        for (let i = 1; i <= Math.min(query.pageSize || 10, total); i++) {
          const offset = (query.pageNum - 1) * (query.pageSize || 10)
          const index = offset + i
          
          list.push({
            id: index,
            openid: `oMKLx5M2xxxxxxxx${index}`,
            nickname: ['张三', '李四', '王五', '赵六', '刘七'][i % 5] || `用户${index}`,
            avatar: '/img/default-avatar.png',
            gender: i % 3,
            phone: `1380013800${i}`,
            balance: parseFloat((Math.random() * 500).toFixed(2)),
            team_id: i % 3 === 0 ? 1 : (i % 3 === 1 ? 2 : null),
            is_leader: i % 7 === 0 ? 1 : 0,
            level: i % 5,
            status: i % 10 === 0 ? 0 : 1,
            created_at: `2023-05-${String(i % 30 + 1).padStart(2, '0')} 10:20:30`,
            updated_at: `2023-06-${String(i % 28 + 1).padStart(2, '0')} 15:30:45`
          })
        }
        
        resolve({
          code: 200,
          data: {
            list,
            total
          },
          message: '获取用户列表成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/user/list',
    method: 'get',
    params: query
  })
}

// 用户详情
export function getUserDetail (id) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const userId = parseInt(id)
        
        // 基本用户信息
        const userInfo = {
          id: userId,
          openid: `oMKLx5M2xxxxxxxx${id}`,
          nickname: ['', '张三', '李四', '王五', '赵六'][userId] || `用户${id}`,
          avatar: '/img/default-avatar.png',
          gender: userId % 2 === 0 ? 2 : 1,
          phone: `1380013800${id}`,
          balance: parseFloat((Math.random() * 500).toFixed(2)),
          team_id: userId <= 2 ? 1 : (userId === 3 ? 2 : null),
          parent_id: userId === 2 ? 1 : null,
          is_leader: [1, 3].includes(userId) ? 1 : 0,
          level: [1, 3].includes(userId) ? 2 : (userId === 2 ? 1 : 0),
          status: userId === 4 ? 0 : 1,
          created_at: `2023-05-0${userId > 4 ? '1' : userId} 10:20:30`,
          updated_at: `2023-05-0${userId > 4 ? '1' : userId} 15:30:45`
        }
        
        // 团队信息（如果用户有团队）
        let teamInfo = null
        if (userInfo.team_id) {
          teamInfo = {
            id: userInfo.team_id,
            name: `团队${userInfo.team_id}`,
            member_count: Math.floor(Math.random() * 20) + 5,
            wifi_count: Math.floor(Math.random() * 10) + 2,
            total_profit: parseFloat((Math.random() * 5000).toFixed(2))
          }
        }
        
        // WiFi列表（最近的WiFi码）
        const wifiList = [
          {
            id: userId * 10 + 1,
            title: `${userInfo.nickname}的WiFi-1`,
            name: `Wifi_${userId}_1`,
            use_count: Math.floor(Math.random() * 100) + 10,
            created_at: '2023-06-01 09:30:00'
          },
          {
            id: userId * 10 + 2,
            title: `${userInfo.nickname}的WiFi-2`,
            name: `Wifi_${userId}_2`,
            use_count: Math.floor(Math.random() * 100) + 10,
            created_at: '2023-06-02 14:20:00'
          }
        ]
        
        // 订单列表（最近的订单）
        const orderList = [
          {
            id: userId * 100 + 1,
            order_no: `WF${Date.now()}${userId}01`,
            total_amount: parseFloat((Math.random() * 200 + 50).toFixed(2)),
            status: Math.floor(Math.random() * 4) + 1,
            created_at: '2023-06-01 16:45:30'
          },
          {
            id: userId * 100 + 2,
            order_no: `WF${Date.now()}${userId}02`,
            total_amount: parseFloat((Math.random() * 200 + 50).toFixed(2)),
            status: Math.floor(Math.random() * 4) + 1,
            created_at: '2023-06-02 10:15:20'
          }
        ]
        
        resolve({
          code: 200,
          data: {
            user_info: userInfo,
            team_info: teamInfo,
            wifi_list: wifiList,
            order_list: orderList
          },
          message: '获取用户详情成功'
        })
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/user/detail/${id}`,
    method: 'get'
  })
}

// 更新用户
export function updateUser (id, data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: {},
          message: '更新用户成功'
        })
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/user/update/${id}`,
    method: 'put',
    data
  })
}

// 更新用户状态
export function updateUserStatus (id, data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: {},
          message: '更新用户状态成功'
        })
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/user/status/${id}`,
    method: 'put',
    data
  })
}

// 创建用户标签
export function createUserTag (data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前用户标签数据
        const userTagsData = getUserTagsData()
        
        // 生成新的ID
        const newId = userTagsData.length > 0 ? Math.max(...userTagsData.map(tag => tag.id)) + 1 : 1
        
        // 创建新的标签对象
        const newTag = {
          id: newId,
          name: data.name,
          description: data.description || '',
          created_at: new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          }).replace(/\//g, '-')
        }
        
        // 添加到数据存储中
        userTagsData.push(newTag)
        
        // 保存到本地存储
        saveUserTagsData(userTagsData)
        
        resolve({
          code: 200,
          data: { id: newId },
          message: '创建用户标签成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/user/tag/create',
    method: 'post',
    data
  })
}

// 更新用户标签
export function updateUserTag (id, data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前用户标签数据
        const userTagsData = getUserTagsData()
        
        // 查找标签索引
        const tagIndex = userTagsData.findIndex(tag => tag.id === parseInt(id))
        
        if (tagIndex !== -1) {
          // 更新标签
          userTagsData[tagIndex].name = data.name
          userTagsData[tagIndex].description = data.description || userTagsData[tagIndex].description
          
          // 保存到本地存储
          saveUserTagsData(userTagsData)
        
        resolve({
          code: 200,
          message: '更新用户标签成功'
        })
        } else {
          resolve({
            code: 404,
            message: '标签不存在'
          })
        }
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/user/tag/update/${id}`,
    method: 'put',
    data
  })
}

// 删除用户标签
export function deleteUserTag (id) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前用户标签数据
        const userTagsData = getUserTagsData()
        
        // 查找标签索引
        const tagIndex = userTagsData.findIndex(tag => tag.id === parseInt(id))
        
        if (tagIndex !== -1) {
          // 删除标签
          userTagsData.splice(tagIndex, 1)
          
          // 保存到本地存储
          saveUserTagsData(userTagsData)
        
        resolve({
          code: 200,
          message: '删除用户标签成功'
        })
        } else {
          resolve({
            code: 404,
            message: '标签不存在'
          })
        }
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/user/tag/delete/${id}`,
    method: 'delete'
  })
}

// 获取用户标签数据（从本地存储读取，如果没有则使用默认数据）
function getUserTagsData() {
  try {
    const stored = localStorage.getItem(USER_TAGS_STORAGE_KEY)
    return stored ? JSON.parse(stored) : defaultUserTagsData
  } catch (error) {
    console.warn('读取用户标签数据失败，使用默认数据:', error)
    return defaultUserTagsData
  }
}

// 保存用户标签数据到本地存储
function saveUserTagsData(data) {
  try {
    localStorage.setItem(USER_TAGS_STORAGE_KEY, JSON.stringify(data))
  } catch (error) {
    console.error('保存用户标签数据失败:', error)
  }
}

// 获取用户标签列表
export function getUserTagList () {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const userTagsData = getUserTagsData()
        resolve({
          code: 200,
          data: [...userTagsData],
          message: '获取用户标签列表成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/user/tag/list',
    method: 'get'
  })
}

// 获取用户统计数据
export function getUserStats () {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: {
            total_users: 1256,
            active_users: 1180,
            leader_users: 45,
            today_users: 23,
            month_users: 187,
            total_balance: 125680.50,
            avg_balance: 106.51
          },
          message: '获取用户统计成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/user/stats',
    method: 'get'
  })
}

// 调整用户余额
export function adjustUserBalance (id, data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: {
            before_balance: 100.00,
            after_balance: data.type === 'add' ? 100 + parseFloat(data.amount) : 100 - parseFloat(data.amount)
          },
          message: '余额调整成功'
        })
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/user/balance/${id}`,
    method: 'post',
    data
  })
}

// 批量操作用户
export function batchOperateUsers (data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: {},
          message: '批量操作成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/user/batch',
    method: 'post',
    data
  })
}

// 分配用户标签
export function assignUserTags (id, data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: {},
          message: '分配标签成功'
        })
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/user/assign-tags/${id}`,
    method: 'post',
    data
  })
}

// 获取用户消费统计
export function getUserConsumptionStats (id) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: {
            total_orders: Math.floor(Math.random() * 50) + 10,
            total_amount: parseFloat((Math.random() * 5000 + 1000).toFixed(2)),
            avg_order_amount: parseFloat((Math.random() * 200 + 50).toFixed(2)),
            last_order_time: '2023-06-15 14:30:20',
            favorite_category: '数码产品',
            monthly_stats: [
              { month: '2023-01', orders: 3, amount: 450.00 },
              { month: '2023-02', orders: 5, amount: 680.50 },
              { month: '2023-03', orders: 2, amount: 320.00 },
              { month: '2023-04', orders: 4, amount: 590.30 },
              { month: '2023-05', orders: 6, amount: 780.20 },
              { month: '2023-06', orders: 3, amount: 420.00 }
            ]
          },
          message: '获取消费统计成功'
        })
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/user/consumption-stats/${id}`,
    method: 'get'
  })
}

// 获取用户WiFi统计
export function getUserWifiStats (id) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: {
            total_wifi: Math.floor(Math.random() * 20) + 5,
            total_scans: Math.floor(Math.random() * 1000) + 100,
            avg_scans_per_wifi: Math.floor(Math.random() * 50) + 10,
            most_popular_wifi: {
              id: 1,
              title: '星巴克WiFi',
              scan_count: 156
            },
            recent_activity: [
              { date: '2023-06-15', scans: 23 },
              { date: '2023-06-14', scans: 18 },
              { date: '2023-06-13', scans: 31 },
              { date: '2023-06-12', scans: 25 },
              { date: '2023-06-11', scans: 19 }
            ]
          },
          message: '获取WiFi统计成功'
        })
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/user/wifi-stats/${id}`,
    method: 'get'
  })
}
