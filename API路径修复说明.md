# WiFi共享管理后台 - API路径重复问题修复说明

## 🚨 问题描述

在生产环境中出现API路径重复问题：
- **错误路径**：`POST /api/api/v1/admin/auth/admin-login`
- **正确路径**：`POST /api/v1/admin/auth/admin-login`

从日志可以看出：
```
[2025-07-28T13:33:10.178Z] [INFO] POST /api/api/v1/admin/auth/admin-login
[2025-07-28T13:33:10.178Z] [WARN] File not found: /api/api/v1/admin/auth/admin-login, serving index.html
```

## 🔧 解决方案

### 1. 修复前端配置

已修复 `src/utils/request.js` 中的baseURL配置：

```javascript
// 修复前（有问题的配置）
const service = axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? '/api' : '/',
  // ...
})

// 修复后（正确的配置）
const service = axios.create({
  baseURL: '/',  // 统一使用相对路径，避免路径重复
  // ...
})
```

### 2. 配置Nginx代理

在宝塔面板中配置网站，使用以下Nginx配置：

```nginx
server {
    listen 80;
    server_name **************;  # 替换为您的域名或IP
    
    # 前端静态文件
    location / {
        root /www/wwwroot/wifi-share-admin/dist;
        try_files $uri $uri/ /index.html;
        index index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理到后端服务
    location /api/ {
        proxy_pass http://localhost:4000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 增加超时时间
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 支持大文件上传
        client_max_body_size 50M;
        
        # 添加CORS头
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # 上传文件代理
    location /uploads/ {
        proxy_pass http://localhost:4000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 日志配置
    access_log /www/wwwlogs/wifi-share-admin-access.log;
    error_log /www/wwwlogs/wifi-share-admin-error.log;
}
```

## 📋 部署步骤

### 1. 重新构建前端项目

```bash
cd /www/wwwroot/wifi-share-admin
npm run build
```

### 2. 在宝塔面板中配置网站

1. 点击"网站" -> "添加站点"
2. 域名填写：`**************`（或您的域名）
3. 根目录：`/www/wwwroot/wifi-share-admin/dist`
4. 点击"设置" -> "配置文件"，替换为上面的Nginx配置
5. 点击"保存"并重载Nginx

### 3. 确保后端服务运行

```bash
cd /www/wwwroot/wifi-share-server
pm2 status
# 如果没有运行，启动后端服务
pm2 start app.js --name wifi-share-server
```

### 4. 测试访问

访问：`http://**************/login`

使用账号：`mrx0927` / `hh20250701`

## 🔍 验证修复

### 1. 检查API请求路径

打开浏览器开发者工具，在Network标签中查看登录请求：
- 应该看到：`POST /api/v1/admin/auth/admin-login`
- 而不是：`POST /api/api/v1/admin/auth/admin-login`

### 2. 检查后端日志

```bash
# 查看后端服务日志
pm2 logs wifi-share-server

# 查看Nginx访问日志
tail -f /www/wwwlogs/wifi-share-admin-access.log
```

### 3. 检查服务状态

```bash
# 检查端口占用
netstat -tlnp | grep 4000

# 检查PM2状态
pm2 status
```

## 🚨 故障排查

如果仍然有问题，请检查：

1. **后端服务是否正常运行**：
   ```bash
   curl http://localhost:4000/api/v1/admin/auth/admin-login
   ```

2. **Nginx配置是否正确**：
   ```bash
   nginx -t
   nginx -s reload
   ```

3. **防火墙设置**：
   确保4000端口在服务器内部可访问

4. **查看详细错误日志**：
   ```bash
   tail -f /www/wwwlogs/wifi-share-admin-error.log
   ```

修复完成后，API路径应该正常工作，登录功能应该可以正常使用。
