const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const db = require('../database');
const platformStatsController = require('../controllers/platform-stats');

/**
 * 平台收益统计路由
 */

// 获取平台收益概览
router.get('/revenue', verifyToken, async (req, res) => {
  try {
    // 验证管理员权限
    if (req.user.role !== 'admin') {
      return error(res, '无权限访问', 403);
    }

    // 模拟数据，实际应该从数据库查询
    const overview = {
      total_revenue: 125000.50,
      today_revenue: 2500.30,
      platform_revenue: 50000.20,
      leader_revenue: 75000.30,
      user_revenue: 0
    };

    success(res, overview, '获取平台收益概览成功');
  } catch (err) {
    console.error('获取平台收益概览失败:', err);
    error(res, '获取平台收益概览失败');
  }
});

// 获取收益趋势
router.get('/revenue-trend', verifyToken, async (req, res) => {
  try {
    // 验证管理员权限
    if (req.user.role !== 'admin') {
      return error(res, '无权限访问', 403);
    }

    const { period = '7d', start_date, end_date } = req.query;

    // 模拟趋势数据
    const trendData = [];
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 90;

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      trendData.push({
        date: date.toISOString().split('T')[0],
        platform_amount: Math.random() * 1000 + 500,
        leader_amount: Math.random() * 1500 + 750,
        user_amount: 0,
        total_amount: Math.random() * 2500 + 1250
      });
    }

    success(res, trendData, '获取收益趋势成功');
  } catch (err) {
    console.error('获取收益趋势失败:', err);
    error(res, '获取收益趋势失败');
  }
});

// 获取平台概览
router.get('/overview', verifyToken, platformStatsController.getPlatformOverview);

// 获取业务类型分布
router.get('/business-distribution', verifyToken, async (req, res) => {
  try {
    // 验证管理员权限
    if (req.user.role !== 'admin') {
      return error(res, '无权限访问', 403);
    }

    // 模拟业务分布数据
    const businessData = [
      {
        business_type: 'wifi_share',
        total_amount: 75000.30,
        transaction_count: 1200
      },
      {
        business_type: 'goods_sale',
        total_amount: 45000.20,
        transaction_count: 800
      },
      {
        business_type: 'advertisement',
        total_amount: 5000.00,
        transaction_count: 150
      }
    ];

    success(res, businessData, '获取业务类型分布成功');
  } catch (err) {
    console.error('获取业务类型分布失败:', err);
    error(res, '获取业务类型分布失败');
  }
});

// 获取财务报表
router.get('/financial-report', verifyToken, async (req, res) => {
  try {
    // 验证管理员权限
    if (req.user.role !== 'admin') {
      return error(res, '无权限访问', 403);
    }

    const { period = '7d' } = req.query;

    // 模拟财务报表数据
    const reportData = [];
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 90;

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      ['wifi_share', 'goods_sale', 'advertisement'].forEach(businessType => {
        const totalAmount = Math.random() * 1000 + 200;
        reportData.push({
          date: date.toISOString().split('T')[0],
          business_type: businessType,
          total_amount: totalAmount,
          platform_amount: totalAmount * 0.4,
          leader_amount: totalAmount * 0.6,
          user_amount: 0,
          transaction_count: Math.floor(Math.random() * 20) + 5,
          avg_amount: totalAmount / (Math.floor(Math.random() * 20) + 5)
        });
      });
    }

    success(res, reportData, '获取财务报表成功');
  } catch (err) {
    console.error('获取财务报表失败:', err);
    error(res, '获取财务报表失败');
  }
});

// 获取用户增长统计
router.get('/user-growth', verifyToken, platformStatsController.getUserGrowth);

// 获取WiFi统计
router.get('/wifi-stats', verifyToken, async (req, res) => {
  try {
    // 验证管理员权限
    if (req.user.role !== 'admin') {
      return error(res, '无权限访问', 403);
    }

    const { period = '30d' } = req.query;

    // 模拟WiFi统计数据
    const wifiData = [];
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 90;

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      wifiData.push({
        date: date.toISOString().split('T')[0],
        new_codes: Math.floor(Math.random() * 15) + 3,
        active_codes: Math.floor(Math.random() * 80) + 40,
        total_views: Math.floor(Math.random() * 200) + 100,
        total_codes: 800 + i * 5 + Math.floor(Math.random() * 20)
      });
    }

    success(res, wifiData, '获取WiFi统计成功');
  } catch (err) {
    console.error('获取WiFi统计失败:', err);
    error(res, '获取WiFi统计失败');
  }
});

// 获取订单统计
router.get('/order-stats', verifyToken, async (req, res) => {
  try {
    // 验证管理员权限
    if (req.user.role !== 'admin') {
      return error(res, '无权限访问', 403);
    }

    const { period = '30d' } = req.query;

    // 模拟订单统计数据
    const orderData = [];
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 90;

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      const totalOrders = Math.floor(Math.random() * 30) + 10;
      const paidOrders = Math.floor(totalOrders * 0.85);

      orderData.push({
        date: date.toISOString().split('T')[0],
        total_orders: totalOrders,
        paid_orders: paidOrders,
        pending_orders: totalOrders - paidOrders,
        revenue: paidOrders * (Math.random() * 100 + 50),
        avg_order_value: Math.random() * 100 + 50
      });
    }

    success(res, orderData, '获取订单统计成功');
  } catch (err) {
    console.error('获取订单统计失败:', err);
    error(res, '获取订单统计失败');
  }
});

// 获取业务类型统计
router.get('/business-type', verifyToken, platformStatsController.getBusinessTypeStats);

// 获取地区统计
router.get('/region', verifyToken, platformStatsController.getRegionStats);

module.exports = router;
