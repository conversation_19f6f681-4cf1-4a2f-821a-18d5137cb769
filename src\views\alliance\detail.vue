<template>
  <div class="app-container">
    <el-card class="box-card" v-loading="loading">
      <div slot="header" class="clearfix">
        <span>申请详情</span>
        <el-button-group style="float: right;">
          <el-button v-if="detail.status === 0" type="success" size="mini" @click="handleApprove">通过申请</el-button>
          <el-button v-if="detail.status === 0" type="danger" size="mini" @click="handleReject">拒绝申请</el-button>
          <el-button type="primary" size="mini" @click="goBack">返回列表</el-button>
        </el-button-group>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <h4 class="detail-title">申请信息</h4>
          <el-descriptions class="margin-top" :column="1" border>
            <el-descriptions-item label="申请ID">{{ detail.id }}</el-descriptions-item>
            <el-descriptions-item label="团队名称">{{ detail.name }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ detail.phone }}</el-descriptions-item>
            <el-descriptions-item label="区域">{{ detail.area }}</el-descriptions-item>
            <el-descriptions-item label="申请时间">
              {{ formatDate(detail.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="申请状态">
              <el-tag :type="detail.status | statusFilter">{{ detail.status | statusTextFilter }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="备注">{{ detail.remark || '无' }}</el-descriptions-item>
            <el-descriptions-item label="简介">
              <div class="description-content">{{ detail.description || '无' }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        
        <el-col :span="12">
          <h4 class="detail-title">用户信息</h4>
          <el-descriptions class="margin-top" :column="1" border>
            <el-descriptions-item label="用户ID">{{ detail.user_id }}</el-descriptions-item>
            <el-descriptions-item label="用户昵称">{{ detail.nickname || '未设置昵称' }}</el-descriptions-item>
            <el-descriptions-item label="用户头像">
              <el-avatar :size="60" :src="detail.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" />
            </el-descriptions-item>
            <el-descriptions-item label="手机号码">{{ detail.user_phone || '未绑定手机' }}</el-descriptions-item>
            <el-descriptions-item label="性别">
              {{ detail.gender === 1 ? '男' : detail.gender === 2 ? '女' : '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ formatDate(detail.user_created_at) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </el-card>

    <!-- 审核对话框 -->
    <el-dialog :title="auditDialogTitle" :visible.sync="auditDialogVisible" width="30%">
      <el-form :model="auditForm" label-width="80px">
        <el-form-item label="备注">
          <el-input v-model="auditForm.remark" type="textarea" :rows="4" placeholder="请输入审核备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAudit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchAllianceDetail, auditAlliance } from '@/api/alliance'

export default {
  name: 'AllianceDetail',
  filters: {
    statusFilter(status) {
      const statusMap = {
        0: 'info',
        1: 'success',
        2: 'danger'
      }
      return statusMap[status]
    },
    statusTextFilter(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已拒绝'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      loading: false,
      detail: {
        id: null,
        user_id: null,
        name: '',
        phone: '',
        area: '',
        description: '',
        status: 0,
        remark: '',
        created_at: '',
        updated_at: '',
        nickname: '',
        avatar: '',
        user_phone: '',
        gender: 0,
        user_created_at: ''
      },
      auditDialogVisible: false,
      auditDialogTitle: '审核',
      auditForm: {
        id: null,
        status: null,
        remark: ''
      }
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      const id = this.$route.params.id
      fetchAllianceDetail(id).then(response => {
        this.detail = response.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    goBack() {
      this.$router.push({ path: '/alliance/list' })
    },
    handleApprove() {
      this.auditDialogTitle = '通过申请'
      this.auditForm = {
        id: this.detail.id,
        status: 1,
        remark: ''
      }
      this.auditDialogVisible = true
    },
    handleReject() {
      this.auditDialogTitle = '拒绝申请'
      this.auditForm = {
        id: this.detail.id,
        status: 2,
        remark: ''
      }
      this.auditDialogVisible = true
    },
    submitAudit() {
      auditAlliance(this.auditForm.id, {
        status: this.auditForm.status,
        remark: this.auditForm.remark
      }).then(response => {
        this.$message({
          type: 'success',
          message: '审核成功!'
        })
        this.auditDialogVisible = false
        this.fetchData()
      })
    },
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hour = String(d.getHours()).padStart(2, '0')
      const minute = String(d.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hour}:${minute}`
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-title {
  margin-top: 0;
  margin-bottom: 20px;
  color: #303133;
  font-size: 16px;
  font-weight: bold;
}
.description-content {
  white-space: pre-line;
  line-height: 1.5;
}
</style> 