// pages/index/index.js
// 首页页面逻辑

const request = require('../../utils/request.js')
const util = require('../../utils/util.js')
const api = require('../../config/api.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    isLogin: false,
    
    // 今日日期
    todayDate: '',
    
    // 轮播图数据
    bannerList: [],
    
    // 快捷功能
    quickActions: [
      {
        id: 1,
        title: '创建WiFi码',
        subtitle: '快速生成分享码',
        icon: '/assets/icons/wifi-create.png',
        url: '/pages/wifi/create/create',
        bgColor: '#07c160'
      },
      {
        id: 2,
        title: '我的WiFi码',
        subtitle: '管理已创建的码',
        icon: '/assets/icons/wifi-manage.png',
        url: '/pages/wifi/list/list',
        bgColor: '#1890ff'
      }
    ],
    
    // 广告数据
    adData: {
      title: '推广广告位',
      subtitle: '点击查看详情',
      image: '/assets/images/ad-banner.jpg',
      link: '/pages/ads/index'
    },
    
    // 统计数据
    statsData: {
      todayIncome: 0,      // 今日收益
      totalWifiCodes: 0,   // WiFi码总数
      totalUsers: 0,       // 团队人数
      totalIncome: 0       // 累计收益
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('首页加载，参数:', options)

    // 处理邀请码
    if (options.inviteCode) {
      console.log('检测到邀请码:', options.inviteCode)
      this.handleInviteCode(options.inviteCode)
      return
    }

    this.initPageData()
    this.checkLoginStatus()
    this.loadPageData()
  },

  /**
   * 处理邀请码
   */
  handleInviteCode: function (inviteCode) {
    // 跳转到邀请页面
    wx.redirectTo({
      url: `/pages/invite/invite?inviteCode=${inviteCode}`
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('首页显示')
    this.checkLoginStatus()
    // 刷新统计数据
    if (this.data.isLogin) {
      this.loadStatsData()
    }
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 设置今日日期
    const today = new Date()
    const todayStr = util.formatDate(today, 'MM-DD')
    this.setData({
      todayDate: todayStr
    })
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp()
    const isLogin = app.globalData.isLogin
    const userInfo = app.globalData.userInfo
    
    this.setData({
      isLogin: isLogin,
      userInfo: userInfo
    })
  },

  /**
   * 加载页面数据
   */
  loadPageData() {
    // 加载轮播图（安全方式）
    this.safeLoadBannerList()
    
    // 如果已登录，加载统计数据
    if (this.data.isLogin) {
      this.loadStatsData()
    }
  },

  /**
   * 安全加载轮播图数据（添加错误处理）
   */
  safeLoadBannerList() {
    try {
      this.loadBannerList().catch(err => {
        console.error('加载轮播图出错:', err)
        this.setDefaultBanners()
      })
    } catch (error) {
      console.error('轮播图加载异常:', error)
      this.setDefaultBanners()
    }
  },

  /**
   * 加载轮播图数据
   */
  async loadBannerList() {
    try {
      console.log('开始从API获取轮播图数据');
      const result = await request.get('/api/v1/client/ads/banner')
      
      if (result && result.data && Array.isArray(result.data) && result.data.length > 0) {
        console.log('成功获取轮播图数据:', result.data)
        
        // 处理图片路径，确保是完整URL
        let banners = result.data.map(banner => {
          return {
            ...banner,
            image: util.formatImageUrl(banner.image)
          }
        })
        
        this.setData({
          bannerList: banners
        })
        console.log('轮播图数据设置成功:', banners)
      } else {
        console.log('轮播图数据为空或无效，使用默认数据')
        this.setDefaultBanners()
      }
    } catch (error) {
      console.error('加载轮播图失败:', error)
      // 加载失败时使用默认数据
      this.setDefaultBanners()
      throw error // 向上传递错误以便外层捕获
    }
  },
  
  /**
   * 设置默认轮播图数据
   */
  setDefaultBanners() {
    console.log('设置默认轮播图数据')
    const defaultBanners = [
      {
        id: 1,
        title: 'WiFi共享赚钱',
        desc: '生成WiFi码，分享即可获得收益',
        image: '/assets/images/banner1.jpg',
        link: '',
        type: 'page'
      },
      {
        id: 2,
        title: '商城优惠活动',
        desc: '精选好物，限时优惠等你来抢',
        image: '/assets/images/banner2.jpg',
        link: '/pages/mall/home/<USER>',
        type: 'page'
      },
      {
        id: 3,
        title: '联盟招募中',
        desc: '成为团长，开启创业新征程',
        image: '/assets/images/banner3.jpg',
        link: '/pages/alliance/index',
        type: 'page'
      }
    ]
    
    console.log('默认轮播图数据:', defaultBanners)
    
    // 使用setTimeout确保在微信内部渲染逻辑完成后更新数据
    setTimeout(() => {
    this.setData({
      bannerList: defaultBanners
    })
      console.log('默认轮播图数据设置完成')
    }, 0)
  },

  /**
   * 加载统计数据
   */
  async loadStatsData() {
    try {
      // 模拟从API获取统计数据
      // const result = await request.get('/user/stats')
      // if (result.success) {
      //   this.setData({
      //     statsData: result.data
      //   })
      // }
      
      // 使用模拟数据
      const statsData = {
        todayIncome: 12.34,
        totalWifiCodes: 5,
        totalUsers: 12,
        totalIncome: 234.56
      }
      
      this.setData({
        statsData: statsData
      })
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  },

  /**
   * 轮播图点击事件
   */
  onBannerTap(e) {
    const item = e.currentTarget.dataset.item
    if (!item || !item.link) return
    
    if (item.type === 'page') {
      wx.navigateTo({
        url: item.link,
        fail: () => {
          wx.switchTab({
            url: item.link
          })
        }
      })
    } else if (item.type === 'url') {
      wx.showToast({
        title: '暂不支持外部链接',
        icon: 'none'
      })
    }
  },

  /**
   * 快捷功能点击事件
   */
  onQuickActionTap(e) {
    const item = e.currentTarget.dataset.item
    
    if (!item) {
      // 处理直接传入的action对象
      const actionIndex = e.currentTarget.dataset.index || 0
      item = this.data.quickActions[actionIndex]
    }
    
    // 检查是否需要登录
    if (item && item.id === 2 && !this.data.isLogin) {
      this.showLoginTip()
      return
    }
    
    if (item && item.url) {
      wx.navigateTo({
        url: item.url
      })
    }
  },

  /**
   * 联盟入驻点击事件
   */
  onAllianceTap() {
    if (!this.data.isLogin) {
      this.showLoginTip()
      return
    }
    
    wx.navigateTo({
      url: '/pages/alliance/index'
    })
  },

  /**
   * 广告点击事件
   */
  onAdTap() {
    const { adData } = this.data
    if (adData.link) {
      wx.navigateTo({
        url: adData.link,
        fail: () => {
          wx.showToast({
            title: '页面开发中',
            icon: 'none'
          })
        }
      })
    } else {
      wx.showToast({
        title: '广告功能开发中',
        icon: 'none'
      })
    }
  },

  /**
   * 登录按钮点击事件
   */
  onLoginTap() {
    this.handleWechatLogin()
  },

  /**
   * 处理微信登录
   */
  async handleWechatLogin() {
    try {
      // 显示加载提示
    wx.showLoading({
        title: '登录中...',
        mask: true
    })

      // 获取用户信息
      const userInfoRes = await new Promise((resolve, reject) => {
    wx.getUserProfile({
          desc: '用于完善会员资料',
          success: res => resolve(res),
          fail: err => reject(err)
        })
      })
      
      console.log('获取到用户信息:', userInfoRes)
        
      // 获取应用实例
    const app = getApp()
      
      // 使用用户信息进行登录
      const loginRes = await app.doLoginWithUserInfo(userInfoRes.userInfo)
      
      console.log('登录结果:', loginRes)
      
      // 隐藏加载提示
      wx.hideLoading()
      
      if (loginRes && loginRes.status === 'success') {
        // 更新页面数据
    this.setData({
      isLogin: true,
          userInfo: loginRes.data.user
    })
    
    // 加载统计数据
    this.loadStatsData()
    
    wx.showToast({
      title: '登录成功',
          icon: 'success',
          duration: 2000
        })
      } else {
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none',
          duration: 2000
        })
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      
      // 隐藏加载提示
      wx.hideLoading()
      
      // 如果是用户取消，不显示错误提示
      if (error.errMsg && error.errMsg.indexOf('cancel') > -1) {
        console.log('用户取消登录')
        return
      }
      
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none',
        duration: 2000
    })
    }
  },

  /**
   * 显示登录提示
   */
  showLoginTip() {
    wx.showModal({
      title: '提示',
      content: '此功能需要登录后才能使用，是否立即登录？',
      success: (res) => {
        if (res.confirm) {
          this.handleWechatLogin()
        }
      }
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新触发')
    // 使用setTimeout确保组件已完全准备好
    setTimeout(() => {
      // 先设置一个默认数据，防止数据为空时出错
      this.setDefaultBanners()
      // 然后再尝试加载实际数据
      this.loadPageData()
      
      // 延迟停止下拉刷新动画
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1500)
    }, 100)
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: 'WiFi共享商城 - 轻松赚钱新方式',
      path: '/pages/index/index',
      imageUrl: '/assets/images/share-home.jpg'
    }
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: 'WiFi共享商城 - 轻松赚钱新方式',
      imageUrl: '/assets/images/share-home.jpg'
    }
  },

  /**
   * 图片加载失败处理
   */
  onImageError(e) {
    const index = e.currentTarget.dataset.index
    if (index !== undefined && this.data.bannerList[index]) {
      // 创建一个新的数组副本
      const updatedBanners = [...this.data.bannerList]
      
      // 替换出错的图片URL为本地默认图片
      updatedBanners[index].image = `/assets/images/banner${(index % 3) + 1}.jpg`
      
      this.setData({
        bannerList: updatedBanners
      })
    }
  }
}) 