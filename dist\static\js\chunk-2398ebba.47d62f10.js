(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2398ebba"],{2628:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"app-container"},[t("el-tabs",{model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[t("el-tab-pane",{attrs:{label:"操作日志",name:"operation"}},[t("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.queryParams}},[t("el-form-item",{attrs:{label:"管理员"}},[t("el-input",{attrs:{placeholder:"管理员ID",clearable:""},model:{value:e.queryParams.adminId,callback:function(t){e.$set(e.queryParams,"adminId",t)},expression:"queryParams.adminId"}})],1),t("el-form-item",{attrs:{label:"模块"}},[t("el-input",{attrs:{placeholder:"模块名称",clearable:""},model:{value:e.queryParams.module,callback:function(t){e.$set(e.queryParams,"module",t)},expression:"queryParams.module"}})],1),t("el-form-item",{attrs:{label:"操作"}},[t("el-input",{attrs:{placeholder:"操作类型",clearable:""},model:{value:e.queryParams.action,callback:function(t){e.$set(e.queryParams,"action",t)},expression:"queryParams.action"}})],1),t("el-form-item",{attrs:{label:"时间范围"}},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.handleQuery}},[e._v("查询")]),t("el-button",{on:{click:e.resetQuery}},[e._v("重置")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.operationLogList,border:""}},[t("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),t("el-table-column",{attrs:{prop:"adminId",label:"管理员ID",width:"100"}}),t("el-table-column",{attrs:{prop:"module",label:"模块",width:"120"}}),t("el-table-column",{attrs:{prop:"action",label:"操作",width:"120"}}),t("el-table-column",{attrs:{prop:"ip",label:"IP地址",width:"120"}}),t("el-table-column",{attrs:{prop:"content",label:"操作内容"}}),t("el-table-column",{attrs:{prop:"created_at",label:"创建时间",width:"180"}})],1),t("div",{staticClass:"pagination-container"},[t("el-pagination",{attrs:{"current-page":e.queryParams.page,"page-sizes":[10,20,30,50],"page-size":e.queryParams.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-tab-pane",{attrs:{label:"登录日志",name:"login"}},[t("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.queryParams}},[t("el-form-item",{attrs:{label:"用户ID"}},[t("el-input",{attrs:{placeholder:"用户ID",clearable:""},model:{value:e.queryParams.userId,callback:function(t){e.$set(e.queryParams,"userId",t)},expression:"queryParams.userId"}})],1),t("el-form-item",{attrs:{label:"用户类型"}},[t("el-select",{attrs:{placeholder:"用户类型",clearable:""},model:{value:e.queryParams.userType,callback:function(t){e.$set(e.queryParams,"userType",t)},expression:"queryParams.userType"}},[t("el-option",{attrs:{label:"管理员",value:1}}),t("el-option",{attrs:{label:"小程序用户",value:2}})],1)],1),t("el-form-item",{attrs:{label:"状态"}},[t("el-select",{attrs:{placeholder:"状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[t("el-option",{attrs:{label:"成功",value:1}}),t("el-option",{attrs:{label:"失败",value:0}})],1)],1),t("el-form-item",{attrs:{label:"时间范围"}},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.handleQuery}},[e._v("查询")]),t("el-button",{on:{click:e.resetQuery}},[e._v("重置")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.loginLogList,border:""}},[t("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),t("el-table-column",{attrs:{prop:"user_type",label:"用户类型",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(1===t.row.user_type?"管理员":"小程序用户")+" ")]}}])}),t("el-table-column",{attrs:{prop:"user_id",label:"用户ID",width:"100"}}),t("el-table-column",{attrs:{prop:"ip",label:"IP地址",width:"120"}}),t("el-table-column",{attrs:{prop:"device",label:"设备信息"}}),t("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:1===a.row.status?"success":"danger"}},[e._v(" "+e._s(1===a.row.status?"成功":"失败")+" ")])]}}])}),t("el-table-column",{attrs:{prop:"created_at",label:"登录时间",width:"180"}})],1),t("div",{staticClass:"pagination-container"},[t("el-pagination",{attrs:{"current-page":e.queryParams.page,"page-sizes":[10,20,30,50],"page-size":e.queryParams.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-tab-pane",{attrs:{label:"异常日志",name:"error"}},[t("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e.queryParams}},[t("el-form-item",{attrs:{label:"模块"}},[t("el-input",{attrs:{placeholder:"模块名称",clearable:""},model:{value:e.queryParams.module,callback:function(t){e.$set(e.queryParams,"module",t)},expression:"queryParams.module"}})],1),t("el-form-item",{attrs:{label:"时间范围"}},[t("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.handleQuery}},[e._v("查询")]),t("el-button",{on:{click:e.resetQuery}},[e._v("重置")])],1)],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.errorLogList,border:""}},[t("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),t("el-table-column",{attrs:{prop:"module",label:"模块",width:"120"}}),t("el-table-column",{attrs:{prop:"level",label:"级别",width:"100"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-tag",{attrs:{type:e.getErrorLevelType(a.row.level)}},[e._v(" "+e._s(a.row.level)+" ")])]}}])}),t("el-table-column",{attrs:{prop:"message",label:"错误信息"}}),t("el-table-column",{attrs:{prop:"stack",label:"堆栈信息"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text"},on:{click:function(t){return e.showStackInfo(a.row)}}},[e._v("查看")])]}}])}),t("el-table-column",{attrs:{prop:"created_at",label:"创建时间",width:"180"}})],1),t("div",{staticClass:"pagination-container"},[t("el-pagination",{attrs:{"current-page":e.queryParams.page,"page-sizes":[10,20,30,50],"page-size":e.queryParams.limit,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1)],1),t("el-dialog",{attrs:{title:"堆栈信息",visible:e.stackDialogVisible,width:"70%"},on:{"update:visible":function(t){e.stackDialogVisible=t}}},[t("pre",[e._v(e._s(e.currentStackInfo))])])],1)},o=[],n=a("8593"),s={name:"LogManagement",data(){return{activeTab:"operation",loading:!1,dateRange:[],queryParams:{page:1,limit:10,adminId:void 0,module:void 0,action:void 0,userId:void 0,userType:void 0,status:void 0,startDate:void 0,endDate:void 0},total:0,operationLogList:[],loginLogList:[],errorLogList:[],stackDialogVisible:!1,currentStackInfo:""}},created(){this.getList()},watch:{activeTab(){this.resetQuery(),this.getList()}},methods:{getList(){this.loading=!0,this.dateRange&&2===this.dateRange.length&&(this.queryParams.startDate=this.dateRange[0],this.queryParams.endDate=this.dateRange[1]);const e={operation:n["h"],login:n["g"],error:n["f"]},t=e[this.activeTab];t&&t(this.queryParams).then(e=>{"operation"===this.activeTab?this.operationLogList=e.data.list:"login"===this.activeTab?this.loginLogList=e.data.list:"error"===this.activeTab&&(this.errorLogList=e.data.list),this.total=e.data.total,this.loading=!1}).catch(()=>{this.loading=!1})},handleQuery(){this.queryParams.page=1,this.getList()},resetQuery(){this.dateRange=[],this.queryParams={page:1,limit:10,adminId:void 0,module:void 0,action:void 0,userId:void 0,userType:void 0,status:void 0,startDate:void 0,endDate:void 0}},handleSizeChange(e){this.queryParams.limit=e,this.getList()},handleCurrentChange(e){this.queryParams.page=e,this.getList()},getErrorLevelType(e){const t={ERROR:"danger",WARNING:"warning",INFO:"info",DEBUG:"success"};return t[e]||"info"},showStackInfo(e){this.currentStackInfo=e.stack||"无堆栈信息",this.stackDialogVisible=!0}}},i=s,l=(a("8763"),a("2877")),c=Object(l["a"])(i,r,o,!1,null,"136ea192",null);t["default"]=c.exports},8593:function(e,t,a){"use strict";a.d(t,"j",(function(){return u})),a.d(t,"m",(function(){return m})),a.d(t,"i",(function(){return f})),a.d(t,"b",(function(){return b})),a.d(t,"l",(function(){return _})),a.d(t,"d",(function(){return v})),a.d(t,"e",(function(){return y})),a.d(t,"a",(function(){return w})),a.d(t,"k",(function(){return I})),a.d(t,"c",(function(){return S})),a.d(t,"h",(function(){return P})),a.d(t,"g",(function(){return M})),a.d(t,"f",(function(){return T}));a("d9e2"),a("14d9"),a("e9f5"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("a732");var r=a("b775");const o=!1,n=[{id:1,name:"超级管理员",code:"super_admin",description:"拥有系统所有权限",permissions:["*"],status:1,created_at:"2024-01-01 00:00:00",updated_at:"2024-01-01 00:00:00"},{id:2,name:"运营管理员",code:"operation_admin",description:"负责日常运营管理",permissions:["wifi:*","user:*","order:*","ad:*"],status:1,created_at:"2024-01-15 10:00:00",updated_at:"2024-12-20 15:00:00"}],s=[{id:1,username:"admin",nickname:"超级管理员",email:"<EMAIL>",phone:"13800138000",avatar:'data:image/svg+xml,%3Csvg width="100" height="100" xmlns="http://www.w3.org/2000/svg"%3E%3Ccircle cx="50" cy="50" r="50" fill="%234A90E2"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="white" font-size="40"%3EA%3C/text%3E%3C/svg%3E',role_id:1,role_name:"超级管理员",status:1,last_login_time:"2025-7-7 14:00:00",last_login_ip:"127.0.0.1",created_at:"2024-01-01 00:00:00",updated_at:"2025-7-7 14:00:00"},{id:2,username:"operation",nickname:"运营小王",email:"<EMAIL>",phone:"***********",avatar:"",role_id:2,role_name:"运营管理员",status:1,last_login_time:"2025-7-7 09:00:00",last_login_ip:"*************",created_at:"2024-01-15 10:00:00",updated_at:"2025-7-7 09:00:00"}];let i=[];try{const e=localStorage.getItem("wifi_admin_accounts");i=e?JSON.parse(e):s,e||localStorage.setItem("wifi_admin_accounts",JSON.stringify(s))}catch(k){console.error("初始化mockAccounts失败:",k),i=s}const l=[{id:1,user_id:1,username:"admin",module:"系统管理",action:"更新系统配置",method:"PUT",url:"/api/v1/admin/system/config/update",ip:"127.0.0.1",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64)",params:'{"site_name":"华红WIFI共享商业系统"}',result:"success",created_at:"2025-7-7 15:30:00"},{id:2,user_id:2,username:"operation",module:"WiFi管理",action:"创建WiFi热点",method:"POST",url:"/api/v1/admin/wifi/create",ip:"*************",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64)",params:'{"name":"星巴克-朝阳店","password":"starbucks2024"}',result:"success",created_at:"2025-7-7 14:20:00"},{id:3,user_id:3,username:"finance",module:"提现管理",action:"审核提现申请",method:"PUT",url:"/api/v1/admin/withdraw/audit/1",ip:"*************",user_agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64)",params:'{"status":1,"remark":"审核通过"}',result:"success",created_at:"2025-7-7 11:00:00"}];function c(e){console.log("保存账号数据到localStorage:",e),localStorage.setItem("wifi_admin_accounts",JSON.stringify(e)),localStorage.removeItem("wifi_admin_accounts_processed"),localStorage.removeItem("wifi_admin_accounts_cache")}function d(){try{localStorage.removeItem("wifi_admin_accounts_processed"),localStorage.removeItem("wifi_admin_accounts_cache");const e=localStorage.getItem("wifi_admin_accounts");if(console.log("getStoredAccounts - 从localStorage获取的原始数据:",e),e){const t=JSON.parse(e);return console.log("getStoredAccounts - 解析后的账号数据:",t),t}}catch(k){console.error("getStoredAccounts - 从localStorage获取账号数据失败:",k)}return console.log("getStoredAccounts - 返回默认账号数据"),s}function u(){return o?new Promise(e=>{setTimeout(()=>{const t=getStoredSystemConfig();e({code:200,data:t,message:"获取成功"})},200)}):Object(r["a"])({url:"/api/v1/admin/system/config",method:"get"})}function m(e){return o?new Promise(t=>{setTimeout(()=>{const a=getStoredSystemConfig();e.basic&&Object.assign(a.basic,e.basic),e.payment&&Object.assign(a.payment,e.payment),e.logistics&&Object.assign(a.logistics,e.logistics),e.sms&&(Object.assign(a.sms,e.sms),e.sms.templates&&Object.assign(a.sms.templates,e.sms.templates)),a.updated_at=(new Date).toISOString().replace("T"," ").slice(0,19),saveSystemConfig(a),t({code:200,message:"更新成功"})},500)}):Object(r["a"])({url:"/api/v1/admin/system/config/update",method:"put",data:e})}function p(){const e=localStorage.getItem("wifi_admin_roles");return e?JSON.parse(e):n}function g(e){localStorage.setItem("wifi_admin_roles",JSON.stringify(e))}let h=p();function f(){return new Promise(e=>{setTimeout(()=>{const t=p();h=t,e({code:200,data:{list:t,total:t.length},message:"获取成功"})},200)})}function b(e){return new Promise(t=>{setTimeout(()=>{const a=p();if(a.some(t=>t.name===e.name))return void t({code:400,message:"角色名称已存在"});if(e.code&&a.some(t=>t.code===e.code))return void t({code:400,message:"角色编码已存在"});const r={...e,id:a.length>0?Math.max(...a.map(e=>e.id))+1:1,created_at:(new Date).toISOString().replace("T"," ").slice(0,19),updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)};a.push(r),g(a),h=a,t({code:200,data:r,message:"创建成功"})},500)})}function _(e,t){return new Promise((a,r)=>{setTimeout(()=>{const o=p(),n=o.findIndex(t=>t.id===parseInt(e));if(n>-1){if(1===e&&(t.code!==o[n].code||0===t.status))return void r(new Error("不能修改超级管理员的关键信息"));o[n]={...o[n],...t,updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)},g(o),h=o,a({code:200,data:o[n],message:"更新成功"})}else r(new Error("角色不存在"))},500)})}function v(e){return new Promise((t,a)=>{setTimeout(()=>{if(1===parseInt(e))return void a(new Error("超级管理员角色不能删除"));const r=p(),o=r.findIndex(t=>t.id===parseInt(e));o>-1?(r.splice(o,1),g(r),h=r,t({code:200,message:"删除成功"})):a(new Error("角色不存在"))},300)})}function y(e){return o?new Promise(t=>{setTimeout(()=>{let a=d();console.log("getAccountList获取到的原始账号数据:",a);const r=p();console.log("获取到的角色数据:",r),a=a.map(e=>{const t=r.find(t=>t.id===e.role_id);return{...e,role_name:t?t.name:"未知角色"}}),e&&(e.username&&(a=a.filter(t=>t.username.includes(e.username))),e.nickname&&(a=a.filter(t=>t.nickname&&t.nickname.includes(e.nickname))),e.role_id&&(a=a.filter(t=>t.role_id===parseInt(e.role_id))),void 0!==e.status&&""!==e.status&&(a=a.filter(t=>t.status===parseInt(e.status)))),localStorage.setItem("wifi_admin_accounts_processed",JSON.stringify(a)),console.log("getAccountList处理后的账号数据:",a),t({code:200,data:{total:a.length,list:a},message:"获取成功"})},200)}):Object(r["a"])({url:"/api/v1/admin/system/account/list",method:"get",params:e})}function w(e){return o?new Promise((t,a)=>{setTimeout(()=>{const r=d();if(r.some(t=>t.username===e.username))return void a({code:400,message:"用户名已存在"});const o=p();console.log("创建账号时获取的角色数据:",o);const n=o.find(t=>t.id===parseInt(e.role_id));if(!n)return void a({code:400,message:"角色不存在"});const s={id:r.length>0?Math.max(...r.map(e=>e.id))+1:1,username:e.username,nickname:e.real_name||e.username,real_name:e.real_name||"",email:e.email||"",phone:e.phone||"",avatar:e.avatar||"",role_id:parseInt(e.role_id),role_name:n.name,status:parseInt(e.status)||1,last_login_time:"",last_login_ip:"",created_at:(new Date).toISOString().replace("T"," ").slice(0,19),updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)};r.push(s),c(r),localStorage.removeItem("wifi_admin_accounts_processed"),localStorage.removeItem("wifi_admin_accounts_cache"),t({code:200,data:s,message:"创建成功"})},500)}):Object(r["a"])({url:"/api/v1/admin/system/account/create",method:"post",data:e})}function I(e,t){return o?new Promise((a,r)=>{setTimeout(()=>{const o=d();console.log("更新账号 - 原始账号列表:",o),console.log("更新账号 - ID:",e,"类型:",typeof e),console.log("更新账号 - 数据:",t);const n=parseInt(e);o.forEach(e=>{console.log("账号ID:",e.id,"类型:",typeof e.id)});const s=o.findIndex(e=>parseInt(e.id)===n);if(console.log("找到的账号索引:",s),-1===s)return console.error("账号不存在, ID:",e),void r({code:404,message:"账号不存在"});if(t.username&&t.username!==o[s].username&&o.some(e=>parseInt(e.id)!==n&&e.username===t.username))return void r({code:400,message:"用户名已存在"});const i=p(),l=i.find(e=>e.id===parseInt(t.role_id));if(!l)return void r({code:400,message:"角色不存在"});const u={...o[s],...t,role_id:parseInt(t.role_id),role_name:l.name,updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)};o[s]=u,console.log("更新后的账号:",u),console.log("更新后的账号列表:",o),c(o),a({code:200,data:u,message:"更新成功"})},500)}):Object(r["a"])({url:"/api/v1/admin/system/account/update/"+e,method:"put",data:t})}function S(e){return o?new Promise((t,a)=>{setTimeout(()=>{console.log("删除账号 - ID:",e,"类型:",typeof e);const r=parseInt(e);if(1===r)return console.error("尝试删除超级管理员账号"),void a(new Error("超级管理员账号不能删除"));const o=d();console.log("删除账号 - 原始账号列表:",o),o.forEach(e=>{console.log("账号ID:",e.id,"类型:",typeof e.id)});const n=o.findIndex(e=>parseInt(e.id)===r);if(console.log("找到的账号索引:",n),n>-1){const e=o[n];console.log("要删除的账号:",e),o.splice(n,1),console.log("删除后的账号列表:",o),c(o),t({code:200,message:"删除成功"})}else console.error("账号不存在, ID:",e),a(new Error("账号不存在"))},300)}):Object(r["a"])({url:"/api/v1/admin/system/account/delete/"+e,method:"delete"})}function P(e){return o?new Promise(t=>{setTimeout(()=>{const a=parseInt(e.page)||1,r=parseInt(e.limit)||10,o=[...l];for(let e=4;e<=20;e++)o.push({id:e,user_id:Math.floor(4*Math.random())+1,username:["admin","operation","finance","service01"][Math.floor(4*Math.random())],module:["系统管理","WiFi管理","用户管理","订单管理"][Math.floor(4*Math.random())],action:["查看列表","创建记录","更新记录","删除记录"][Math.floor(4*Math.random())],method:["GET","POST","PUT","DELETE"][Math.floor(4*Math.random())],url:"/api/v1/admin/xxx",ip:"192.168.1."+(100+Math.floor(10*Math.random())),user_agent:"Mozilla/5.0",params:"{}",result:Math.random()>.1?"success":"error",created_at:new Date(Date.now()-36e5*e).toISOString().replace("T"," ").slice(0,19)});o.sort((e,t)=>new Date(t.created_at)-new Date(e.created_at));const n=o.length,s=(a-1)*r,i=s+r,c=o.slice(s,i);t({code:200,data:{list:c,total:n,page:a,limit:r},message:"获取成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/log/operation",method:"get",params:e})}function M(e){return o?new Promise(t=>{setTimeout(()=>{const a=parseInt(e.page)||1,r=parseInt(e.limit)||10,o=[];for(let e=1;e<=30;e++)o.push({id:e,user_id:Math.floor(4*Math.random())+1,username:["admin","operation","finance","service01"][Math.floor(4*Math.random())],ip:"192.168.1."+(100+Math.floor(20*Math.random())),location:["北京市","上海市","广州市","深圳市"][Math.floor(4*Math.random())],browser:["Chrome","Firefox","Safari","Edge"][Math.floor(4*Math.random())],os:["Windows 10","macOS","Ubuntu","Windows 11"][Math.floor(4*Math.random())],status:Math.random()>.2?1:0,message:Math.random()>.2?"登录成功":"密码错误",created_at:new Date(Date.now()-72e5*e).toISOString().replace("T"," ").slice(0,19)});const n=o.length,s=(a-1)*r,i=s+r,l=o.slice(s,i);t({code:200,data:{list:l,total:n,page:a,limit:r},message:"获取成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/log/login",method:"get",params:e})}function T(e){return o?new Promise(t=>{setTimeout(()=>{const a=parseInt(e.page)||1,r=parseInt(e.limit)||10,o=[];for(let e=1;e<=15;e++){const t=Math.random()>.3,a=["Connection timeout","Invalid parameter","File not found","Permission denied","Database connection failed","Redis connection refused","API rate limit exceeded","Memory allocation failed"],r=a[Math.floor(Math.random()*a.length)],n=t?`Error: ${r}\n    at Object.<anonymous> (/app/src/api/wifi.js:45:15)\n    at Module._compile (internal/modules/cjs/loader.js:1063:30)\n    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1092:10)\n    at Module.load (internal/modules/cjs/loader.js:928:32)\n    at Function.Module._load (internal/modules/cjs/loader.js:769:14)\n    at Module.require (internal/modules/cjs/loader.js:952:19)\n    at require (internal/modules/cjs/helpers.js:88:18)\n    at Object.<anonymous> (/app/src/router/index.js:12:18)\n    at Module._compile (internal/modules/cjs/loader.js:1063:30)\n    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1092:10)`:null;o.push({id:e,level:["error","warning"][Math.floor(2*Math.random())],module:["API","Database","Redis","File"][Math.floor(4*Math.random())],message:r,stack:n,user_id:Math.floor(4*Math.random())+1,username:["admin","operation","finance","service01"][Math.floor(4*Math.random())],ip:"192.168.1."+(100+Math.floor(20*Math.random())),url:"/api/v1/admin/xxx",created_at:new Date(Date.now()-108e5*e).toISOString().replace("T"," ").slice(0,19)})}const n=o.length,s=(a-1)*r,i=s+r,l=o.slice(s,i);t({code:200,data:{list:l,total:n,page:a,limit:r},message:"获取成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/log/error",method:"get",params:e})}},8763:function(e,t,a){"use strict";a("a592")},a592:function(e,t,a){},a732:function(e,t,a){"use strict";var r=a("23e7"),o=a("c65b"),n=a("2266"),s=a("59ed"),i=a("825a"),l=a("46c4"),c=a("2a62"),d=a("f99f"),u=d("some",TypeError);r({target:"Iterator",proto:!0,real:!0,forced:u},{some:function(e){i(this);try{s(e)}catch(r){c(this,"throw",r)}if(u)return o(u,this,e);var t=l(this),a=0;return n(t,(function(t,r){if(e(t,a++))return r()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},ab43:function(e,t,a){"use strict";var r=a("23e7"),o=a("c65b"),n=a("59ed"),s=a("825a"),i=a("46c4"),l=a("c5cc"),c=a("9bdd"),d=a("2a62"),u=a("2baa"),m=a("f99f"),p=a("c430"),g=!p&&!u("map",(function(){})),h=!p&&!g&&m("map",TypeError),f=p||g||h,b=l((function(){var e=this.iterator,t=s(o(this.next,e)),a=this.done=!!t.done;if(!a)return c(e,this.mapper,[t.value,this.counter++],!0)}));r({target:"Iterator",proto:!0,real:!0,forced:f},{map:function(e){s(this);try{n(e)}catch(t){d(this,"throw",t)}return h?o(h,this,e):new b(i(this),{mapper:e})}})},f665:function(e,t,a){"use strict";var r=a("23e7"),o=a("c65b"),n=a("2266"),s=a("59ed"),i=a("825a"),l=a("46c4"),c=a("2a62"),d=a("f99f"),u=d("find",TypeError);r({target:"Iterator",proto:!0,real:!0,forced:u},{find:function(e){i(this);try{s(e)}catch(r){c(this,"throw",r)}if(u)return o(u,this,e);var t=l(this),a=0;return n(t,(function(t,r){if(e(t,a++))return r(t)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})}}]);