#!/bin/bash

# WiFi共享管理后台启动脚本

# 设置工作目录
cd "$(dirname "$0")"

# 创建日志目录
mkdir -p logs

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: Node.js 未安装"
    exit 1
fi

# 检查PM2是否安装
if ! command -v pm2 &> /dev/null; then
    echo "安装PM2..."
    npm install -g pm2
fi

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "安装依赖..."
    npm install
fi

# 检查dist目录是否存在
if [ ! -d "dist" ]; then
    echo "构建前端项目..."
    npm run build
fi

# 启动应用
echo "启动WiFi共享管理后台..."
pm2 start ecosystem.config.js --env production

# 显示状态
pm2 status

echo "WiFi共享管理后台启动完成!"
echo "访问地址: http://localhost:8081"
echo "日志文件: ./logs/wifi-share-admin.log"
echo ""
echo "常用命令:"
echo "  查看状态: pm2 status"
echo "  查看日志: pm2 logs wifi-share-admin"
echo "  重启应用: pm2 restart wifi-share-admin"
echo "  停止应用: pm2 stop wifi-share-admin"
