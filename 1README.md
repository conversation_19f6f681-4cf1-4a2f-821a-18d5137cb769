# WiFi共享商业系统 - 项目文档

## 项目概述

WiFi共享商业系统是一个完整的商业化WiFi共享平台，包含管理后台、小程序前端和后端API服务。系统支持WiFi码管理、商品销售、用户管理、订单处理、广告投放等功能。

## 项目架构

```
wifi共享商业系统/
├── wifi-share-admin/     # Vue.js管理后台
├── wifi-share-server/    # Node.js后端API服务
├── UI示意图/            # 界面设计稿
├── 手稿示意图/          # 手绘原型图
└── 项目文档/            # 需求和架构文档
```

## 技术栈

### 前端（管理后台）
- **框架**: Vue.js 2.x
- **UI库**: Element UI
- **状态管理**: Vuex
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vue CLI

### 后端（API服务）
- **框架**: Node.js + Express.js
- **数据库**: MySQL
- **身份验证**: JWT
- **日志系统**: Winston
- **安全**: Helmet.js
- **跨域**: CORS

## 会话日志记录

### 问题修复历程

#### 第一阶段：用户列表API错误修复
**时间**: 2025-01-07
**问题**: `/api/user/list` 接口返回500错误
```
Error: Incorrect arguments to mysqld_stmt_execute
```

**原因分析**: 
- SQL查询参数数组与占位符数量不匹配
- 同一个params数组被用于count查询和list查询，导致参数重复

**解决方案**:
```javascript
// 修复前：使用同一个params数组
const params = [];
// count查询使用params
const countResult = await db.getOne(countSql, params);
// list查询继续向params添加参数，导致数量不匹配
params.push(parseInt(limit), parseInt(offset));
const users = await db.query(listSql, params);

// 修复后：分别创建独立的参数数组
const countParams = [];
const listParams = [];
// 为每个查询创建独立的参数数组
const countResult = await db.getOne(countSql, countParams);
listParams.push(parseInt(limit), parseInt(offset));
const users = await db.query(listSql, listParams);
```

**修复文件**: `wifi-share-server/src/routes/user.js`
**状态**: ✅ 已修复

#### 第二阶段：系统配置API路由缺失
**时间**: 2025-01-07
**问题**: `/api/v1/admin/system/config` 接口返回404错误
```
{"status":"error","message":"未找到请求的资源"}
```

**原因分析**:
- 后端缺少系统配置相关的路由文件
- 前端调用v1版本API，但后端没有对应的路由结构

**解决方案**:
1. 创建系统配置路由文件 `wifi-share-server/src/routes/system.js`
2. 创建v1版本API路由 `wifi-share-server/src/routes/v1.js`
3. 在主应用中注册v1路由

**新增API接口**:
```javascript
// 系统配置管理
GET  /api/v1/admin/system/config           - 获取系统配置
PUT  /api/v1/admin/system/config/update    - 更新系统配置

// 角色管理
GET    /api/v1/admin/system/role/list      - 获取角色列表
POST   /api/v1/admin/system/role/create    - 创建角色
PUT    /api/v1/admin/system/role/update/:id - 更新角色
DELETE /api/v1/admin/system/role/delete/:id - 删除角色

// 账号管理
GET    /api/v1/admin/system/account/list      - 获取账号列表
POST   /api/v1/admin/system/account/create    - 创建账号
PUT    /api/v1/admin/system/account/update/:id - 更新账号
DELETE /api/v1/admin/system/account/delete/:id - 删除账号
```

**修复文件**: 
- `wifi-share-server/src/routes/system.js` (新建)
- `wifi-share-server/src/routes/v1.js` (新建)
- `wifi-share-server/app.js` (更新)

**状态**: ✅ 已修复

#### 第三阶段：日志API路径不匹配
**时间**: 2025-01-07
**问题**: `/api/v1/admin/log/operation` 接口返回404错误
```
{"status":"error","message":"未找到请求的资源"}
```

**原因分析**:
- 前端调用 `/api/v1/admin/log/operation`
- 后端配置的是 `/api/v1/admin/system/log/operation`
- 路径不匹配导致404错误

**解决方案**:
1. 创建专门的日志路由文件 `wifi-share-server/src/routes/log.js`
2. 在v1路由中添加独立的日志路由 `/admin/log`
3. 重新启动后端服务器

**新增日志API接口**:
```javascript
// 操作日志
GET /api/v1/admin/log/operation - 获取操作日志
// 参数: page, limit, username, module, start_time, end_time

// 登录日志  
GET /api/v1/admin/log/login - 获取登录日志
// 参数: page, limit, username, ip, start_time, end_time

// 错误日志
GET /api/v1/admin/log/error - 获取错误日志
// 参数: page, limit, level, start_time, end_time
```

**修复文件**:
- `wifi-share-server/src/routes/log.js` (新建)
- `wifi-share-server/src/routes/v1.js` (更新)

**状态**: ✅ 已修复

## API接口文档

### 认证相关
```javascript
POST /api/auth/login          - 管理员登录
POST /api/auth/logout         - 管理员登出
GET  /api/auth/me             - 获取当前用户信息
```

### 用户管理
```javascript
GET  /api/user/list           - 获取用户列表
GET  /api/user/detail/:id     - 获取用户详情
PUT  /api/user/status/:id     - 更新用户状态
GET  /api/user/tag/list       - 获取用户标签列表
POST /api/user/tag/create     - 创建用户标签
PUT  /api/user/tag/update/:id - 更新用户标签
DELETE /api/user/tag/delete/:id - 删除用户标签
```

### WiFi管理
```javascript
GET  /api/wifi/list           - 获取WiFi列表
POST /api/wifi/create         - 创建WiFi
PUT  /api/wifi/update/:id     - 更新WiFi
DELETE /api/wifi/delete/:id   - 删除WiFi
GET  /api/wifi/stats          - 获取WiFi统计
```

### 商品管理
```javascript
GET  /api/goods/list          - 获取商品列表
POST /api/goods/create        - 创建商品
PUT  /api/goods/update/:id    - 更新商品
DELETE /api/goods/delete/:id  - 删除商品
GET  /api/goods/detail/:id    - 获取商品详情
```

### 订单管理
```javascript
GET  /api/order/list          - 获取订单列表
GET  /api/order/detail/:id    - 获取订单详情
PUT  /api/order/status/:id    - 更新订单状态
```

### 系统管理
```javascript
GET  /api/v1/admin/system/config           - 获取系统配置
PUT  /api/v1/admin/system/config/update    - 更新系统配置
GET  /api/v1/admin/system/role/list        - 获取角色列表
POST /api/v1/admin/system/role/create      - 创建角色
PUT  /api/v1/admin/system/role/update/:id  - 更新角色
DELETE /api/v1/admin/system/role/delete/:id - 删除角色
GET  /api/v1/admin/system/account/list     - 获取账号列表
POST /api/v1/admin/system/account/create   - 创建账号
PUT  /api/v1/admin/system/account/update/:id - 更新账号
DELETE /api/v1/admin/system/account/delete/:id - 删除账号
```

### 日志管理
```javascript
GET /api/v1/admin/log/operation - 获取操作日志
GET /api/v1/admin/log/login     - 获取登录日志
GET /api/v1/admin/log/error     - 获取错误日志
```

## 数据库结构

### 用户表 (user)
```sql
CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `balance` decimal(10,2) DEFAULT '0.00' COMMENT '余额',
  `is_leader` tinyint(1) DEFAULT '0' COMMENT '是否为团长',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `openid` (`openid`)
);
```

### WiFi表 (wifi)
```sql
CREATE TABLE `wifi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT 'WiFi名称',
  `password` varchar(100) DEFAULT NULL COMMENT 'WiFi密码',
  `qr_code` text COMMENT '二维码内容',
  `location` varchar(255) DEFAULT NULL COMMENT '位置',
  `description` text COMMENT '描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 商品表 (goods)
```sql
CREATE TABLE `goods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '商品名称',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `image` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `description` text COMMENT '商品描述',
  `stock` int(11) DEFAULT '0' COMMENT '库存',
  `category_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-下架，1-上架',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 订单表 (order)
```sql
CREATE TABLE `order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态：0-待付款，1-已付款，2-已发货，3-已完成，4-已取消',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`)
);
```

## 项目启动指南

### 后端服务启动
```bash
cd wifi-share-server
npm install
node app.js
```

### 前端管理后台启动
```bash
cd wifi-share-admin
npm install
npm run serve
```

### 默认管理员账号
- 用户名: `admin`
- 密码: `admin123`

## 环境配置

### 后端环境变量
```env
NODE_ENV=development
PORT=8080
DB_HOST=localhost
DB_PORT=3306
DB_NAME=wifi_share
DB_USER=root
DB_PASSWORD=your_password
JWT_SECRET=your_jwt_secret
```

### 前端环境配置
```javascript
// vue.config.js
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
}
```

## 问题排查指南

### 常见问题及解决方案

1. **API接口404错误**
   - 检查路由配置是否正确
   - 确认API路径与前端调用一致
   - 检查中间件是否正确配置

2. **数据库连接失败**
   - 检查数据库配置信息
   - 确认数据库服务是否启动
   - 检查数据库用户权限

3. **JWT认证失败**
   - 检查JWT密钥配置
   - 确认token是否正确传递
   - 检查token是否过期

4. **跨域问题**
   - 检查CORS配置
   - 确认前端代理设置
   - 检查请求头配置

### 3. 常见问题和解决方案

**问题**: `/api/user/list` 接口返回500错误
- **错误信息**: "Incorrect arguments to mysqld_stmt_execute"
- **原因**: MySQL查询参数数组管理不当，导致执行时参数不匹配
- **解决方案**: 
  1. 统一使用`whereParams`数组管理WHERE子句参数
  2. 将LIMIT和OFFSET直接拼接到SQL字符串中：`LIMIT ${limitNum} OFFSET ${offset}`
  3. 确保参数顺序和数量与SQL占位符一致
- **注意**: 后端服务器运行在4000端口，管理后台在8080端口通过代理访问

**问题**: 运行`npm install`时出现gyp ERR错误

## 开发规范

### 代码规范
- 使用ESLint进行代码检查
- 遵循JavaScript Standard Style
- 使用Prettier进行代码格式化

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式修改
- refactor: 代码重构
- test: 测试相关
- chore: 其他修改

### API设计规范
- RESTful API设计
- 统一的响应格式
- 适当的HTTP状态码
- 详细的错误信息

## 部署指南

### 生产环境部署
1. 构建前端项目
```bash
cd wifi-share-admin
npm run build
```

2. 配置Nginx
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

3. 使用PM2管理Node.js进程
```bash
npm install -g pm2
pm2 start app.js --name wifi-share-api
pm2 startup
pm2 save
```

## 维护说明

### 日志管理
- 访问日志: `/logs/access.log`
- 错误日志: `/logs/error.log`
- 应用日志: `/logs/app.log`

### 数据备份
- 定期备份数据库
- 备份上传文件
- 备份配置文件

### 性能监控
- 监控API响应时间
- 监控数据库查询性能
- 监控服务器资源使用

## 更新日志

### v1.0.0 (2025-01-07)
- ✅ 修复用户列表API参数错误
- ✅ 添加系统配置API路由
- ✅ 修复日志API路径不匹配
- ✅ 完善API接口文档
- ✅ 添加v1版本API路由支持

### 待开发功能
- [ ] 小程序前端开发
- [ ] 支付功能集成
- [ ] 数据统计报表
- [ ] 消息推送系统
- [ ] 多租户支持

## 功能完成情况

### ✅ 已完成功能

#### 1. 后台管理系统
- **用户管理** ✅
  - 用户列表页：数据统计卡片、搜索筛选、批量操作、用户状态管理
  - 用户详情页：基本信息、消费统计、WiFi统计、团队信息、余额管理
  - 用户标签管理：标签创建、编辑、删除、分配
  - 批量操作：批量启用/禁用、设置等级、分配标签
  - 余额管理：增加/减少余额、操作记录
- **系统基础** ✅
  - 管理员登录认证
  - 权限控制中间件
  - 错误处理机制
  - 日志记录系统
- **数据库设计** ✅
  - 完整的数据库表结构
  - 用户表、商品表、订单表等核心表
  - 数据库连接和查询封装

#### 2. 后端API服务
- **用户管理API** ✅
  - GET /api/user/list - 获取用户列表（支持分页、搜索、筛选）
  - GET /api/user/detail/:id - 获取用户详情
  - PUT /api/user/update/:id - 更新用户信息
  - PUT /api/user/status/:id - 更新用户状态
  - GET /api/user/stats - 获取用户统计数据
  - POST /api/user/balance/:id - 调整用户余额
  - POST /api/user/batch - 批量操作用户
  - GET /api/user/tag/list - 获取用户标签列表
  - POST /api/user/tag/create - 创建用户标签
  - PUT /api/user/tag/update/:id - 更新用户标签
  - DELETE /api/user/tag/delete/:id - 删除用户标签

### 🚧 开发中功能

#### 1. WiFi码管理
- WiFi码列表和详情页面
- WiFi码创建和编辑功能
- 使用统计和数据分析

#### 2. 商城管理
- 商品管理功能
- 订单管理功能
- 分类管理功能

#### 3. 分润管理
- 分润规则配置
- 分润账单管理
- 提现管理功能

### 📋 待开发功能

#### 1. 小程序前端
- 用户端小程序界面
- WiFi码扫描和连接
- 商城购物功能
- 团队管理功能

#### 2. 广告管理
- 广告位管理
- 广告内容管理
- 投放效果统计

#### 3. 系统设置
- 基础配置管理
- 权限角色管理
- 操作日志查看

## 用户管理功能详细说明

### 用户列表页功能
1. **数据统计卡片**
   - 总用户数、活跃用户数、团长用户数
   - 今日新增、本月新增、总余额统计

2. **搜索和筛选**
   - 按昵称/手机号搜索
   - 按状态筛选（启用/禁用）
   - 按是否团长筛选

3. **批量操作**
   - 批量启用/禁用用户
   - 批量设置用户等级
   - 批量分配用户标签

4. **单个用户操作**
   - 查看用户详情
   - 编辑用户信息
   - 调整用户余额
   - 启用/禁用用户

### 用户详情页功能
1. **基本信息展示**
   - 用户头像、昵称、状态
   - 用户ID、openid、手机号
   - 账户余额、等级、注册时间

2. **消费统计**
   - 总订单数、总消费金额
   - 平均订单金额、偏好分类
   - 月度消费趋势图表

3. **WiFi统计**
   - 创建WiFi数、总扫码次数
   - 平均扫码次数、最受欢迎WiFi
   - 近期活动数据

4. **团队信息**
   - 团队ID、名称、成员数量
   - WiFi数量、总收益

5. **操作功能**
   - 编辑用户信息
   - 调整账户余额
   - 启用/禁用账户

### 用户标签管理
1. **标签管理**
   - 创建、编辑、删除标签
   - 标签名称和描述管理

2. **标签分配**
   - 为用户分配标签
   - 批量标签分配
   - 标签使用统计

## API接口说明

### 用户管理接口
所有用户管理接口都需要管理员权限验证。

#### 获取用户列表
```
GET /api/user/list
参数：
- page: 页码（默认1）
- limit: 每页数量（默认10）
- keyword: 搜索关键词（昵称/手机号）
- status: 状态筛选（0/1）
- is_leader: 是否团长（0/1）
```

#### 获取用户统计
```
GET /api/user/stats
返回：
- total_users: 总用户数
- active_users: 活跃用户数
- leader_users: 团长用户数
- today_users: 今日新增
- month_users: 本月新增
- total_balance: 总余额
```

#### 调整用户余额
```
POST /api/user/balance/:id
参数：
- amount: 调整金额
- type: 操作类型（add/subtract）
- remark: 备注信息
```

#### 批量操作用户
```
POST /api/user/batch
参数：
- user_ids: 用户ID数组
- action: 操作类型（enable/disable/set_level）
- data: 附加数据（如level值）
```

## 联系方式

- 项目负责人: 华红科技
- 技术支持: 开发团队
- 文档维护: AI助手

---

*本文档记录了WiFi共享商业系统的完整开发过程和技术细节，包含所有会话日志和问题修复记录。* 