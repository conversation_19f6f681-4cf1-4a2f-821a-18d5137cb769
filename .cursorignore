# 依赖目录 - 包含项目依赖的模块
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# 构建输出 - 编译后的文件和构建目录
/dist/
/build/
/coverage/

# 本地环境文件 - 包含敏感信息的环境配置
.env
.env.local
.env.*.local

# 编辑器目录和文件 - IDE和编辑器的配置文件
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.project
.classpath

# 操作系统文件 - 系统生成的文件
.DS_Store
Thumbs.db
desktop.ini

# 日志文件 - 应用程序日志
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 临时文件 - 缓存和临时生成的文件
.temp
.tmp
.cache
*.tmp
*.temp

# Vue特定文件 - Vue项目特有的临时文件
.nuxt
.nitro
.output
.vercel
.next
out
*.tgz

# 测试覆盖率报告
coverage/
.nyc_output/

# 其他常见忽略文件
*.bak
*.swp
*.lock
!yarn.lock
!package-lock.json 