<template>
  <div class="team-list-container">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="团队名称">
          <el-input v-model="searchForm.name" placeholder="请输入团队名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="团长">
          <el-input v-model="searchForm.leader" placeholder="请输入团长姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="正常" :value="1"></el-option>
            <el-option label="禁用" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <div slot="header" class="card-header">
        <span>团队列表</span>
        <el-button type="primary" size="small" @click="handleCreate">创建团队</el-button>
      </div>
      
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" align="center"></el-table-column>
        <el-table-column prop="name" label="团队名称" min-width="150"></el-table-column>
        <el-table-column prop="leader_name" label="团长" min-width="120">
          <template slot-scope="scope">
            <div class="user-info">
              <el-avatar :size="30" :src="scope.row.leader_avatar || '/img/default-avatar.png'"></el-avatar>
              <span class="user-name">{{ scope.row.leader_name || '未知' }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="member_count" label="成员数" width="100" align="center">
          <template slot-scope="scope">
            <el-tag type="info">{{ scope.row.member_count || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="total_income" label="总收益" width="120" align="right">
          <template slot-scope="scope">
            <span class="revenue">¥{{ (scope.row.total_income || 0).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="month_income" label="本月收益" width="120" align="right">
          <template slot-scope="scope">
            <span style="color: #e6a23c">¥{{ (scope.row.month_income || 0).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="handleMembers(scope.row)">成员管理</el-button>
            <el-button type="text" size="small" @click="handleDelete(scope.row)" style="color: #F56C6C;">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import { getTeamList, deleteTeam } from '@/api/team'
import { formatDate } from '@/utils/date'

export default {
  name: 'TeamList',
  data() {
    return {
      loading: false,
      searchForm: {
        name: '',
        leader: '',
        status: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      }
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    // 获取数据
    async fetchData() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit,
          ...this.searchForm
        }
        const res = await getTeamList(params)
        if (res.code === 0) {
          this.tableData = res.data.list || []
          // 修复分页组件total属性类型错误
          this.pagination.total = (res.data.pagination && res.data.pagination.total) || res.data.total || 0
        }
      } catch (error) {
        console.error('获取团队列表失败:', error)
        this.tableData = []
        this.pagination.total = 0
        this.$message.error('获取团队列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.fetchData()
    },
    
    // 重置
    handleReset() {
      this.searchForm = {
        name: '',
        leader: '',
        status: ''
      }
      this.pagination.page = 1
      this.fetchData()
    },
    
    // 创建团队
    handleCreate() {
      this.$router.push('/team/create')
    },
    
    // 查看详情
    handleView(row) {
      this.$router.push(`/team/detail/${row.id}`)
    },
    
    // 编辑
    handleEdit(row) {
      this.$router.push(`/team/edit/${row.id}`)
    },
    
    // 成员管理
    handleMembers(row) {
      this.$router.push(`/team/members/${row.id}`)
    },
    
    // 删除
    handleDelete(row) {
      this.$confirm('确定要删除该团队吗？删除后不可恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const res = await deleteTeam(row.id)
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.fetchData()
          }
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => {})
    },
    
    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.limit = val
      this.fetchData()
    },
    
    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.page = val
      this.fetchData()
    },
    
    // 格式化日期
    formatDate
  }
}
</script>

<style lang="scss" scoped>
.team-list-container {
  padding: 20px;
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .user-info {
      display: flex;
      align-items: center;
      
      .user-name {
        margin-left: 10px;
      }
    }
    
    .revenue {
      color: #F56C6C;
      font-weight: bold;
    }
  }
  
  .el-pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style> 