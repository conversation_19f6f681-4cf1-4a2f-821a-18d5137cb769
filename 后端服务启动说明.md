# 后端服务启动说明

## 🚨 网络连接失败解决方案

根据您遇到的错误：`API调用失败，使用模拟数据: Error: 网络连接失败，请检查网络设置`，这表明小程序无法连接到后端服务。

## 🔧 解决步骤

### 第一步：启动后端服务

#### 1. 进入后端服务目录
```bash
cd wifi-share-server
```

#### 2. 安装依赖（首次运行需要）
```bash
npm install
```

#### 3. 配置环境变量
根据后端README.md文档，确保配置文件正确：

**创建 `.env` 文件**（如果不存在）：
```bash
cp env.example.env .env
```

**编辑 `.env` 文件**，确保包含以下配置：
```env
# 服务器配置
PORT=4000
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=wo587129955
DB_NAME=wifi_share

# JWT密钥
JWT_SECRET=your_jwt_secret_key
```

#### 4. 启动后端服务

**开发环境启动**：
```bash
npm run dev
```

**或者直接启动**：
```bash
node app.js
```

**或者使用PM2**：
```bash
npm run pm2
```

#### 5. 验证服务启动
服务启动成功后，您应该看到类似输出：
```
服务器运行在端口 4000
数据库连接成功
服务器启动完成
```

### 第二步：验证服务可访问

#### 1. 浏览器测试
在浏览器中访问：
```
http://localhost:4000/api/v1/client/goods/list
```

如果服务正常，应该返回JSON数据或相应的错误信息。

#### 2. 小程序测试
在小程序开发工具中：
1. 打开商城页面
2. 查看控制台输出
3. 应该看到 "✅ API连接测试成功" 的消息

### 第三步：小程序网络配置

#### 1. 检查小程序域名配置
确保在微信开发者工具中：
- 进入 "详情" → "本地设置"
- 勾选 "不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"

#### 2. 检查网络权限
确保小程序有网络访问权限。

## 🐛 常见问题解决

### 问题1：端口冲突
**错误信息**：`Error: listen EADDRINUSE :::4000`

**解决方案**：
```bash
# 查找占用4000端口的进程
lsof -i :4000

# 杀死占用进程
kill -9 <进程ID>

# 或者修改端口号
```

### 问题2：数据库连接失败
**错误信息**：`ER_ACCESS_DENIED_ERROR` 或 `ECONNREFUSED`

**解决方案**：
1. 确保MySQL服务已启动
2. 检查数据库用户名和密码
3. 确保数据库 `wifi_share` 已创建

### 问题3：依赖安装失败
**解决方案**：
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules
npm install
```

### 问题4：小程序仍然无法连接
**检查清单**：
- [ ] 后端服务是否在4000端口正常运行
- [ ] 网络是否正常
- [ ] 防火墙是否阻止连接
- [ ] 小程序开发工具网络设置是否正确

## 📋 API接口测试

### 测试商品列表API
```bash
curl "http://localhost:4000/api/v1/client/goods/list?page=1&limit=10"
```

### 测试购物车API
```bash
curl "http://localhost:4000/api/v1/client/cart/list" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🎯 成功标志

当一切配置正确后，您应该看到：

### 1. 后端服务启动成功
```
✅ 数据库连接成功
✅ 服务器运行在端口 4000
✅ 路由注册完成
```

### 2. 小程序连接成功
```
🔍 开始测试API连接...
📍 服务地址: http://localhost:4000
✅ 后端服务连接成功
✅ API连接测试成功
```

### 3. 商品数据加载成功
- 商城页面显示真实商品数据
- 不再显示"使用模拟数据"的日志
- 购物车功能正常工作

## 🚀 快速启动脚本

创建一个一键启动脚本 `start-backend.sh`：

```bash
#!/bin/bash
echo "🚀 启动WiFi共享商城后端服务..."

# 进入后端目录
cd wifi-share-server

# 检查node_modules是否存在
if [ ! -d "node_modules" ]; then
  echo "📦 安装依赖..."
  npm install
fi

# 启动服务
echo "🔄 启动服务..."
npm run dev
```

使用方法：
```bash
chmod +x start-backend.sh
./start-backend.sh
```

## 📞 需要帮助？

如果仍然遇到问题，请提供：
1. 后端服务启动日志
2. 小程序控制台错误信息
3. 浏览器访问后端API的结果
4. 操作系统和Node.js版本

这样我可以提供更精准的解决方案！ 