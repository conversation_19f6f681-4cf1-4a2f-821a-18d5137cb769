<!DOCTYPE html>
<html>
<head>
    <title>Token测试</title>
    <script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.1/dist/js.cookie.min.js"></script>
</head>
<body>
    <h1>Token测试页面</h1>
    <button onclick="testToken()">测试Token</button>
    <button onclick="setTestToken()">设置测试Token</button>
    <button onclick="clearToken()">清除Token</button>
    <div id="result"></div>
    
    <script>
        function testToken() {
            const token = Cookies.get('Admin-Token');
            const result = document.getElementById('result');
            result.innerHTML = `
                <h3>Token信息：</h3>
                <p>Token存在: ${token ? '是' : '否'}</p>
                <p>Token值: ${token || '无'}</p>
                <p>所有Cookies: ${document.cookie}</p>
            `;
        }
        
        function setTestToken() {
            Cookies.set('Admin-Token', 'test-token-123456', { expires: 7 });
            alert('测试Token已设置');
            testToken();
        }
        
        function clearToken() {
            Cookies.remove('Admin-Token');
            alert('Token已清除');
            testToken();
        }
    </script>
</body>
</html> 