const axios = require('axios');

// Test the platform revenue APIs
async function testPlatformAPIs() {
  const baseURL = 'http://localhost:4000';
  
  // First, let's try to login as admin to get a token
  try {
    console.log('🔐 Testing admin login...');
    const loginResponse = await axios.post(`${baseURL}/api/v1/admin/auth/admin-login`, {
      username: 'mrx0927',
      password: 'hh20250701'
    });
    
    console.log('✅ Login successful');
    const token = loginResponse.data.data.token;
    console.log('🎫 Token:', token.substring(0, 20) + '...');
    
    // Set up headers with token
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    // Test platform revenue overview
    console.log('\n📊 Testing platform revenue overview...');
    const revenueResponse = await axios.get(`${baseURL}/api/v1/admin/platform/revenue`, { headers });
    console.log('✅ Revenue overview response:', JSON.stringify(revenueResponse.data, null, 2));
    
    // Test revenue trend
    console.log('\n📈 Testing revenue trend...');
    const trendResponse = await axios.get(`${baseURL}/api/v1/admin/platform/revenue-trend?period=7d`, { headers });
    console.log('✅ Revenue trend response:', JSON.stringify(trendResponse.data, null, 2));
    
    // Test business distribution
    console.log('\n🏢 Testing business distribution...');
    const businessResponse = await axios.get(`${baseURL}/api/v1/admin/platform/business-distribution?period=7d`, { headers });
    console.log('✅ Business distribution response:', JSON.stringify(businessResponse.data, null, 2));
    
    // Test financial report
    console.log('\n📋 Testing financial report...');
    const reportResponse = await axios.get(`${baseURL}/api/v1/admin/platform/financial-report?period=7d&page=1&limit=20`, { headers });
    console.log('✅ Financial report response:', JSON.stringify(reportResponse.data, null, 2));
    
  } catch (error) {
    console.error('❌ Error:', error.response ? error.response.data : error.message);
  }
}

testPlatformAPIs();
