<template>
  <div class="app-container">
    <div v-loading="loading">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>分润规则配置</span>
        </div>
        <el-form ref="form" :model="form" :rules="rules" label-width="150px">
          <el-divider content-position="left">WiFi分润规则</el-divider>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="平台分润比例" prop="wifi_platform_rate">
                <el-input-number
                  v-model="form.wifi_platform_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="1"
                  style="width: 180px;"
                ></el-input-number>
                <span class="unit">%</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="团长分润比例" prop="wifi_leader_rate">
                <el-input-number
                  v-model="form.wifi_leader_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="1"
                  style="width: 180px;"
                ></el-input-number>
                <span class="unit">%</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="成员分润比例" prop="wifi_member_rate">
                <el-input-number
                  v-model="form.wifi_member_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="1"
                  style="width: 180px;"
                ></el-input-number>
                <span class="unit">%</span>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="rate-info">
            <span class="info">分润比例总和: {{ getWifiTotalRate }}%</span>
            <span v-if="getWifiTotalRate !== 100" class="error">
              (注意: 分润比例总和应等于100%)
            </span>
          </div>

          <el-divider content-position="left">商品分润规则</el-divider>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="平台分润比例" prop="goods_platform_rate">
                <el-input-number
                  v-model="form.goods_platform_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="1"
                  style="width: 180px;"
                ></el-input-number>
                <span class="unit">%</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="团长分润比例" prop="goods_leader_rate">
                <el-input-number
                  v-model="form.goods_leader_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="1"
                  style="width: 180px;"
                ></el-input-number>
                <span class="unit">%</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="成员分润比例" prop="goods_member_rate">
                <el-input-number
                  v-model="form.goods_member_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="1"
                  style="width: 180px;"
                ></el-input-number>
                <span class="unit">%</span>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="rate-info">
            <span class="info">分润比例总和: {{ getGoodsTotalRate }}%</span>
            <span v-if="getGoodsTotalRate !== 100" class="error">
              (注意: 分润比例总和应等于100%)
            </span>
          </div>

          <el-divider content-position="left">广告分润规则</el-divider>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="平台分润比例" prop="ad_platform_rate">
                <el-input-number
                  v-model="form.ad_platform_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="1"
                  style="width: 180px;"
                ></el-input-number>
                <span class="unit">%</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="团长分润比例" prop="ad_leader_rate">
                <el-input-number
                  v-model="form.ad_leader_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="1"
                  style="width: 180px;"
                ></el-input-number>
                <span class="unit">%</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="成员分润比例" prop="ad_member_rate">
                <el-input-number
                  v-model="form.ad_member_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="1"
                  style="width: 180px;"
                ></el-input-number>
                <span class="unit">%</span>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="rate-info">
            <span class="info">分润比例总和: {{ getAdTotalRate }}%</span>
            <span v-if="getAdTotalRate !== 100" class="error">
              (注意: 分润比例总和应等于100%)
            </span>
          </div>

          <el-divider content-position="left">提现设置</el-divider>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="最小提现金额" prop="min_withdraw_amount">
                <el-input-number
                  v-model="form.min_withdraw_amount"
                  :min="0"
                  :precision="2"
                  :step="10"
                  style="width: 180px;"
                ></el-input-number>
                <span class="unit">元</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="提现手续费" prop="withdraw_fee_rate">
                <el-input-number
                  v-model="form.withdraw_fee_rate"
                  :min="0"
                  :max="100"
                  :precision="2"
                  :step="0.1"
                  style="width: 180px;"
                ></el-input-number>
                <span class="unit">%</span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="submitForm">保存配置</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getProfitRules, updateProfitRules } from '@/api/profit'

export default {
  name: 'ProfitRules',
  data () {
    return {
      loading: false,
      form: {
        // WiFi分润规则
        wifi_platform_rate: 40,
        wifi_leader_rate: 40,
        wifi_member_rate: 20,
        // 商品分润规则
        goods_platform_rate: 50,
        goods_leader_rate: 30,
        goods_member_rate: 20,
        // 广告分润规则
        ad_platform_rate: 60,
        ad_leader_rate: 30,
        ad_member_rate: 10,
        // 提现设置
        min_withdraw_amount: 50,
        withdraw_fee_rate: 1
      },
      rules: {
        wifi_platform_rate: [
          { required: true, message: '请输入平台分润比例', trigger: 'blur' }
        ],
        wifi_leader_rate: [
          { required: true, message: '请输入团长分润比例', trigger: 'blur' }
        ],
        wifi_member_rate: [
          { required: true, message: '请输入成员分润比例', trigger: 'blur' }
        ],
        goods_platform_rate: [
          { required: true, message: '请输入平台分润比例', trigger: 'blur' }
        ],
        goods_leader_rate: [
          { required: true, message: '请输入团长分润比例', trigger: 'blur' }
        ],
        goods_member_rate: [
          { required: true, message: '请输入成员分润比例', trigger: 'blur' }
        ],
        ad_platform_rate: [
          { required: true, message: '请输入平台分润比例', trigger: 'blur' }
        ],
        ad_leader_rate: [
          { required: true, message: '请输入团长分润比例', trigger: 'blur' }
        ],
        ad_member_rate: [
          { required: true, message: '请输入成员分润比例', trigger: 'blur' }
        ],
        min_withdraw_amount: [
          { required: true, message: '请输入最小提现金额', trigger: 'blur' }
        ],
        withdraw_fee_rate: [
          { required: true, message: '请输入提现手续费', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    getWifiTotalRate () {
      return Number(this.form.wifi_platform_rate) +
             Number(this.form.wifi_leader_rate) +
             Number(this.form.wifi_member_rate)
    },
    getGoodsTotalRate () {
      return Number(this.form.goods_platform_rate) +
             Number(this.form.goods_leader_rate) +
             Number(this.form.goods_member_rate)
    },
    getAdTotalRate () {
      return Number(this.form.ad_platform_rate) +
             Number(this.form.ad_leader_rate) +
             Number(this.form.ad_member_rate)
    }
  },
  created () {
    this.fetchRules()
  },
  methods: {
    fetchRules () {
      this.loading = true
      getProfitRules().then(response => {
        // 从后端获取数据
        const data = response.data
        
        // 转换后端嵌套格式到前端扁平格式
        if (data.wifi_share) {
          this.form.wifi_platform_rate = data.wifi_share.platform_rate || 40
          this.form.wifi_leader_rate = data.wifi_share.leader_rate || 40
          this.form.wifi_member_rate = data.wifi_share.user_rate || 20
        }
        
        if (data.goods_sale) {
          this.form.goods_platform_rate = data.goods_sale.platform_rate || 50
          this.form.goods_leader_rate = data.goods_sale.leader_rate || 30
          this.form.goods_member_rate = data.goods_sale.user_rate || 20
        }
        
        if (data.advertisement) {
          this.form.ad_platform_rate = data.advertisement.platform_rate || 60
          this.form.ad_leader_rate = data.advertisement.leader_rate || 30
          this.form.ad_member_rate = data.advertisement.user_rate || 10
        }
        
        // 提现设置
        this.form.min_withdraw_amount = data.min_withdraw_amount || 50
        this.form.withdraw_fee_rate = data.withdraw_fee_rate || 1
        
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    submitForm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 检查分润比例总和是否为100%
          if (this.getWifiTotalRate !== 100 || this.getGoodsTotalRate !== 100 || this.getAdTotalRate !== 100) {
            this.$confirm('分润比例总和不等于100%，确认要保存吗？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.saveRules()
            }).catch(() => {})
          } else {
            this.saveRules()
          }
        }
      })
    },
    saveRules () {
      this.loading = true
      
      // 转换前端扁平格式到后端嵌套格式
      const postData = {
        wifi_share: {
          name: 'WiFi分享',
          platform_rate: this.form.wifi_platform_rate,
          leader_rate: this.form.wifi_leader_rate,
          user_rate: this.form.wifi_member_rate,
          status: 1
        },
        goods_sale: {
          name: '商品销售',
          platform_rate: this.form.goods_platform_rate,
          leader_rate: this.form.goods_leader_rate,
          user_rate: this.form.goods_member_rate,
          status: 1
        },
        advertisement: {
          name: '广告点击',
          platform_rate: this.form.ad_platform_rate,
          leader_rate: this.form.ad_leader_rate,
          user_rate: this.form.ad_member_rate,
          status: 1
        },
        min_withdraw_amount: this.form.min_withdraw_amount,
        withdraw_fee_rate: this.form.withdraw_fee_rate
      }
      
      updateProfitRules(postData).then(response => {
        this.$message.success('保存成功')
        // 更新成功后重新获取最新数据
        this.fetchRules()
      }).catch(() => {
        this.loading = false
        this.$message.error('保存失败')
      })
    },
    resetForm () {
      this.$refs.form.resetFields()
      this.fetchRules()
    }
  }
}
</script>

<style scoped>
.unit {
  margin-left: 8px;
}
.rate-info {
  margin-bottom: 20px;
  font-size: 14px;
  text-align: right;
}
.info {
  color: #606266;
}
.error {
  color: #F56C6C;
  margin-left: 10px;
}
</style>
