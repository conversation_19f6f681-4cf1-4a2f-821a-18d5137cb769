import request from '@/utils/request'

// 获取钱包列表
export function getWalletList(params) {
  return request({
    url: '/api/v1/admin/wallet/list',
    method: 'get',
    params
  })
}

// 获取钱包详情
export function getWalletDetail(userId) {
  return request({
    url: `/api/v1/admin/wallet/detail/${userId}`,
    method: 'get'
  })
}

// 调整用户余额
export function adjustBalance(userId, data) {
  return request({
    url: `/api/v1/admin/wallet/adjust/${userId}`,
    method: 'post',
    data
  })
}

// 获取交易记录
export function getTransactions(userId, params) {
  return request({
    url: `/api/v1/admin/wallet/transactions/${userId}`,
    method: 'get',
    params
  })
}

// 获取钱包统计
export function getWalletStats() {
  return request({
    url: '/api/v1/admin/wallet/stats',
    method: 'get'
  })
}

// 调整钱包余额
export function adjustWalletBalance(data) {
  return request({
    url: '/api/v1/admin/wallet/adjust',
    method: 'post',
    data
  })
}

// 获取钱包交易记录
export function getWalletTransactions(userId, params) {
  return request({
    url: `/api/v1/admin/wallet/transactions/${userId}`,
    method: 'get',
    params
  })
}
