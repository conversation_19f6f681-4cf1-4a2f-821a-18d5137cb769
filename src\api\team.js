import request from '@/utils/request'

// 获取团队列表
export function getTeamList(params) {
  return request({
    url: '/api/v1/admin/team/list',
    method: 'get',
    params
  })
}

// 获取团队详情
export function getTeamDetail(id) {
  return request({
    url: `/api/v1/admin/team/detail/${id}`,
    method: 'get'
  })
}

// 创建团队
export function createTeam(data) {
  return request({
    url: '/api/v1/admin/team/create',
    method: 'post',
    data
  })
}

// 更新团队
export function updateTeam(id, data) {
  return request({
    url: `/api/v1/admin/team/update/${id}`,
    method: 'put',
    data
  })
}

// 删除团队
export function deleteTeam(id) {
  return request({
    url: `/api/v1/admin/team/delete/${id}`,
    method: 'delete'
  })
}

// 获取团队统计数据
export function getTeamStats(id) {
  return request({
    url: `/api/v1/admin/team/stats/${id}`,
    method: 'get'
  })
}

// 获取团队成员列表
export function getTeamMembers(teamId, params) {
  return request({
    url: `/api/v1/admin/team/${teamId}/members`,
    method: 'get',
    params
  })
}

// 添加团队成员
export function addTeamMember(teamId, userId) {
  return request({
    url: `/api/v1/admin/team/${teamId}/members`,
    method: 'post',
    data: { user_id: userId }
  })
}

// 移除团队成员
export function removeTeamMember(teamId, userId) {
  return request({
    url: `/api/v1/admin/team/${teamId}/members/${userId}`,
    method: 'delete'
  })
}

// 设置成员等级
export function setMemberLevel(teamId, userId, level) {
  return request({
    url: `/api/v1/admin/team/${teamId}/members/${userId}/level`,
    method: 'put',
    data: { level }
  })
}

// 搜索用户（用于添加成员）
export function searchUsers(params) {
  return request({
    url: '/api/v1/admin/user/search',
    method: 'get',
    params
  })
} 

// 获取可选团长列表
export function getAvailableLeaders() {
  return request({
    url: '/api/v1/admin/team/available-leaders',
    method: 'get'
  })
}

// 移除成员（别名函数）
export function removeMember(teamId, userId) {
  return removeTeamMember(teamId, userId)
}