(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-fffc8e28"],{"487e":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"app-container"},[t("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("基本信息")])]),t("el-form-item",{attrs:{label:"商品标题",prop:"title"}},[t("el-input",{attrs:{placeholder:"请输入商品标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1),t("el-form-item",{attrs:{label:"商品分类",prop:"category_id"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择分类"},model:{value:e.form.category_id,callback:function(t){e.$set(e.form,"category_id",t)},expression:"form.category_id"}},e._l(e.categoryOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"商品封面",prop:"cover"}},[t("el-upload",{staticClass:"avatar-uploader",attrs:{action:"/api/v1/admin/upload","show-file-list":!1,"http-request":e.customUploadCover,"before-upload":e.beforeUpload}},[e.form.cover?t("img",{staticClass:"avatar",attrs:{src:e.form.cover}}):t("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),t("el-form-item",{attrs:{label:"商品图片",prop:"images"}},[t("el-upload",{attrs:{action:"/api/v1/admin/upload","list-type":"picture-card","file-list":e.fileList,"http-request":e.customUploadImage,"on-preview":e.handlePictureCardPreview,"on-remove":e.handleRemove,"before-upload":e.beforeUpload}},[t("i",{staticClass:"el-icon-plus"})]),t("el-dialog",{attrs:{visible:e.dialogVisible},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("img",{attrs:{width:"100%",src:e.dialogImageUrl,alt:""}})])],1)],1),t("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("价格与库存")])]),t("el-form-item",{attrs:{label:"售价",prop:"price"}},[t("el-input-number",{attrs:{precision:2,step:.1,min:0},model:{value:e.form.price,callback:function(t){e.$set(e.form,"price",t)},expression:"form.price"}})],1),t("el-form-item",{attrs:{label:"原价",prop:"original_price"}},[t("el-input-number",{attrs:{precision:2,step:.1,min:0},model:{value:e.form.original_price,callback:function(t){e.$set(e.form,"original_price",t)},expression:"form.original_price"}})],1),t("el-form-item",{attrs:{label:"库存",prop:"stock"}},[t("el-input-number",{attrs:{min:0},model:{value:e.form.stock,callback:function(t){e.$set(e.form,"stock",t)},expression:"form.stock"}})],1)],1),t("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("商品属性")])]),t("el-form-item",{attrs:{label:"推荐商品",prop:"is_recommend"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.form.is_recommend,callback:function(t){e.$set(e.form,"is_recommend",t)},expression:"form.is_recommend"}})],1),t("el-form-item",{attrs:{label:"热门商品",prop:"is_hot"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.form.is_hot,callback:function(t){e.$set(e.form,"is_hot",t)},expression:"form.is_hot"}})],1),t("el-form-item",{attrs:{label:"新品",prop:"is_new"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.form.is_new,callback:function(t){e.$set(e.form,"is_new",t)},expression:"form.is_new"}})],1),t("el-form-item",{attrs:{label:"上架状态",prop:"status"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}})],1)],1),t("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("商品详情")])]),t("el-form-item",{attrs:{label:"商品描述",prop:"description"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入商品简短描述"},model:{value:e.form.description,callback:function(t){e.$set(e.form,"description",t)},expression:"form.description"}})],1),t("el-form-item",{attrs:{label:"详情内容",prop:"details"}},[t("div",{staticClass:"editor-container"},[t("el-input",{attrs:{type:"textarea",rows:10,placeholder:"请输入商品详细描述"},model:{value:e.form.details,callback:function(t){e.$set(e.form,"details",t)},expression:"form.details"}})],1)])],1),t("el-form-item",{staticStyle:{"margin-top":"20px"}},[t("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.isEdit?"更新":"创建"))]),t("el-button",{on:{click:e.cancel}},[e._v("取消")])],1)],1)],1)},s=[],i=(r("14d9"),r("e9f5"),r("ab43"),r("c40e")),o=r("91b6"),l={name:"GoodsForm",data(){return{isEdit:!1,goodsId:null,form:{title:"",category_id:void 0,cover:"",images:[],price:0,original_price:0,stock:0,description:"",details:"",is_recommend:0,is_hot:0,is_new:0,status:1},rules:{title:[{required:!0,message:"请输入商品标题",trigger:"blur"},{max:100,message:"长度不能超过100个字符",trigger:"blur"}],category_id:[{required:!0,message:"请选择商品分类",trigger:"change"}],cover:[{required:!0,message:"请上传商品封面",trigger:"change"}],price:[{required:!0,message:"请输入售价",trigger:"blur"}],stock:[{required:!0,message:"请输入库存",trigger:"blur"}]},categoryOptions:[{label:"数码产品",value:1},{label:"家居用品",value:2},{label:"美妆护肤",value:3},{label:"食品饮料",value:4}],fileList:[],dialogVisible:!1,dialogImageUrl:""}},created(){this.$route.params.id&&(this.isEdit=!0,this.goodsId=parseInt(this.$route.params.id),this.getDetail())},methods:{getDetail(){Object(i["d"])(this.goodsId).then(e=>{if(this.form=e.data,this.form.images)try{const e="string"===typeof this.form.images?JSON.parse(this.form.images):this.form.images;this.fileList=e.map((e,t)=>({name:"商品图片"+(t+1),url:e}))}catch(t){console.error("解析商品图片失败",t),this.fileList=[]}}).catch(e=>{console.error("获取商品详情失败:",e)})},submitForm(){this.$refs.form.validate(e=>{if(!e)return!1;{const e=this.fileList.map(e=>e.url),t={...this.form,name:this.form.title,category_id:this.form.category_id,images:e,is_recommend:this.form.is_recommend?1:0,is_hot:this.form.is_hot?1:0,is_new:this.form.is_new?1:0,status:this.form.status?1:0};delete t.title,console.log("提交的表单数据:",t),this.isEdit?Object(i["f"])(this.goodsId,t).then(e=>{this.$message.success("更新成功"),this.goBack()}).catch(e=>{console.error("更新失败:",e),this.$message.error("更新失败: "+(e.message||"未知错误"))}):Object(i["a"])(t).then(e=>{this.$message.success("创建成功"),this.goBack()}).catch(e=>{console.error("创建失败:",e),this.$message.error("创建失败: "+(e.message||"未知错误"))})}})},cancel(){this.goBack()},goBack(){this.$router.push("/mall/goods")},beforeUpload(e){const t=0===e.type.indexOf("image/"),r=e.size/1024/1024<5;return t?r?t&&r:(this.$message.error("上传图片大小不能超过 5MB!"),!1):(this.$message.error("上传图片只能是图片格式!"),!1)},customUploadCover(e){const{file:t}=e;Object(o["a"])(t).then(e=>{const t=e.data.url;this.form.cover=t.includes("/uploads/")?t.substring(t.indexOf("/uploads/")):t,console.log("封面上传成功，URL:",this.form.cover),this.$message.success("封面上传成功")}).catch(e=>{console.error("上传失败:",e),this.$message.error("封面上传失败")})},customUploadImage(e){const{file:t}=e;Object(o["a"])(t).then(e=>{const r=e.data.url,a=r.includes("/uploads/")?r.substring(r.indexOf("/uploads/")):r,s={name:t.name,url:a};console.log("图片上传成功，URL:",s.url),this.fileList.push(s),this.$message.success("图片上传成功")}).catch(e=>{console.error("上传失败:",e),this.$message.error("图片上传失败")})},handleRemove(e,t){this.fileList=t},handlePictureCardPreview(e){this.dialogImageUrl=e.url,this.dialogVisible=!0}}},c=l,n=(r("83e4"),r("2877")),m=Object(n["a"])(c,a,s,!1,null,null,null);t["default"]=m.exports},"83e4":function(e,t,r){"use strict";r("e6b9")},"91b6":function(e,t,r){"use strict";r.d(t,"a",(function(){return o}));var a=r("b775");const s=!1;function i(e){const t=Date.now(),r=Math.floor(1e4*Math.random()),a=e.split(".").pop();return`${t}_${r}.${a}`}function o(e){if(s)return new Promise(t=>{setTimeout(()=>{const r=new FileReader;r.onload=r=>{const a=r.target.result,s=i(e.name);t({code:200,data:{url:a,filename:s,size:e.size,type:e.type},message:"上传成功"})},r.readAsDataURL(e)},500)});const t=new FormData;return t.append("file",e),Object(a["a"])({url:"/api/v1/admin/upload",method:"post",data:t,headers:{"Content-Type":"multipart/form-data"}})}},ab43:function(e,t,r){"use strict";var a=r("23e7"),s=r("c65b"),i=r("59ed"),o=r("825a"),l=r("46c4"),c=r("c5cc"),n=r("9bdd"),m=r("2a62"),d=r("2baa"),u=r("f99f"),p=r("c430"),f=!p&&!d("map",(function(){})),h=!p&&!f&&u("map",TypeError),g=p||f||h,b=c((function(){var e=this.iterator,t=o(s(this.next,e)),r=this.done=!!t.done;if(!r)return n(e,this.mapper,[t.value,this.counter++],!0)}));a({target:"Iterator",proto:!0,real:!0,forced:g},{map:function(e){o(this);try{i(e)}catch(t){m(this,"throw",t)}return h?s(h,this,e):new b(l(this),{mapper:e})}})},c40e:function(e,t,r){"use strict";r.d(t,"e",(function(){return s})),r.d(t,"d",(function(){return i})),r.d(t,"a",(function(){return o})),r.d(t,"f",(function(){return l})),r.d(t,"g",(function(){return c})),r.d(t,"b",(function(){return n})),r.d(t,"c",(function(){return m}));var a=r("b775");function s(e){return Object(a["a"])({url:"/api/v1/admin/goods/list",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/api/v1/admin/goods/detail/"+e,method:"get"})}function o(e){return Object(a["a"])({url:"/api/v1/admin/goods/create",method:"post",data:e})}function l(e,t){return Object(a["a"])({url:"/api/v1/admin/goods/update/"+e,method:"put",data:t})}function c(e,t){return Object(a["a"])({url:"/api/v1/admin/goods/status/"+e,method:"put",data:t})}function n(e){return console.log(`删除商品 ID: ${e}, 请求URL: /api/v1/admin/goods/delete/${e}`),Object(a["a"])({url:"/api/v1/admin/goods/delete/"+e,method:"delete"})}function m(e){return e?e.startsWith("http://")||e.startsWith("https://")||e.startsWith("/")?e:"/uploads/"+e:"/uploads/default-goods.jpg"}},e6b9:function(e,t,r){}}]);