const axios = require('axios');

async function testProxy() {
  try {
    console.log('🔍 测试前端代理功能...\n');
    
    // 测试通过前端代理访问API
    console.log('1️⃣ 测试代理登录API...');
    const response = await axios.post('http://localhost:3000/api/v1/admin/auth/admin-login', {
      username: 'mrx0927',
      password: 'hh20250701'
    });
    
    console.log('✅ 代理登录成功!');
    console.log('状态码:', response.status);
    console.log('响应数据:', response.data);
    
  } catch (error) {
    console.log('❌ 代理登录失败:', error.message);
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('错误信息:', error.response.data);
    }
  }
}

testProxy();
