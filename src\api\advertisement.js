import request from '@/utils/request'

// 格式化图片URL，确保前缀正确
export function formatImageUrl(url) {
  if (!url) {
    return '/uploads/default-ad.jpg'
  }
  
  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  
  // 如果是相对路径，添加前缀
  if (url.startsWith('/')) {
    return url
  } else {
    return `/uploads/${url}`
  }
}

// 广告位列表
export function getAdSpaceList(query) {
  return request({
    url: '/api/v1/admin/advertisement/spaces',
    method: 'get',
    params: query
  })
}

// 创建广告位
export function createAdSpace(data) {
  return request({
    url: '/api/v1/admin/advertisement/spaces',
    method: 'post',
    data
  })
}

// 更新广告位
export function updateAdSpace(id, data) {
  return request({
    url: `/api/v1/admin/advertisement/spaces/${id}`,
    method: 'put',
    data
  })
}

// 删除广告位
export function deleteAdSpace(id) {
  return request({
    url: `/api/v1/admin/advertisement/spaces/${id}`,
    method: 'delete'
  })
}

// 更新广告位状态
export function updateAdSpaceStatus(id, status) {
  return request({
    url: `/api/v1/admin/advertisement/spaces/${id}`,
    method: 'put',
    data: { status }
  })
}

// 获取广告位分类
export function getAdCategories() {
  return request({
    url: '/api/v1/admin/advertisement/categories',
    method: 'get'
  })
}

// 广告内容列表
export function getAdContentList(query) {
  return request({
    url: '/api/v1/admin/advertisement/contents',
    method: 'get',
    params: query
  })
}

// 创建广告内容
export function createAdContent(data) {
  return request({
    url: '/api/v1/admin/advertisement/contents',
    method: 'post',
    data
  })
}

// 更新广告内容
export function updateAdContent(id, data) {
  return request({
    url: `/api/v1/admin/advertisement/contents/${id}`,
    method: 'put',
    data
  })
}

// 删除广告内容
export function deleteAdContent(id) {
  return request({
    url: `/api/v1/admin/advertisement/contents/${id}`,
    method: 'delete'
  })
}

// 更新广告内容状态
export function updateAdContentStatus(id, data) {
  // 确保status是数字而不是对象
  const status = data && data.status !== undefined ? data.status : 0;
  
  return request({
    url: `/api/v1/admin/advertisement/contents/${id}`,
    method: 'put',
    data: { status }
  })
}

// 获取广告内容详情
export function getAdContentDetail(id) {
  return request({
    url: `/api/v1/admin/advertisement/contents/${id}`,
    method: 'get'
  })
}

// 获取广告位编码指南
export function getAdCodeGuide() {
  return request({
    url: '/api/v1/admin/advertisement/code-guide',
    method: 'get'
  })
}
