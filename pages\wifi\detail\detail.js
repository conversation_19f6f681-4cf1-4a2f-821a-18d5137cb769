// pages/wifi/detail/detail.js
// WiFi码详情页面

const request = require('../../../utils/request.js')
const util = require('../../../utils/util.js')
const API = require('../../../config/api.js')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // WiFi码ID
    wifiId: '',
    
    // WiFi码详细信息
    wifiInfo: {
      id: '',
      title: '',
      ssid: '',
      password: '',
      merchantName: '',
      status: 'active',
      qrCodeUrl: '',
      createTime: '',
      updateTime: '',
      ad_enabled: false, // 是否启用广告
      ad_type: 'video' // 广告类型
    },
    
    // 统计数据
    statsData: {
      scanCount: 0,         // 扫码次数
      connectCount: 0,      // 连接次数
      todayCount: 0,        // 今日连接
      totalEarnings: 0,     // 总收益
      todayEarnings: 0,     // 今日收益
      dailyAverage: 0,      // 日均连接数
      weeklyConnections: 0, // 本周连接
      monthlyConnections: 0,// 本月连接
      monthlyEarnings: 0    // 本月收益
    },
    
    // 推荐商品
    recommendGoods: [],
    
    // 加载状态
    loading: true,
    showPassword: false,
    showStatsDetails: false, // 是否显示详细统计
    localQrCodeUrl: '', // 本地生成的二维码URL
    
    // 广告数据
    adData: {
      title: '推广位招租',
      subtitle: '点击了解详情',
      image: '/assets/images/ad-placeholder.jpg'
    },
    
    // 广告模式开关
    adModeEnabled: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const { id } = options
    if (!id) {
      wx.showToast({
        title: 'WiFi码ID不能为空',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }
    
    this.setData({ wifiId: id })
    this.loadWifiDetail()
    this.loadRecommendGoods()
  },

  /**
   * 加载WiFi码详情
   */
  async loadWifiDetail() {
    try {
      wx.showLoading({
        title: '加载中...'
      })

      const result = await request.get(`/wifi/${this.data.wifiId}`)
      
      wx.hideLoading()

      if (result.success) {
        const wifiInfo = result.data
        console.log('获取到WiFi详情数据:', wifiInfo)
        
        // 确保字段名称正确
        if (!wifiInfo.ssid && wifiInfo.name) {
          wifiInfo.ssid = wifiInfo.name // 兼容后端返回name字段而不是ssid
        }
        
        if (!wifiInfo.merchantName && wifiInfo.merchant_name) {
          wifiInfo.merchantName = wifiInfo.merchant_name // 兼容后端返回merchant_name字段而不是merchantName
        }
        
        // 格式化时间
        wifiInfo.createTime = util.formatTime(new Date(wifiInfo.createTime))
        wifiInfo.updateTime = util.formatTime(new Date(wifiInfo.updateTime))
        
        // 处理统计数据，如果返回数据不完整，使用默认值
        const statsData = {
          ...this.data.statsData,
          ...wifiInfo.stats || {}
        }
        
        // 计算额外的统计信息（如果后端没有提供）
        if (!statsData.dailyAverage && wifiInfo.createTime) {
          const daysSinceCreation = this.calculateDaysSinceCreation(wifiInfo.createTime);
          if (daysSinceCreation > 0) {
            statsData.dailyAverage = Math.round((statsData.connectCount || 0) / daysSinceCreation * 10) / 10;
          }
        }
        
        // 检查是否启用广告
        const adModeEnabled = wifiInfo.ad_enabled || wifiInfo.ad_required === 1;
        
        console.log('设置WiFi详情页面数据:', {
          wifiInfo: wifiInfo,
          statsData: statsData,
          adModeEnabled: adModeEnabled
        })
        
        // 设置数据
        this.setData({
          wifiInfo: wifiInfo,
          statsData: statsData,
          adModeEnabled: adModeEnabled,
          loading: false
        })
        
        // 获取真实的WiFi二维码
        this.getWifiQRCode(wifiInfo.ssid, wifiInfo.password);
        
        // 保存到本地缓存，以便离线使用
        try {
          wx.setStorageSync(`wifi_${wifiInfo.id}`, JSON.stringify(wifiInfo))
        } catch(e) {
          console.error('保存WiFi信息到缓存失败:', e)
        }
      } else {
        wx.showToast({
          title: result.message || '加载失败',
          icon: 'none'
        })
        // 即使API返回错误，也尝试本地生成二维码
        this.initLocalWiFiInfo()
      }
    } catch (error) {
      wx.hideLoading()
      console.error('加载WiFi码详情失败:', error)
      wx.showToast({
        title: '网络错误，尝试本地显示',
        icon: 'none'
      })
      // 出现网络错误时，也尝试本地生成二维码
      this.initLocalWiFiInfo()
    }
  },
  
  /**
   * 获取WiFi二维码
   */
  async getWifiQRCode(ssid, password) {
    if (!ssid || !password) return;
    
    try {
      // 调用后端API获取二维码
      const result = await request.get(API.wifi.qrcode, {
        ssid: ssid,
        password: password,
        encryption: 'WPA',
        hidden: 'false',
        adEnabled: this.data.adModeEnabled ? 'true' : 'false' // 添加广告模式参数
      });
      
      if (result.status === 'success' && result.data && result.data.qrcode_url) {
        console.log('获取到WiFi二维码URL:', result.data.qrcode_url);
        
        // 更新二维码URL
        const wifiInfo = { ...this.data.wifiInfo };
        wifiInfo.qrCodeUrl = result.data.qrcode_url;
        
        this.setData({ wifiInfo });
      } else {
        console.log('获取WiFi二维码失败，使用本地生成方式');
      }
    } catch (error) {
      console.error('获取WiFi二维码失败:', error);
      // 失败时不做特殊处理，将使用组件自身的二维码生成能力
    }
  },
  
  /**
   * 当API加载失败时，初始化本地WiFi信息
   */
  initLocalWiFiInfo() {
    try {
      // 尝试从缓存获取该WiFi码信息
      const cachedInfo = wx.getStorageSync(`wifi_${this.data.wifiId}`)
      if (cachedInfo) {
        const wifiInfo = JSON.parse(cachedInfo);
        
        // 确保字段名称正确
        if (!wifiInfo.ssid && wifiInfo.name) {
          wifiInfo.ssid = wifiInfo.name;
        }
        
        if (!wifiInfo.merchantName && wifiInfo.merchant_name) {
          wifiInfo.merchantName = wifiInfo.merchant_name;
        }
        
        this.setData({
          wifiInfo: wifiInfo,
          loading: false
        })
        return
      }
    } catch(e) {
      console.error('读取缓存数据失败:', e)
    }
    
    // 如果缓存也没有，显示空数据和提示
    this.setData({
      wifiInfo: {
        id: this.data.wifiId,
        title: 'WiFi码详情',
        ssid: '加载失败',
        password: '加载失败',
        merchantName: '加载失败',
        status: 'inactive',
        createTime: util.formatTime(new Date())
      },
      loading: false
    })
    
    wx.showModal({
      title: '提示',
      content: '无法加载WiFi码详情，请检查网络连接后重试',
      showCancel: false
    })
  },

  /**
   * 计算创建至今的天数
   */
  calculateDaysSinceCreation(createTimeStr) {
    try {
      const now = new Date();
      // 解析格式化的时间字符串 (2023-05-01 12:34:56)
      const parts = createTimeStr.split(' ');
      const dateParts = parts[0].split('-');
      
      const createDate = new Date(
        parseInt(dateParts[0]), 
        parseInt(dateParts[1]) - 1, // 月份从0开始
        parseInt(dateParts[2])
      );
      
      const diffTime = Math.abs(now - createDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)); 
      return diffDays || 1; // 至少返回1天
    } catch (e) {
      console.error('计算创建天数失败:', e);
      return 1;
    }
  },

  /**
   * QR码生成完成事件处理
   */
  onQrCodeGenerated(e) {
    const { imageUrl } = e.detail
    
    if (imageUrl) {
      this.setData({
        localQrCodeUrl: imageUrl
      })
      
      // 尝试保存WiFi码基本信息到缓存，以便离线使用
      try {
        const { wifiInfo } = this.data
        wx.setStorageSync(`wifi_${wifiInfo.id}`, JSON.stringify(wifiInfo))
      } catch(e) {
        console.error('保存WiFi信息到缓存失败:', e)
      }
    }
  },
  
  /**
   * QR码点击事件处理
   */
  onQrCodeTap() {
    const imageUrl = this.data.localQrCodeUrl || this.data.wifiInfo.qrCodeUrl
    
    if (imageUrl) {
      wx.previewImage({
        urls: [imageUrl],
        current: imageUrl
      })
    }
  },

  /**
   * 加载推荐商品
   */
  async loadRecommendGoods() {
    try {
      const result = await request.get('/goods/recommend', {
        limit: 3
      })
      
      if (result.success) {
        this.setData({
          recommendGoods: result.data.list || []
        })
      }
    } catch (error) {
      console.error('加载推荐商品失败:', error)
    }
  },

  /**
   * 显示/隐藏密码
   */
  onTogglePassword() {
    this.setData({
      showPassword: !this.data.showPassword
    })
  },
  
  /**
   * 切换统计详情显示/隐藏
   */
  toggleStatsDetails() {
    this.setData({
      showStatsDetails: !this.data.showStatsDetails
    })
  },

  /**
   * 复制WiFi信息
   */
  onCopyWifiInfo(e) {
    const { type } = e.currentTarget.dataset
    const { wifiInfo } = this.data
    
    let copyText = ''
    let toastText = ''
    
    switch (type) {
      case 'ssid':
        copyText = wifiInfo.ssid
        toastText = 'WiFi名称已复制'
        break
      case 'password':
        copyText = wifiInfo.password
        toastText = 'WiFi密码已复制'
        break
      case 'all':
        copyText = `WiFi名称：${wifiInfo.ssid}\nWiFi密码：${wifiInfo.password}\n商户：${wifiInfo.merchantName}`
        toastText = 'WiFi信息已复制'
        break
    }
    
    if (copyText) {
      wx.setClipboardData({
        data: copyText,
        success: () => {
          wx.showToast({
            title: toastText,
            icon: 'success'
          })
        }
      })
    }
  },

  /**
   * 编辑WiFi码
   */
  onEditWifi() {
    wx.navigateTo({
      url: `/pages/wifi/create/create?id=${this.data.wifiId}&mode=edit`
    })
  },

  /**
   * 删除WiFi码
   */
  onDeleteWifi() {
    const { wifiInfo } = this.data
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除WiFi码"${wifiInfo.title}"吗？删除后无法恢复。`,
      success: async (res) => {
        if (res.confirm) {
          try {
            wx.showLoading({
              title: '删除中...'
            })

            const result = await request.delete(`/wifi/${this.data.wifiId}`)
            
            wx.hideLoading()

            if (result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              })
              
              // 删除本地缓存
              try {
                wx.removeStorageSync(`wifi_${this.data.wifiId}`)
              } catch(e) {
                console.error('删除缓存数据失败:', e)
              }
              
              setTimeout(() => {
                wx.navigateBack()
              }, 1500)
            } else {
              wx.showToast({
                title: result.message || '删除失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.hideLoading()
            console.error('删除WiFi码失败:', error)
            wx.showToast({
              title: '网络错误，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  /**
   * 打印WiFi码
   */
  onPrintWifi() {
    // 优先使用本地生成的二维码，其次使用后端返回的二维码
    const qrCodeUrl = this.data.localQrCodeUrl || this.data.wifiInfo.qrCodeUrl
    
    if (!qrCodeUrl) {
      wx.showToast({
        title: '二维码生成中，请稍后',
        icon: 'none'
      })
      return
    }
    
    wx.showActionSheet({
      itemList: ['预览打印', '保存到相册', '生成海报'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.previewPrint(qrCodeUrl)
            break
          case 1:
            this.saveToAlbum(qrCodeUrl)
            break
          case 2:
            this.generateSharePoster()
            break
        }
      }
    })
  },

  /**
   * 预览打印
   */
  previewPrint(qrCodeUrl) {
    if (qrCodeUrl) {
      wx.previewImage({
        urls: [qrCodeUrl],
        current: qrCodeUrl
      })
    } else {
      wx.showToast({
        title: '二维码生成中，请稍后',
        icon: 'none'
      })
    }
  },

  /**
   * 保存到相册
   */
  async saveToAlbum(qrCodeUrl) {
    if (!qrCodeUrl) {
      wx.showToast({
        title: '二维码生成中，请稍后',
        icon: 'none'
      })
      return
    }

    try {
      wx.showLoading({
        title: '保存中...'
      })

      // 下载图片（如果是网络图片）或直接使用本地临时路径
      let tempFilePath = qrCodeUrl
      if (qrCodeUrl.startsWith('http')) {
        const downloadRes = await new Promise((resolve, reject) => {
          wx.downloadFile({
            url: qrCodeUrl,
            success: resolve,
            fail: reject
          })
        })
        tempFilePath = downloadRes.tempFilePath
      }

      // 保存到相册
      await new Promise((resolve, reject) => {
        wx.saveImageToPhotosAlbum({
          filePath: tempFilePath,
          success: resolve,
          fail: reject
        })
      })

      wx.hideLoading()
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
    } catch (error) {
      wx.hideLoading()
      console.error('保存图片失败:', error)
      
      if (error.errMsg && error.errMsg.includes('auth')) {
        wx.showModal({
          title: '提示',
          content: '需要您授权保存图片到相册',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting()
            }
          }
        })
      } else {
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        })
      }
    }
  },

  /**
   * 分享WiFi码
   */
  onShareWifi() {
    wx.showActionSheet({
      itemList: ['生成分享海报', '分享给朋友', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.generateSharePoster()
            break
          case 1:
            this.shareToFriend()
            break
          case 2:
            this.copyShareLink()
            break
        }
      }
    })
  },

  /**
   * 生成分享海报
   */
  async generateSharePoster() {
    try {
      const { wifiInfo } = this.data;
      
      if (!wifiInfo.ssid || !wifiInfo.password) {
        wx.showToast({
          title: 'WiFi信息不完整',
          icon: 'none'
        });
        return;
      }
      
      // 显示海报样式选择
      wx.showActionSheet({
        itemList: ['商务风格', '咖啡馆风格', '现代简约', '缤纷色彩', '极简风格', '默认蓝色'],
        success: async (res) => {
          // 根据选择的索引确定样式
          const styles = ['business', 'cafe', 'modern', 'colorful', 'minimal', 'default'];
          const selectedStyle = styles[res.tapIndex] || 'default';
          
          wx.showLoading({
            title: '生成中...'
          });
          
          try {
            // 调用海报生成API
            const result = await request.get(API.wifi.poster, {
              ssid: wifiInfo.ssid,
              password: wifiInfo.password,
              title: wifiInfo.title || '免费WiFi',
              merchant: wifiInfo.merchantName || '',
              style: selectedStyle
            });
            
            wx.hideLoading();
            
            if (result.status === 'success' && result.data && result.data.poster_data) {
              console.log('获取到WiFi海报数据:', result.data);
              
              // 使用返回的海报数据绘制本地海报
              await this.drawLocalPoster(result.data.poster_data);
            } else {
              throw new Error('获取海报数据失败');
            }
          } catch (error) {
            wx.hideLoading();
            console.error('生成分享海报失败:', error);
            
            // 尝试使用本地Canvas绘制简单海报
            try {
              await this.drawLocalPoster({
                title: wifiInfo.title || '免费WiFi',
                ssid: wifiInfo.ssid,
                merchant: wifiInfo.merchantName || '',
                style: selectedStyle,
                background: '#1976D2',
                textColor: '#FFFFFF',
                accentColor: '#BBDEFB'
              });
            } catch (canvasError) {
              wx.showToast({
                title: '海报生成失败',
                icon: 'none'
              });
            }
          }
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('生成分享海报失败:', error);
      wx.showToast({
        title: '海报生成失败',
        icon: 'none'
      });
    }
  },
  
  /**
   * 使用本地Canvas绘制WiFi海报
   */
  async drawLocalPoster(posterData) {
    return new Promise(async (resolve, reject) => {
      try {
        const { wifiInfo } = this.data;
        const qrCodeUrl = this.data.localQrCodeUrl || wifiInfo.qrCodeUrl || posterData.qrcode;
        
        if (!qrCodeUrl) {
          reject(new Error('二维码不存在'));
          return;
        }
        
        // 创建离屏Canvas
        const canvas = wx.createOffscreenCanvas({
          type: '2d',
          width: 750,
          height: 1100
        });
        
        const ctx = canvas.getContext('2d');
        
        // 根据样式设置颜色
        const background = posterData.background || '#1976D2';
        const textColor = posterData.textColor || '#FFFFFF';
        const accentColor = posterData.accentColor || '#BBDEFB';
        
        // 绘制背景
        ctx.fillStyle = background;
        ctx.fillRect(0, 0, 750, 1100);
        
        // 绘制标题区域
        ctx.fillStyle = accentColor;
        ctx.fillRect(0, 0, 750, 180);
        
        // 绘制WiFi图标
        ctx.fillStyle = textColor;
        ctx.beginPath();
        ctx.arc(375, 100, 40, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.fillStyle = background;
        ctx.beginPath();
        ctx.arc(375, 100, 25, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制WiFi波纹
        ctx.strokeStyle = background;
        ctx.lineWidth = 8;
        for (let i = 0; i < 3; i++) {
          ctx.beginPath();
          ctx.arc(375, 100, 60 + i * 20, Math.PI, 0);
          ctx.stroke();
        }
        
        // 绘制标题
        ctx.fillStyle = textColor;
        ctx.font = 'bold 60px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('免费WiFi', 375, 260);
        
        // 绘制WiFi名称
        ctx.fillStyle = textColor;
        ctx.font = 'bold 48px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(posterData.title || wifiInfo.title || '免费WiFi', 375, 340);
        
        // 绘制SSID
        ctx.font = '40px sans-serif';
        ctx.fillText(`网络名称: ${posterData.ssid || wifiInfo.ssid}`, 375, 420);
        
        // 不显示密码，而是提示扫码连接
        ctx.fillStyle = accentColor;
        ctx.font = 'bold 36px sans-serif';
        ctx.fillText('扫描二维码自动连接', 375, 480);
        
        // 如果有商户信息，则显示
        if (posterData.merchant || wifiInfo.merchantName) {
          ctx.fillStyle = textColor;
          ctx.font = '32px sans-serif';
          ctx.fillText(`提供方: ${posterData.merchant || wifiInfo.merchantName}`, 375, 540);
        }
        
        try {
          // 下载二维码
          const qrCodeImage = await new Promise((resolve, reject) => {
            const img = canvas.createImage();
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = qrCodeUrl;
          });
          
          // 绘制二维码背景
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(225, 580, 300, 300);
          
          // 绘制二维码
          const qrSize = 280;
          ctx.drawImage(
            qrCodeImage, 
            235,  // x坐标
            590,  // y坐标
            qrSize, // 宽度
            qrSize  // 高度
          );
        } catch (error) {
          console.error('加载二维码图片失败:', error);
          // 如果二维码加载失败，绘制一个占位符
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(225, 580, 300, 300);
          ctx.fillStyle = '#000000';
          ctx.font = '24px sans-serif';
          ctx.fillText('二维码加载失败', 375, 730);
        }
        
        // 绘制提示文本
        ctx.fillStyle = textColor;
        ctx.font = '32px sans-serif';
        ctx.fillText('扫描二维码连接WiFi', 375, 920);
        
        // 绘制底部信息
        ctx.fillStyle = accentColor;
        ctx.font = '28px sans-serif';
        ctx.fillText('由WiFi共享商业系统提供', 375, 1050);
        
        // 导出图片
        const tempFilePath = `${wx.env.USER_DATA_PATH}/wifi_poster_${Date.now()}.png`;
        
        // 将Canvas内容转为图片
        const buffer = canvas.toDataURL('image/png');
        
        // 保存图片到本地
        const fs = wx.getFileSystemManager();
        fs.writeFile({
          filePath: tempFilePath,
          data: buffer.replace(/^data:image\/\w+;base64,/, ''),
          encoding: 'base64',
          success: () => {
            // 预览海报
            wx.previewImage({
              urls: [tempFilePath],
              current: tempFilePath
            });
            
            // 缓存海报临时路径
            this.setData({
              posterUrl: tempFilePath
            });
            
            resolve(tempFilePath);
          },
          fail: (err) => {
            console.error('保存海报失败:', err);
            reject(err);
          }
        });
      } catch (error) {
        console.error('本地绘制海报失败:', error);
        reject(error);
      }
    });
  },

  /**
   * 分享给朋友
   */
  shareToFriend() {
    // 触发页面分享
    wx.showShareMenu({
      withShareTicket: true
    })
  },

  /**
   * 复制分享链接
   */
  copyShareLink() {
    const shareUrl = `pages/wifi/detail/detail?id=${this.data.wifiId}`
    
    wx.setClipboardData({
      data: shareUrl,
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 商品点击事件
   */
  onGoodsTap(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/mall/goods/goods?id=${id}`
    })
  },

  /**
   * 广告点击事件
   */
  onAdTap() {
    wx.showToast({
      title: '广告功能开发中',
      icon: 'none'
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadWifiDetail()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const { wifiInfo } = this.data
    const imageUrl = this.data.localQrCodeUrl || wifiInfo.qrCodeUrl || '/assets/images/share-wifi-detail.jpg'
    
    return {
      title: `${wifiInfo.title} - WiFi免费连接`,
      path: `/pages/wifi/detail/detail?id=${this.data.wifiId}`,
      imageUrl: imageUrl
    }
  },

  /**
   * 切换广告模式
   */
  toggleAdMode() {
    const newAdModeEnabled = !this.data.adModeEnabled;
    
    this.setData({
      adModeEnabled: newAdModeEnabled
    });
    
    // 显示提示
    wx.showToast({
      title: newAdModeEnabled ? '已启用广告模式' : '已关闭广告模式',
      icon: 'none'
    });
    
    console.log('广告模式已' + (newAdModeEnabled ? '启用' : '关闭'));
    
    // 重新获取二维码
    const { ssid, password } = this.data.wifiInfo;
    if (ssid && password) {
      this.getWifiQRCode(ssid, password);
    }
  }
})