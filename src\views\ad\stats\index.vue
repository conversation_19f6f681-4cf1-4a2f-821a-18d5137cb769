<template>
  <div class="app-container">
    <el-card class="stats-card">
      <div slot="header" class="clearfix">
        <span>广告统计概览</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshData">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
      
      <!-- 数据概览卡片 -->
      <el-row :gutter="20" class="overview-cards">
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-icon ad-icon">
              <i class="el-icon-picture-outline"></i>
            </div>
            <div class="card-content">
              <div class="card-title">广告位总数</div>
              <div class="card-value">{{ overview.total_spaces || 0 }}</div>
              <div class="card-footer">
                <span>启用: </span>
                <span class="highlight">{{ overview.active_spaces || 0 }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-icon content-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="card-content">
              <div class="card-title">广告内容总数</div>
              <div class="card-value">{{ overview.total_contents || 0 }}</div>
              <div class="card-footer">
                <span>投放中: </span>
                <span class="highlight">{{ overview.active_contents || 0 }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-icon click-icon">
              <i class="el-icon-mouse"></i>
            </div>
            <div class="card-content">
              <div class="card-title">总点击次数</div>
              <div class="card-value">{{ formatNumber(overview.total_clicks || 0) }}</div>
              <div class="card-footer">
                <span>今日点击: </span>
                <span class="highlight">{{ formatNumber(overview.today_clicks || 0) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-icon revenue-icon">
              <i class="el-icon-coin"></i>
            </div>
            <div class="card-content">
              <div class="card-title">广告收益</div>
              <div class="card-value">¥{{ formatMoney(overview.total_revenue || 0) }}</div>
              <div class="card-footer">
                <span>今日收益: </span>
                <span class="highlight">¥{{ formatMoney(overview.today_revenue || 0) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 时间筛选 -->
      <el-row style="margin: 20px 0">
        <el-col :span="24">
          <el-form :inline="true" size="small">
            <el-form-item label="统计周期">
              <el-radio-group v-model="timePeriod" @change="handlePeriodChange">
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="30d">30天</el-radio-button>
                <el-radio-button label="90d">90天</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="自定义时间">
              <el-date-picker
                v-model="customDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleCustomDateChange"
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      
      <!-- 点击趋势图 -->
      <el-card class="chart-card">
        <div slot="header" class="clearfix">
          <span>点击趋势</span>
        </div>
        <div class="chart-container" ref="clickTrendChart"></div>
      </el-card>
      
      <!-- 收益趋势图 -->
      <el-card class="chart-card">
        <div slot="header" class="clearfix">
          <span>收益趋势</span>
        </div>
        <div class="chart-container" ref="revenueTrendChart"></div>
      </el-card>
      
      <!-- 广告位效果排行 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>广告位点击排行</span>
            </div>
            <div class="chart-container" ref="spaceRankChart"></div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>广告内容效果排行</span>
            </div>
            <div class="chart-container" ref="contentRankChart"></div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 详细数据表格 -->
      <el-card class="chart-card">
        <div slot="header" class="clearfix">
          <span>广告位详细数据</span>
        </div>
        <el-table
          v-loading="tableLoading"
          :data="spaceStats"
          border
          style="width: 100%"
        >
          <el-table-column prop="space_name" label="广告位名称" min-width="150" />
          <el-table-column prop="space_code" label="广告位编码" width="120" />
          <el-table-column prop="total_clicks" label="总点击数" width="100" />
          <el-table-column prop="today_clicks" label="今日点击" width="100" />
          <el-table-column prop="total_revenue" label="总收益" width="120">
            <template slot-scope="scope">
              ¥{{ formatMoney(scope.row.total_revenue) }}
            </template>
          </el-table-column>
          <el-table-column prop="today_revenue" label="今日收益" width="120">
            <template slot-scope="scope">
              ¥{{ formatMoney(scope.row.today_revenue) }}
            </template>
          </el-table-column>
          <el-table-column prop="click_rate" label="点击率" width="100">
            <template slot-scope="scope">
              {{ (scope.row.click_rate * 100).toFixed(2) }}%
            </template>
          </el-table-column>
          <el-table-column prop="avg_revenue" label="平均收益" width="120">
            <template slot-scope="scope">
              ¥{{ formatMoney(scope.row.avg_revenue) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
              <el-tag v-else type="danger">禁用</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.status === 1"
                size="mini"
                type="warning"
                @click="toggleSpaceStatus(scope.row, 0)"
                :loading="scope.row.loading"
              >
                禁用
              </el-button>
              <el-button
                v-else
                size="mini"
                type="success"
                @click="toggleSpaceStatus(scope.row, 1)"
                :loading="scope.row.loading"
              >
                启用
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getAdStats, getAdTrend, getAdSpaceRank, getAdContentRank, getAdSpaceStats } from '@/api/ad-stats'
import request from '@/utils/request'

export default {
  name: 'AdStats',
  data() {
    return {
      loading: false,
      tableLoading: false,
      timePeriod: '7d',
      customDateRange: [],
      overview: {},
      spaceStats: [],
      
      // 图表实例
      clickTrendChart: null,
      revenueTrendChart: null,
      spaceRankChart: null,
      contentRankChart: null,
      
      // 图表数据
      trendData: [],
      spaceRankData: [],
      contentRankData: []
    }
  },
  mounted() {
    this.initData()
  },
  beforeDestroy() {
    // 销毁图表实例
    if (this.clickTrendChart) {
      this.clickTrendChart.dispose()
    }
    if (this.revenueTrendChart) {
      this.revenueTrendChart.dispose()
    }
    if (this.spaceRankChart) {
      this.spaceRankChart.dispose()
    }
    if (this.contentRankChart) {
      this.contentRankChart.dispose()
    }
  },
  methods: {
    initData() {
      this.fetchOverview()
      this.fetchTrendData()
      this.fetchRankData()
      this.fetchSpaceStats()
    },
    refreshData() {
      this.initData()
      this.$message.success('数据已刷新')
    },
    handlePeriodChange() {
      this.customDateRange = []
      this.fetchTrendData()
      this.fetchRankData()
    },
    handleCustomDateChange(val) {
      if (val && val.length === 2) {
        this.timePeriod = 'custom'
        this.fetchTrendData()
        this.fetchRankData()
      }
    },
    async fetchOverview() {
      try {
        this.loading = true
        const { data } = await getAdStats()
        this.overview = data || {}
      } catch (error) {
        console.error('获取广告统计概览失败:', error)
        this.$message.error('获取广告统计概览失败')
        this.overview = {}
      } finally {
        this.loading = false
      }
    },
    async fetchTrendData() {
      try {
        const params = {
          period: this.timePeriod
        }
        
        if (this.customDateRange && this.customDateRange.length === 2) {
          params.start_date = this.customDateRange[0]
          params.end_date = this.customDateRange[1]
        }
        
        const { data } = await getAdTrend(params)
        this.trendData = Array.isArray(data) ? data : []

        this.$nextTick(() => {
          this.renderTrendCharts()
        })
      } catch (error) {
        console.error('获取广告趋势数据失败:', error)
        this.$message.error('获取广告趋势数据失败')
        this.trendData = []
      }
    },
    async fetchRankData() {
      try {
        const params = {
          period: this.timePeriod
        }
        
        if (this.customDateRange && this.customDateRange.length === 2) {
          params.start_date = this.customDateRange[0]
          params.end_date = this.customDateRange[1]
        }
        
        const [spaceRankRes, contentRankRes] = await Promise.all([
          getAdSpaceRank(params),
          getAdContentRank(params)
        ])

        this.spaceRankData = Array.isArray(spaceRankRes.data) ? spaceRankRes.data : []
        this.contentRankData = Array.isArray(contentRankRes.data) ? contentRankRes.data : []

        this.$nextTick(() => {
          this.renderRankCharts()
        })
      } catch (error) {
        console.error('获取广告排行数据失败:', error)
        this.$message.error('获取广告排行数据失败')
        this.spaceRankData = []
        this.contentRankData = []
      }
    },
    async fetchSpaceStats() {
      try {
        this.tableLoading = true
        const { data } = await getAdSpaceStats()
        this.spaceStats = Array.isArray(data) ? data : []
      } catch (error) {
        console.error('获取广告位统计失败:', error)
        this.$message.error('获取广告位统计失败')
        this.spaceStats = []
      } finally {
        this.tableLoading = false
      }
    },
    renderTrendCharts() {
      this.renderClickTrendChart()
      this.renderRevenueTrendChart()
    },
    renderClickTrendChart() {
      if (!this.$refs.clickTrendChart) {
        return
      }

      if (!this.clickTrendChart) {
        this.clickTrendChart = echarts.init(this.$refs.clickTrendChart)
      }

      if (!Array.isArray(this.trendData) || this.trendData.length === 0) {
        return
      }

      const dates = this.trendData.map(item => item.date || '')
      const clicks = this.trendData.map(item => item.clicks || 0)
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '点击次数',
            type: 'line',
            data: clicks,
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }
      
      this.clickTrendChart.setOption(option)
    },
    renderRevenueTrendChart() {
      if (!this.$refs.revenueTrendChart) {
        return
      }

      if (!this.revenueTrendChart) {
        this.revenueTrendChart = echarts.init(this.$refs.revenueTrendChart)
      }

      if (!Array.isArray(this.trendData) || this.trendData.length === 0) {
        return
      }

      const dates = this.trendData.map(item => item.date || '')
      const revenue = this.trendData.map(item => item.revenue || 0)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            return `${params[0].name}<br/>${params[0].seriesName}: ¥${params[0].value.toFixed(2)}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '广告收益',
            type: 'bar',
            data: revenue,
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }
      
      this.revenueTrendChart.setOption(option)
    },
    renderRankCharts() {
      this.renderSpaceRankChart()
      this.renderContentRankChart()
    },
    renderSpaceRankChart() {
      if (!this.$refs.spaceRankChart) {
        return
      }

      if (!this.spaceRankChart) {
        this.spaceRankChart = echarts.init(this.$refs.spaceRankChart)
      }

      if (!Array.isArray(this.spaceRankData) || this.spaceRankData.length === 0) {
        return
      }

      const spaceNames = this.spaceRankData.map(item => item.space_name || '')
      const clicks = this.spaceRankData.map(item => item.clicks || 0)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: spaceNames
        },
        series: [
          {
            name: '点击次数',
            type: 'bar',
            data: clicks,
            itemStyle: {
              color: '#E6A23C'
            }
          }
        ]
      }
      
      this.spaceRankChart.setOption(option)
    },
    renderContentRankChart() {
      if (!this.$refs.contentRankChart) {
        return
      }

      if (!this.contentRankChart) {
        this.contentRankChart = echarts.init(this.$refs.contentRankChart)
      }

      if (!Array.isArray(this.contentRankData) || this.contentRankData.length === 0) {
        return
      }

      const contentData = this.contentRankData.map(item => ({
        value: item.clicks || 0,
        name: item.title || item.content_title || '未知内容'
      }))

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: this.contentRankData.map(item => item.title || item.content_title || '未知内容')
        },
        series: [
          {
            name: '广告内容点击',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: contentData
          }
        ]
      }
      
      this.contentRankChart.setOption(option)
    },
    formatNumber(num) {
      return parseInt(num).toLocaleString()
    },
    formatMoney(num) {
      return parseFloat(num || 0).toFixed(2)
    },
    async toggleSpaceStatus(row, status) {
      try {
        // 设置按钮加载状态
        this.$set(row, 'loading', true)

        console.log('准备更新广告位状态:', {
          space_id: row.space_id,
          current_status: row.status,
          target_status: status
        })

        // 调用API更新广告位状态
        const response = await request({
          url: `/api/v1/admin/advertisement/spaces/${row.space_id}`,
          method: 'put',
          data: {
            status: status
          }
        })

        console.log('API响应数据:', response)

        if (response && response.status === 'success') {
          // 更新本地状态
          row.status = status
          this.$message.success(status === 1 ? '广告位已启用' : '广告位已禁用')
        } else {
          console.error('API响应格式异常:', response)
          this.$message.error(response?.message || '操作失败')
        }
      } catch (error) {
        console.error('切换广告位状态失败:', error)
        this.$message.error('操作失败，请稍后重试')
      } finally {
        // 清除按钮加载状态
        this.$set(row, 'loading', false)
      }
    }
  }
}
</script>

<style scoped>
.stats-card {
  margin-bottom: 20px;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
  display: flex;
  align-items: center;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.card-icon i {
  font-size: 30px;
  color: white;
}

.ad-icon {
  background-color: #409EFF;
}

.content-icon {
  background-color: #67C23A;
}

.click-icon {
  background-color: #E6A23C;
}

.revenue-icon {
  background-color: #F56C6C;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #909399;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin: 5px 0;
}

.card-footer {
  font-size: 12px;
  color: #909399;
}

.highlight {
  color: #F56C6C;
  font-weight: bold;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}
</style>
