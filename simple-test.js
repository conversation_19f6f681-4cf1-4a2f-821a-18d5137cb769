const axios = require('axios');

async function test() {
  try {
    console.log('测试后端连接...');
    const response = await axios.get('http://localhost:4000');
    console.log('✅ 后端连接成功');
    console.log('响应:', response.data);
  } catch (error) {
    console.log('❌ 后端连接失败:', error.message);
  }
  
  try {
    console.log('\n测试登录API...');
    const loginResponse = await axios.post('http://localhost:4000/api/v1/admin/auth/admin-login', {
      username: 'mrx0927',
      password: 'hh20250701'
    });
    console.log('✅ 登录成功');
    console.log('响应:', loginResponse.data);
  } catch (error) {
    console.log('❌ 登录失败:', error.message);
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('错误信息:', error.response.data);
    }
  }
}

test();
