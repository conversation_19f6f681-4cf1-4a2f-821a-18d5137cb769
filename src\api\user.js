import request from '@/utils/request'

// 本地模拟数据，用于开发测试
const useMock = false // 禁用模拟数据

// 用户登录
export function login (data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (data.username === 'admin' && data.password === 'admin123') {
          resolve({
            code: 200,
            data: {
              token: 'admin-token-mock'
            },
            message: '登录成功'
          })
        } else {
          resolve({
            code: 401,
            message: '用户名或密码错误'
          })
        }
      }, 300)
    })
  }

  console.log('发送登录请求:', data)
  
  return request({
    url: '/api/v1/admin/auth/admin-login',
    method: 'post',
    data
  })
}

// 获取用户信息
export function getInfo (token) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (token === 'admin-token-mock') {
          resolve({
            code: 200,
            data: {
              roles: ['admin'],
              name: '管理员',
              avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif'
            },
            message: '获取用户信息成功'
          })
        } else {
          resolve({
            code: 50008,
            message: '登录状态已过期，请重新登录'
          })
        }
      }, 300)
    })
  }

  console.log('获取用户信息，token:', token)
  
  if (!token) {
    console.error('Token不存在，无法获取用户信息')
    return Promise.reject(new Error('Token不存在'))
  }
  
  return request({
    url: '/api/v1/admin/auth/user-info',
    method: 'get'
  })
}

// 用户登出
export function logout () {
  if (useMock) {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          code: 200,
          data: {},
          message: '登出成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/auth/logout',
    method: 'post'
  })
}

// 搜索用户
export function searchUsers (params) {
  return request({
    url: '/api/v1/admin/user/search',
    method: 'get',
    params
  })
}
