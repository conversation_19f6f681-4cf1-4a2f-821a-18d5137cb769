(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0742c010"],{"0eb9":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{staticClass:"filter-container"},[e("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.queryParams}},[e("el-form-item",{attrs:{label:"广告标题"}},[e("el-input",{attrs:{placeholder:"广告标题",clearable:""},model:{value:t.queryParams.title,callback:function(e){t.$set(t.queryParams,"title",e)},expression:"queryParams.title"}})],1),e("el-form-item",{attrs:{label:"广告位"}},[e("el-select",{attrs:{placeholder:"广告位",clearable:""},model:{value:t.queryParams.spaceId,callback:function(e){t.$set(t.queryParams,"spaceId",e)},expression:"queryParams.spaceId"}},t._l(t.spaceOptions,(function(t){return e("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),e("el-form-item",{attrs:{label:"状态"}},[e("el-select",{attrs:{placeholder:"状态",clearable:""},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,"status",e)},expression:"queryParams.status"}},[e("el-option",{attrs:{label:"上线",value:1}}),e("el-option",{attrs:{label:"下线",value:0}})],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.handleQuery}},[t._v("查询")]),e("el-button",{on:{click:t.resetQuery}},[t._v("重置")]),e("el-button",{attrs:{type:"primary"},on:{click:t.handleCreate}},[t._v("添加广告")])],1)],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.adList,border:""}},[e("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),e("el-table-column",{attrs:{prop:"title",label:"广告标题",width:"200"}}),e("el-table-column",{attrs:{prop:"space_name",label:"广告位",width:"150"}}),e("el-table-column",{attrs:{label:"图片",width:"150"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-image",{staticStyle:{width:"100px",height:"60px"},attrs:{src:t.formatImageUrl(a.row.image),"preview-src-list":[t.formatImageUrl(a.row.image)],fit:"cover"}},[e("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[e("i",{staticClass:"el-icon-picture-outline"})])])]}}])}),e("el-table-column",{attrs:{prop:"url",label:"链接",width:"150"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-link",{attrs:{type:"primary",href:a.row.url,target:"_blank"}},[t._v(t._s(a.row.url))])]}}])}),e("el-table-column",{attrs:{label:"投放时间",width:"300"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.start_time)+" 至 "+t._s(a.row.end_time))])]}}])}),e("el-table-column",{attrs:{prop:"view_count",label:"曝光量",width:"100"}}),e("el-table-column",{attrs:{prop:"click_count",label:"点击量",width:"100"}}),e("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:1===a.row.status?"success":"info"}},[t._v(" "+t._s(1===a.row.status?"上线":"下线")+" ")])]}}])}),e("el-table-column",{attrs:{label:"操作",width:"280",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handleUpdate(a.row)}}},[t._v("编辑")]),e("el-button",{attrs:{size:"mini",type:1===a.row.status?"warning":"success"},on:{click:function(e){return t.handleChangeStatus(a.row)}}},[t._v(t._s(1===a.row.status?"下线":"上线"))]),e("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.handleDelete(a.row)}}},[t._v("删除")])]}}])})],1),e("div",{staticClass:"pagination-container"},[e("el-pagination",{attrs:{"current-page":t.queryParams.page,"page-sizes":[10,20,30,50],"page-size":t.queryParams.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)},s=[],n=(a("14d9"),a("e9f5"),a("f665"),a("ab43"),a("2b3f")),i={name:"AdContentList",data(){return{loading:!1,total:0,queryParams:{page:1,limit:10,title:void 0,spaceId:void 0,status:void 0},adList:[],spaceOptions:[]}},created(){this.getAdSpaces(),this.getList()},methods:{formatImageUrl:n["e"],getAdSpaces(){Object(n["h"])({limit:100}).then(t=>{console.log("广告位选项响应:",t),"success"===t.status&&t.data&&t.data.list?this.spaceOptions=t.data.list:this.$message.error(t.message||"获取广告位选项失败")}).catch(t=>{console.error("获取广告位选项错误:",t),this.$message.error("获取广告位选项失败")})},getList(){this.loading=!0,Object(n["g"])(this.queryParams).then(t=>{console.log("广告内容列表响应:",t),"success"===t.status?(this.adList=(t.data.list||[]).map(t=>{const e=this.spaceOptions.find(e=>e.id===t.space_id);return{...t,space_name:t.space_name||(e?e.name:"未知广告位")}}),this.total=t.data.total||0):this.$message.error(t.message||"获取广告列表失败")}).catch(t=>{console.error("获取广告列表错误:",t),this.$message.error("获取广告列表失败")}).finally(()=>{this.loading=!1})},handleQuery(){this.queryParams.page=1,this.getList()},resetQuery(){this.queryParams={page:1,limit:10,title:void 0,spaceId:void 0,status:void 0},this.getList()},handleSizeChange(t){this.queryParams.limit=t,this.getList()},handleCurrentChange(t){this.queryParams.page=t,this.getList()},handleCreate(){this.$router.push("/ad/content/create")},handleUpdate(t){this.$router.push("/ad/content/edit/"+t.id)},handleChangeStatus(t){const e=1===t.status?0:1,a=1===e?"上线":"下线";this.$confirm(`确认要${a}该广告吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(n["j"])(t.id,{status:e}).then(t=>{console.log(a+"广告响应:",t),"success"===t.status?(this.$message.success(a+"成功"),this.getList()):this.$message.error(t.message||a+"失败")}).catch(t=>{console.error(a+"广告错误:",t),this.$message.error(a+"失败")})}).catch(()=>{})},handleDelete(t){this.$confirm("确认要删除该广告吗？删除后不可恢复！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(n["c"])(t.id).then(t=>{console.log("删除广告响应:",t),"success"===t.status?(this.$message.success("删除成功"),this.getList()):this.$message.error(t.message||"删除失败")}).catch(t=>{console.error("删除广告错误:",t),this.$message.error("删除失败")})}).catch(()=>{})}}},o=i,l=(a("8dbc"),a("2877")),c=Object(l["a"])(o,r,s,!1,null,"7ed5664f",null);e["default"]=c.exports},"2b3f":function(t,e,a){"use strict";a.d(e,"e",(function(){return s})),a.d(e,"h",(function(){return n})),a.d(e,"b",(function(){return i})),a.d(e,"k",(function(){return o})),a.d(e,"d",(function(){return l})),a.d(e,"l",(function(){return c})),a.d(e,"g",(function(){return u})),a.d(e,"a",(function(){return d})),a.d(e,"i",(function(){return m})),a.d(e,"c",(function(){return p})),a.d(e,"j",(function(){return h})),a.d(e,"f",(function(){return f}));var r=a("b775");function s(t){return t?t.startsWith("http://")||t.startsWith("https://")||t.startsWith("/")?t:"/uploads/"+t:"/uploads/default-ad.jpg"}function n(t){return Object(r["a"])({url:"/api/v1/admin/advertisement/spaces",method:"get",params:t})}function i(t){return Object(r["a"])({url:"/api/v1/admin/advertisement/spaces",method:"post",data:t})}function o(t,e){return Object(r["a"])({url:"/api/v1/admin/advertisement/spaces/"+t,method:"put",data:e})}function l(t){return Object(r["a"])({url:"/api/v1/admin/advertisement/spaces/"+t,method:"delete"})}function c(t,e){return Object(r["a"])({url:"/api/v1/admin/advertisement/spaces/"+t,method:"put",data:{status:e}})}function u(t){return Object(r["a"])({url:"/api/v1/admin/advertisement/contents",method:"get",params:t})}function d(t){return Object(r["a"])({url:"/api/v1/admin/advertisement/contents",method:"post",data:t})}function m(t,e){return Object(r["a"])({url:"/api/v1/admin/advertisement/contents/"+t,method:"put",data:e})}function p(t){return Object(r["a"])({url:"/api/v1/admin/advertisement/contents/"+t,method:"delete"})}function h(t,e){const a=e&&void 0!==e.status?e.status:0;return Object(r["a"])({url:"/api/v1/admin/advertisement/contents/"+t,method:"put",data:{status:a}})}function f(t){return Object(r["a"])({url:"/api/v1/admin/advertisement/contents/"+t,method:"get"})}},"8dbc":function(t,e,a){"use strict";a("a954")},a954:function(t,e,a){},ab43:function(t,e,a){"use strict";var r=a("23e7"),s=a("c65b"),n=a("59ed"),i=a("825a"),o=a("46c4"),l=a("c5cc"),c=a("9bdd"),u=a("2a62"),d=a("2baa"),m=a("f99f"),p=a("c430"),h=!p&&!d("map",(function(){})),f=!p&&!h&&m("map",TypeError),g=p||h||f,b=l((function(){var t=this.iterator,e=i(s(this.next,t)),a=this.done=!!e.done;if(!a)return c(t,this.mapper,[e.value,this.counter++],!0)}));r({target:"Iterator",proto:!0,real:!0,forced:g},{map:function(t){i(this);try{n(t)}catch(e){u(this,"throw",e)}return f?s(f,this,t):new b(o(this),{mapper:t})}})},f665:function(t,e,a){"use strict";var r=a("23e7"),s=a("c65b"),n=a("2266"),i=a("59ed"),o=a("825a"),l=a("46c4"),c=a("2a62"),u=a("f99f"),d=u("find",TypeError);r({target:"Iterator",proto:!0,real:!0,forced:d},{find:function(t){o(this);try{i(t)}catch(r){c(this,"throw",r)}if(d)return s(d,this,t);var e=l(this),a=0;return n(e,(function(e,r){if(t(e,a++))return r(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})}}]);