<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? '编辑广告' : '创建广告' }}</span>
      </div>
      <el-form ref="adForm" :model="adForm" :rules="rules" label-width="100px">
        <el-form-item label="广告标题" prop="title">
          <el-input v-model="adForm.title" placeholder="请输入广告标题" />
        </el-form-item>

        <el-form-item label="广告位" prop="space_id">
          <el-select v-model="adForm.space_id" placeholder="请选择广告位">
            <el-option
              v-for="item in spaceOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="广告图片" prop="image">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :http-request="uploadImage"
            :before-upload="beforeImageUpload">
            <img v-if="adForm.image || imagePreview" :src="adForm.image ? formatImageUrl(adForm.image) : imagePreview" class="uploaded-image">
            <i v-else class="el-icon-plus image-uploader-icon"></i>
          </el-upload>
          <div class="tip">图片格式: JPG/PNG/GIF, 大小不超过2MB</div>
        </el-form-item>

        <el-form-item label="链接地址" prop="url">
          <el-input v-model="adForm.url" placeholder="请输入链接地址" />
        </el-form-item>

        <el-form-item label="投放时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="adForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="上线"
            inactive-text="下线">
          </el-switch>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="goBack">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { createAdContent, updateAdContent, getAdContentDetail, getAdSpaceList, formatImageUrl } from '@/api/advertisement'
import { uploadFile } from '@/api/upload'

export default {
  name: 'AdContentForm',
  data () {
    return {
      isEdit: false,
      adId: undefined,
      dateRange: [],
      adForm: {
        title: '',
        space_id: '',
        image: '',
        url: '',
        start_time: '',
        end_time: '',
        status: 1,
        view_count: 0,
        click_count: 0
      },
      rules: {
        title: [
          { required: true, message: '请输入广告标题', trigger: 'blur' },
          { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        space_id: [
          { required: true, message: '请选择广告位', trigger: 'change' }
        ],
        image: [
          { required: true, message: '请上传广告图片', trigger: 'change' }
        ],
        url: [
          { required: true, message: '请输入链接地址', trigger: 'blur' },
          { pattern: /^(https?:\/\/)/, message: '请输入有效的URL地址', trigger: 'blur' }
        ]
      },
      spaceOptions: [],
      imagePreview: '' // 新增用于预览的属性
    }
  },
  created () {
    // 加载广告位选项
    this.getSpaceOptions()

    // 判断是编辑还是新增
    const id = this.$route.params && this.$route.params.id
    if (id) {
      this.isEdit = true
      this.adId = parseInt(id)
      this.getDetail(this.adId)
    }
  },
  methods: {
    formatImageUrl,
    getDetail (id) {
      getAdContentDetail(id).then(response => {
        console.log('广告内容详情响应:', response)
        if (response.status === 'success') {
          this.adForm = response.data
          // 确保is_recommend字段存在
          if (this.adForm.is_recommend === undefined) {
            this.adForm.is_recommend = 0
          }
          // 设置日期范围
          this.dateRange = [this.adForm.start_time, this.adForm.end_time]
        } else {
          this.$message.error(response.message || '获取广告详情失败')
          this.goBack()
        }
      }).catch(error => {
        console.error('获取广告详情错误:', error)
        this.$message.error('获取广告详情失败')
        this.goBack()
      })
    },
    getSpaceOptions () {
      getAdSpaceList({ page: 1, limit: 100 }).then(response => {
        console.log('广告位列表响应:', response)
        if (response.status === 'success') {
          this.spaceOptions = response.data.list || []
        } else {
          this.$message.error(response.message || '获取广告位列表失败')
        }
      }).catch(error => {
        console.error('获取广告位列表错误:', error)
        this.$message.error('获取广告位列表失败')
      })
    },
    beforeImageUpload (file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过2MB!')
        return false
      }
      
      // 创建图片预览
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => {
        // 仅用于预览，实际上传由uploadImage方法处理
        this.imagePreview = reader.result
      }
      
      return true
    },
    uploadImage (options) {
      const { file } = options
      // 使用上传API将文件上传到服务器
      uploadFile(file).then(response => {
        console.log('广告图片上传响应:', response)
        if (response.code === 200 && response.data && response.data.url) {
          // 获取上传后的URL并处理成相对路径
          const url = response.data.url
          // 从URL中提取相对路径部分
          this.adForm.image = url.includes('/uploads/') ? url.substring(url.indexOf('/uploads/')) : url
          console.log('广告图片上传成功，URL:', this.adForm.image)
          this.$message.success('图片上传成功')
        } else {
          this.$message.error('图片上传失败: ' + (response.message || '未知错误'))
        }
      }).catch(error => {
        console.error('图片上传失败:', error)
        this.$message.error('图片上传失败: ' + (error.message || '未知错误'))
      })
    },
    submitForm () {
      this.$refs.adForm.validate(valid => {
        if (valid) {
          // 处理日期
          if (this.dateRange && this.dateRange.length === 2) {
            // 确保日期格式正确，转换为MySQL可接受的格式 YYYY-MM-DD HH:MM:SS
            const formatDate = (date) => {
              if (typeof date === 'string') {
                // 如果已经是字符串格式，确保格式正确
                if (date.includes('T')) {
                  return date.replace('T', ' ').replace(/\.\d+Z$/, '')
                }
                return date
              } else {
                // 如果是Date对象，格式化为字符串
                const d = new Date(date)
                return d.toISOString().slice(0, 19).replace('T', ' ')
              }
            }
            
            this.adForm.start_time = formatDate(this.dateRange[0])
            this.adForm.end_time = formatDate(this.dateRange[1])
          }

          const submitData = { ...this.adForm }
          console.log('提交的数据:', submitData)

          if (this.isEdit) {
            // 更新
            updateAdContent(this.adId, submitData).then(response => {
              console.log('更新广告内容响应:', response)
              if (response.status === 'success') {
                this.$message.success('更新成功')
                this.goBack()
              } else {
                this.$message.error(response.message || '更新失败')
              }
            }).catch(error => {
              console.error('更新广告内容错误:', error)
              this.$message.error('更新失败')
            })
          } else {
            // 创建
            createAdContent(submitData).then(response => {
              console.log('创建广告内容响应:', response)
              if (response.status === 'success') {
                this.$message.success('创建成功')
                this.goBack()
              } else {
                this.$message.error(response.message || '创建失败')
              }
            }).catch(error => {
              console.error('创建广告内容错误:', error)
              this.$message.error('创建失败')
            })
          }
        }
      })
    },
    goBack () {
      this.$router.push('/ad/content')
    }
  }
}
</script>

<style lang="scss" scoped>
.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 300px;
  height: 200px;

  &:hover {
    border-color: #409EFF;
  }

  .uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 300px;
  height: 200px;
  line-height: 200px;
  text-align: center;
}
.tip {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}
</style>
