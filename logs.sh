#!/bin/bash

# WiFi共享管理后台日志查看脚本

echo "=== WiFi共享管理后台日志查看工具 ==="
echo ""

# 检查日志目录是否存在
if [ ! -d "logs" ]; then
    echo "日志目录不存在，请先启动应用"
    exit 1
fi

# 显示可用的日志文件
echo "可用的日志文件:"
ls -la logs/

echo ""
echo "选择要查看的日志:"
echo "1. 综合日志 (wifi-share-admin.log)"
echo "2. 信息日志 (info.log)"
echo "3. 警告日志 (warn.log)"
echo "4. 错误日志 (error.log)"
echo "5. PM2输出日志 (wifi-share-admin-out.log)"
echo "6. PM2错误日志 (wifi-share-admin-error.log)"
echo "7. 实时查看综合日志"
echo "8. 实时查看PM2日志"

read -p "请输入选项 (1-8): " choice

case $choice in
    1)
        echo "=== 综合日志 ==="
        tail -n 50 logs/wifi-share-admin.log
        ;;
    2)
        echo "=== 信息日志 ==="
        tail -n 50 logs/info.log
        ;;
    3)
        echo "=== 警告日志 ==="
        tail -n 50 logs/warn.log
        ;;
    4)
        echo "=== 错误日志 ==="
        tail -n 50 logs/error.log
        ;;
    5)
        echo "=== PM2输出日志 ==="
        tail -n 50 logs/wifi-share-admin-out.log
        ;;
    6)
        echo "=== PM2错误日志 ==="
        tail -n 50 logs/wifi-share-admin-error.log
        ;;
    7)
        echo "=== 实时查看综合日志 (按Ctrl+C退出) ==="
        tail -f logs/wifi-share-admin.log
        ;;
    8)
        echo "=== 实时查看PM2日志 (按Ctrl+C退出) ==="
        pm2 logs wifi-share-admin
        ;;
    *)
        echo "无效选项"
        exit 1
        ;;
esac
