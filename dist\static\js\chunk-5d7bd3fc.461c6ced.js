(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5d7bd3fc"],{"13d5":function(t,e,i){"use strict";var a=i("23e7"),n=i("d58f").left,r=i("a640"),s=i("1212"),o=i("9adc"),u=!o&&s>79&&s<83,l=u||!r("reduce");a({target:"Array",proto:!0,forced:l},{reduce:function(t){var e=arguments.length;return n(this,t,e,e>1?arguments[1]:void 0)}})},"21e5":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{staticClass:"filter-container"},[e("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"WiFi名称/标题",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.keyword,callback:function(e){t.$set(t.listQuery,"keyword",e)},expression:"listQuery.keyword"}}),e("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"状态",clearable:""},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},t._l(t.statusOptions,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),e("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleFilter}},[t._v("搜索")]),e("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-plus"},on:{click:t.handleCreate}},[t._v("新增")])],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{data:t.list,"element-loading-text":"正在加载...",border:"",fit:"","highlight-current-row":""}},[e("el-table-column",{attrs:{label:"ID",prop:"id",align:"center",width:"80"}}),e("el-table-column",{attrs:{label:"标题",prop:"title",align:"center","min-width":"120"}}),e("el-table-column",{attrs:{label:"WiFi名称",prop:"name",align:"center","min-width":"120"}}),e("el-table-column",{attrs:{label:"商户名称",prop:"merchant_name",align:"center","min-width":"120"}}),e("el-table-column",{attrs:{label:"使用次数",prop:"use_count",align:"center",width:"100"}}),e("el-table-column",{attrs:{label:"状态",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function({row:i}){return[e("el-tag",{attrs:{type:1===i.status?"success":"info"}},[t._v(" "+t._s(1===i.status?"启用":"禁用")+" ")])]}}])}),e("el-table-column",{attrs:{label:"创建时间",align:"center",width:"160"},scopedSlots:t._u([{key:"default",fn:function({row:i}){return[e("span",[t._v(t._s(i.created_at))])]}}])}),e("el-table-column",{attrs:{label:"操作",align:"center",width:"250","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function({row:i}){return[e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.handleView(i)}}},[t._v("查看")]),e("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(e){return t.handleUpdate(i)}}},[t._v("编辑")]),1===i.status?e("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(e){return t.handleStatusChange(i,0)}}},[t._v("禁用")]):e("el-button",{attrs:{size:"mini",type:"info"},on:{click:function(e){return t.handleStatusChange(i,1)}}},[t._v("启用")]),e("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.handleDelete(i)}}},[t._v("删除")])]}}])})],1),e("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}})],1)},n=[],r=(i("14d9"),i("d251")),s=i("333d"),o={name:"WiFiList",components:{Pagination:s["a"]},data(){return{list:null,total:0,listLoading:!0,listQuery:{page:1,limit:10,keyword:void 0,status:void 0},statusOptions:[{label:"启用",value:1},{label:"禁用",value:0}]}},created(){this.getList()},methods:{getList(){this.listLoading=!0,Object(r["d"])(this.listQuery).then(t=>{this.list=t.data.list||[],this.total=t.data.pagination&&t.data.pagination.total||t.data.total||0,this.listLoading=!1}).catch(t=>{console.error("获取WiFi列表失败:",t),this.list=[],this.total=0,this.listLoading=!1,this.$message.error("获取WiFi列表失败")})},handleFilter(){this.listQuery.page=1,this.getList()},handleCreate(){this.$router.push("/wifi/create")},handleUpdate(t){this.$router.push("/wifi/edit/"+t.id)},handleView(t){this.$router.push("/wifi/detail/"+t.id)},handleStatusChange(t,e){Object(r["g"])(t.id,{status:e}).then(i=>{this.$message.success("状态更新成功"),t.status=e}).catch(()=>{this.$message.error("状态更新失败")})},handleDelete(t){this.$confirm("确认要删除这个WiFi码吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(r["b"])(t.id).then(t=>{this.$message.success("删除成功"),this.getList()}).catch(()=>{this.$message.error("删除失败")})}).catch(()=>{})}}},u=o,l=i("2877"),c=Object(l["a"])(u,a,n,!1,null,null,null);e["default"]=c.exports},"330e":function(t,e,i){"use strict";i("34b0")},"333d":function(t,e,i){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[e("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},n=[],r={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get(){return this.page},set(t){this.$emit("update:page",t)}},pageSize:{get(){return this.limit},set(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange(t){this.$emit("pagination",{page:this.currentPage,limit:t})},handleCurrentChange(t){this.$emit("pagination",{page:t,limit:this.pageSize})}}},s=r,o=(i("330e"),i("2877")),u=Object(o["a"])(s,a,n,!1,null,"11252b03",null);e["a"]=u.exports},"34b0":function(t,e,i){},8558:function(t,e,i){"use strict";var a=i("cfe9"),n=i("b5db"),r=i("c6b6"),s=function(t){return n.slice(0,t.length)===t};t.exports=function(){return s("Bun/")?"BUN":s("Cloudflare-Workers")?"CLOUDFLARE":s("Deno/")?"DENO":s("Node.js/")?"NODE":a.Bun&&"string"==typeof Bun.version?"BUN":a.Deno&&"object"==typeof Deno.version?"DENO":"process"===r(a.process)?"NODE":a.window&&a.document?"BROWSER":"REST"}()},9485:function(t,e,i){"use strict";var a=i("23e7"),n=i("2266"),r=i("59ed"),s=i("825a"),o=i("46c4"),u=i("2a62"),l=i("f99f"),c=i("2ba4"),d=i("d039"),p=TypeError,f=d((function(){[].keys().reduce((function(){}),void 0)})),h=!f&&l("reduce",p);a({target:"Iterator",proto:!0,real:!0,forced:f||h},{reduce:function(t){s(this);try{r(t)}catch(d){u(this,"throw",d)}var e=arguments.length<2,i=e?void 0:arguments[1];if(h)return c(h,this,e?[t]:[t,i]);var a=o(this),l=0;if(n(a,(function(a){e?(e=!1,i=a):i=t(i,a,l),l++}),{IS_RECORD:!0}),e)throw new p("Reduce of empty iterator with no initial value");return i}})},"9adc":function(t,e,i){"use strict";var a=i("8558");t.exports="NODE"===a},a640:function(t,e,i){"use strict";var a=i("d039");t.exports=function(t,e){var i=[][t];return!!i&&a((function(){i.call(null,e||function(){return 1},1)}))}},ab43:function(t,e,i){"use strict";var a=i("23e7"),n=i("c65b"),r=i("59ed"),s=i("825a"),o=i("46c4"),u=i("c5cc"),l=i("9bdd"),c=i("2a62"),d=i("2baa"),p=i("f99f"),f=i("c430"),h=!f&&!d("map",(function(){})),g=!f&&!h&&p("map",TypeError),m=f||h||g,w=u((function(){var t=this.iterator,e=s(n(this.next,t)),i=this.done=!!e.done;if(!i)return l(t,this.mapper,[e.value,this.counter++],!0)}));a({target:"Iterator",proto:!0,real:!0,forced:m},{map:function(t){s(this);try{r(t)}catch(e){c(this,"throw",e)}return g?n(g,this,t):new w(o(this),{mapper:t})}})},d251:function(t,e,i){"use strict";i.d(e,"d",(function(){return c})),i.d(e,"c",(function(){return d})),i.d(e,"a",(function(){return p})),i.d(e,"f",(function(){return f})),i.d(e,"b",(function(){return h})),i.d(e,"g",(function(){return g})),i.d(e,"e",(function(){return m}));i("14d9"),i("13d5"),i("e9f5"),i("910d"),i("f665"),i("ab43"),i("9485");var a=i("b775");const n=!1,r="wifi_admin_wifi_list",s=[{id:1,title:"WiFi测试1",name:"Test WiFi",password:"12345678",merchant_name:"测试商家1",qrcode:"",use_count:123,user_id:1,status:1,created_at:"2023-06-01 12:30:45"},{id:2,title:"WiFi测试2",name:"Office WiFi",password:"87654321",merchant_name:"测试商家2",qrcode:"",use_count:456,user_id:2,status:1,created_at:"2023-06-02 10:20:30"}];function o(){try{const t=localStorage.getItem(r);return t?JSON.parse(t):s}catch(t){return console.warn("读取WiFi数据失败，使用默认数据:",t),s}}function u(t){try{localStorage.setItem(r,JSON.stringify(t))}catch(e){console.error("保存WiFi数据失败:",e)}}function l(){const t=new Date,e=t.getFullYear(),i=String(t.getMonth()+1).padStart(2,"0"),a=String(t.getDate()).padStart(2,"0"),n=String(t.getHours()).padStart(2,"0"),r=String(t.getMinutes()).padStart(2,"0"),s=String(t.getSeconds()).padStart(2,"0");return`${e}-${i}-${a} ${n}:${r}:${s}`}function c(t){return n?new Promise(e=>{setTimeout(()=>{const i=o(),a=parseInt(t.page)||1,n=parseInt(t.limit)||10,r=(a-1)*n,s=r+n;let u=i;if(t.keyword){const e=t.keyword.toLowerCase();u=i.filter(t=>t.title.toLowerCase().includes(e)||t.name.toLowerCase().includes(e)||t.merchant_name.toLowerCase().includes(e))}void 0!==t.status&&""!==t.status&&(u=u.filter(e=>e.status===parseInt(t.status)));const l=u.slice(r,s);e({code:200,data:{list:l,total:u.length},message:"获取WiFi列表成功"})},300)}):Object(a["a"])({url:"/api/v1/admin/wifi/list",method:"get",params:t})}function d(t){return n?new Promise(e=>{setTimeout(()=>{const i=o(),a=i.find(e=>e.id===parseInt(t));e(a?{code:200,data:a,message:"获取WiFi详情成功"}:{code:404,message:"未找到该WiFi码"})},300)}):(console.log("请求WiFi详情, ID:",t),Object(a["a"])({url:"/api/v1/admin/wifi/detail/"+t,method:"get"}))}function p(t){return n?new Promise(e=>{setTimeout(()=>{const i=o(),a=i.length>0?Math.max(...i.map(t=>t.id))+1:1,n={id:a,title:t.title,name:t.name,password:t.password,merchant_name:t.merchant_name,qrcode:"",use_count:0,user_id:1,status:t.status,created_at:l()};i.push(n),u(i),console.log("模拟数据 - 创建WiFi码成功:",n),console.log("当前WiFi列表:",i),e({code:200,data:{id:a},message:"创建WiFi码成功"})},300)}):Object(a["a"])({url:"/api/v1/admin/wifi/create",method:"post",data:t})}function f(t,e){return n?new Promise(i=>{setTimeout(()=>{const a=o(),n=a.findIndex(e=>e.id===parseInt(t));-1!==n?(a[n]={...a[n],...e},u(a),console.log("模拟数据 - 更新WiFi码成功:",a[n]),i({code:200,data:{},message:"更新WiFi码成功"})):i({code:404,message:"未找到该WiFi码"})},300)}):Object(a["a"])({url:"/api/v1/admin/wifi/update/"+t,method:"put",data:e})}function h(t){return n?new Promise(e=>{setTimeout(()=>{const i=o(),a=i.findIndex(e=>e.id===parseInt(t));-1!==a?(i.splice(a,1),u(i),console.log("模拟数据 - 删除WiFi码成功, ID:",t),console.log("当前WiFi列表:",i),e({code:200,data:{},message:"删除WiFi码成功"})):e({code:404,message:"未找到该WiFi码"})},300)}):Object(a["a"])({url:"/api/v1/admin/wifi/"+t,method:"delete"})}function g(t,e){return n?new Promise(i=>{setTimeout(()=>{const a=o(),n=a.findIndex(e=>e.id===parseInt(t));-1!==n?(a[n].status=e.status,u(a),console.log("模拟数据 - 更新WiFi码状态成功:",a[n]),i({code:200,data:{},message:"更新WiFi码状态成功"})):i({code:404,message:"未找到该WiFi码"})},300)}):Object(a["a"])({url:"/api/v1/admin/wifi/status/"+t,method:"put",data:e})}function m(t){return n?new Promise(e=>{setTimeout(()=>{const i=o(),a=i.length,n=i.filter(t=>1===t.status).length,r=a-n,s=i.reduce((t,e)=>t+e.use_count,0),u=[],l=new Date;let c=7;t&&t.time_range&&("month"===t.time_range?c=30:"year"===t.time_range&&(c=365));for(let t=c-1;t>=0;t--){const e=new Date(l);e.setDate(e.getDate()-t);const i=`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`;u.push({date:i,count:Math.floor(100*Math.random())+10})}const d=[...i].sort((t,e)=>e.use_count-t.use_count).slice(0,5);e({code:200,data:{stats:{total:a,active:n,inactive:r,total_use_count:s},trend_data:u,top_wifi_list:d},message:"获取WiFi统计数据成功"})},300)}):Object(a["a"])({url:"/api/v1/admin/wifi/stats",method:"get",params:t})}},d58f:function(t,e,i){"use strict";var a=i("59ed"),n=i("7b0b"),r=i("44ad"),s=i("07fa"),o=TypeError,u="Reduce of empty array with no initial value",l=function(t){return function(e,i,l,c){var d=n(e),p=r(d),f=s(d);if(a(i),0===f&&l<2)throw new o(u);var h=t?f-1:0,g=t?-1:1;if(l<2)while(1){if(h in p){c=p[h],h+=g;break}if(h+=g,t?h<0:f<=h)throw new o(u)}for(;t?h>=0:f>h;h+=g)h in p&&(c=i(c,p[h],h,d));return c}};t.exports={left:l(!1),right:l(!0)}},f665:function(t,e,i){"use strict";var a=i("23e7"),n=i("c65b"),r=i("2266"),s=i("59ed"),o=i("825a"),u=i("46c4"),l=i("2a62"),c=i("f99f"),d=c("find",TypeError);a({target:"Iterator",proto:!0,real:!0,forced:d},{find:function(t){o(this);try{s(t)}catch(a){l(this,"throw",a)}if(d)return n(d,this,t);var e=u(this),i=0;return r(e,(function(e,a){if(t(e,i++))return a(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})}}]);