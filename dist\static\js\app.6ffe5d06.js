(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["app"],{0:function(e,t,n){e.exports=n("56d7")},"020e":function(e,t,n){},"186b":function(e,t,n){},"253b":function(e,t,n){},"3b62":function(e,t,n){},"3bab":function(e,t,n){},4360:function(e,t,n){"use strict";var a=n("2b0e"),i=n("2f62"),o=(n("d9e2"),n("c24f")),r=n("5f87"),s=n("a18c");const c={token:Object(r["a"])(),name:"",avatar:"",roles:[]},l={SET_TOKEN:(e,t)=>{e.token=t},SET_NAME:(e,t)=>{e.name=t},SET_AVATAR:(e,t)=>{e.avatar=t},SET_ROLES:(e,t)=>{e.roles=t}},d={login({commit:e},t){const{username:n,password:a}=t;return new Promise((t,i)=>{Object(o["b"])({username:n.trim(),password:a}).then(n=>{const{data:a}=n;if(console.log("登录响应:",n),console.log("登录数据:",a),!a||!a.token)return console.error("登录响应中没有token"),i(new Error("登录响应中没有token"));e("SET_TOKEN",a.token),Object(r["c"])(a.token),t()}).catch(e=>{console.error("登录失败:",e),i(e)})})},getInfo({commit:e,state:t}){return new Promise((n,a)=>{Object(o["a"])(t.token).then(t=>{const{data:i}=t;console.log("获取用户信息响应:",t),console.log("用户信息数据:",i),i||a(new Error("验证失败，请重新登录。"));const{roles:o,name:r,avatar:s}=i;(!o||o.length<=0)&&a(new Error("getInfo: roles必须是非空数组!")),e("SET_ROLES",o),e("SET_NAME",r),e("SET_AVATAR",s),n(i)}).catch(e=>{console.error("获取用户信息失败:",e),a(e)})})},logout({commit:e,state:t}){return new Promise((n,a)=>{Object(o["c"])(t.token).then(()=>{e("SET_TOKEN",""),e("SET_ROLES",[]),Object(r["b"])(),Object(s["b"])(),n()}).catch(e=>{a(e)})})},resetToken({commit:e}){return new Promise(t=>{e("SET_TOKEN",""),e("SET_ROLES",[]),Object(r["b"])(),t()})}};var u={namespaced:!0,state:c,mutations:l,actions:d},m=n("852e"),h=n.n(m);const p={sidebar:{opened:!h.a.get("sidebarStatus")||!!+h.a.get("sidebarStatus"),withoutAnimation:!1},device:"desktop"},f={TOGGLE_SIDEBAR:e=>{e.sidebar.opened=!e.sidebar.opened,e.sidebar.withoutAnimation=!1,e.sidebar.opened?h.a.set("sidebarStatus",1):h.a.set("sidebarStatus",0)},CLOSE_SIDEBAR:(e,t)=>{h.a.set("sidebarStatus",0),e.sidebar.opened=!1,e.sidebar.withoutAnimation=t},TOGGLE_DEVICE:(e,t)=>{e.device=t}},b={toggleSideBar({commit:e}){e("TOGGLE_SIDEBAR")},closeSideBar({commit:e},{withoutAnimation:t}){e("CLOSE_SIDEBAR",t)},toggleDevice({commit:e},t){e("TOGGLE_DEVICE",t)}};var g={namespaced:!0,state:p,mutations:f,actions:b};const v={sidebar:e=>e.app.sidebar,token:e=>e.user.token,avatar:e=>e.user.avatar,name:e=>e.user.name,roles:e=>e.user.roles};var k=v;a["default"].use(i["a"]);const w=new i["a"].Store({modules:{user:u,app:g},getters:k});t["a"]=w},"56d7":function(e,t,n){"use strict";n.r(t);var a={};n.r(a),n.d(a,"parseTime",(function(){return y})),n.d(a,"timeAgo",(function(){return C})),n.d(a,"formatFileSize",(function(){return S})),n.d(a,"formatNumber",(function(){return O})),n.d(a,"formatMoney",(function(){return _})),n.d(a,"truncate",(function(){return E})),n.d(a,"formatStatus",(function(){return T})),n.d(a,"formatUserType",(function(){return x}));n("e9f5"),n("910d"),n("7d54");var i=n("2b0e"),o=function(){var e=this,t=e._self._c;return t("div",{attrs:{id:"app"}},[t("router-view")],1)},r=[],s={name:"App"},c=s,l=(n("e4b1"),n("2877")),d=Object(l["a"])(c,o,r,!1,null,null,null),u=d.exports,m=n("a18c"),h=n("4360"),p=n("5c96"),f=n.n(p),b=(n("0fae"),n("f5df1"),n("6861"),n("323e")),g=n.n(b),v=(n("a5d8"),n("5f87"));g.a.configure({showSpinner:!1});const k=["/login"];m["a"].beforeEach(async(e,t,n)=>{g.a.start(),console.log("路由守卫: 从",t.path,"到",e.path),document.title=(e.meta.title||"")+" - WIFI共享商业系统管理后台";const a=Object(v["a"])();if(console.log("是否有token:",!!a,"token值:",a),a)if("/login"===e.path)console.log("已登录，重定向到首页"),n({path:"/"}),g.a.done();else{const t=h["a"].getters.roles&&h["a"].getters.roles.length>0;if(console.log("是否有角色信息:",t,"角色:",h["a"].getters.roles),t)n();else try{console.log("开始获取用户信息..."),await h["a"].dispatch("user/getInfo"),console.log("获取用户信息成功，继续导航"),n()}catch(i){console.error("获取用户信息失败:",i),await h["a"].dispatch("user/resetToken"),p["Message"].error(i.message||"Has Error"),n("/login?redirect="+e.path),g.a.done()}}else console.log("没有token，检查是否在白名单中"),-1!==k.indexOf(e.path)?n():(console.log("重定向到登录页"),n("/login?redirect="+e.path),g.a.done())}),m["a"].afterEach(()=>{g.a.done()});var w=n("87b4");function y(e,t){if(0===arguments.length||!e)return null;const n=t||"{y}-{m}-{d} {h}:{i}:{s}";let a;"object"===typeof e?a=e:("string"===typeof e&&(e=/^[0-9]+$/.test(e)?parseInt(e):e.replace(new RegExp(/-/gm),"/")),"number"===typeof e&&10===e.toString().length&&(e*=1e3),a=new Date(e));const i={y:a.getFullYear(),m:a.getMonth()+1,d:a.getDate(),h:a.getHours(),i:a.getMinutes(),s:a.getSeconds(),a:a.getDay()},o=n.replace(/{([ymdhisa])+}/g,(e,t)=>{const n=i[t];return"a"===t?["日","一","二","三","四","五","六"][n]:n.toString().padStart(2,"0")});return o}function C(e){const t=Date.now()/1e3-Number(new Date(e))/1e3;return t<3600?M(~~(t/60)," minute"):t<86400?M(~~(t/3600)," hour"):M(~~(t/86400)," day")}function M(e,t){return 1===e?e+t:e+t+"s"}function S(e){if(0===e)return"0 B";const t=1024,n=["B","KB","MB","GB","TB"],a=Math.floor(Math.log(e)/Math.log(t));return(e/Math.pow(t,a)).toFixed(2)+" "+n[a]}function O(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function _(e,t=2){return isNaN(e)?"0.00":Number(e).toFixed(t)}function E(e,t=50){return e?e.length<=t?e:e.substring(0,t)+"...":""}function T(e){const t={0:"待审核",1:"已通过",2:"已拒绝",pending:"待审核",approved:"已通过",rejected:"已拒绝",active:"激活",inactive:"未激活",enabled:"启用",disabled:"禁用"};return t[e]||e}function x(e){const t={0:"普通用户",1:"团队长",2:"管理员",user:"普通用户",leader:"团队长",admin:"管理员"};return t[e]||e}i["default"].use(f.a,{size:"medium"}),i["default"].use(w["a"]),Object.keys(a).forEach(e=>{i["default"].filter(e,a[e])}),i["default"].config.productionTip=!1,new i["default"]({router:m["a"],store:h["a"],render:e=>e(u)}).$mount("#app")},5758:function(e,t,n){},"5f87":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"c",(function(){return s})),n.d(t,"b",(function(){return c}));var a=n("852e"),i=n.n(a);const o="Admin-Token";function r(){const e=i.a.get(o);return console.log("获取到的token:",e),e}function s(e){return console.log("设置token:",e),i.a.set(o,e,{expires:7})}function c(){return console.log("移除token"),i.a.remove(o)}},6434:function(e,t,n){"use strict";n("020e")},6861:function(e,t,n){e.exports={menuText:"#bfcbd9",menuActiveText:"#409eff",subMenuActiveText:"#f4f4f5",menuBg:"#304156",menuHover:"#263445",subMenuBg:"#1f2d3d",subMenuHover:"#001528",sideBarWidth:"210px"}},"87b4":function(e,t,n){"use strict";var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"qrcode-generator"},[t("div",{staticClass:"qrcode-wrapper"},[t("canvas",{ref:"qrcodeCanvas",staticClass:"qrcode-canvas"}),e.error?t("div",{staticClass:"qrcode-error"},[e._v(" "+e._s(e.errorMessage||"二维码生成失败")+" ")]):e._e()]),e.showActions?t("div",{staticClass:"qrcode-actions"},[e._t("actions",(function(){return[t("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.downloadQrcode}},[e._v("下载")]),t("el-button",{attrs:{type:"success",size:"small"},on:{click:e.printQrcode}},[e._v("打印")])]}))],2):e._e()])},i=[],o=n("d055"),r=n.n(o),s={name:"QrcodeGenerator",props:{content:{type:String,default:""},wifiInfo:{type:Object,default:null},width:{type:Number,default:200},margin:{type:Number,default:2},color:{type:Object,default:()=>({dark:"#000000",light:"#FFFFFF"})},showActions:{type:Boolean,default:!0},fileName:{type:String,default:"qrcode"},title:{type:String,default:"二维码"},printInfo:{type:Object,default:null}},data(){return{error:!1,errorMessage:"",qrcodeDataUrl:"",generatedContent:""}},computed:{qrcodeContent(){if(this.wifiInfo&&this.wifiInfo.ssid&&this.wifiInfo.password){const e=this.wifiInfo.hidden?"true":"false";return`WIFI:T:WPA;S:${this.wifiInfo.ssid};P:${this.wifiInfo.password};H:${e};;`}return this.content||""}},watch:{qrcodeContent:{handler(e){e&&e!==this.generatedContent&&this.$nextTick(()=>{this.generateQRCode()})},immediate:!0}},methods:{generateQRCode(){if(!this.qrcodeContent)return console.warn("二维码内容为空"),this.error=!0,void(this.errorMessage="二维码内容为空");this.error=!1,this.errorMessage="";const e=this.$refs.qrcodeCanvas;if(!e)return console.error("Canvas元素未找到"),this.error=!0,void(this.errorMessage="Canvas元素未找到");r.a.toCanvas(e,this.qrcodeContent,{width:this.width,margin:this.margin,color:this.color},t=>{t?(console.error("生成二维码失败:",t),this.error=!0,this.errorMessage="生成二维码失败: "+t.message):(console.log("二维码生成成功"),this.generatedContent=this.qrcodeContent,this.qrcodeDataUrl=e.toDataURL("image/png"),this.$emit("generated",this.qrcodeDataUrl))})},downloadQrcode(){if(this.qrcodeDataUrl)try{const e=document.createElement("a");e.href=this.qrcodeDataUrl,e.download=this.fileName+".png",document.body.appendChild(e),e.click(),document.body.removeChild(e),this.$message.success("二维码下载成功"),this.$emit("downloaded")}catch(e){console.error("下载二维码失败:",e),this.$message.error("下载二维码失败")}else this.$message.error("二维码尚未生成")},printQrcode(){if(this.qrcodeDataUrl)try{const e=window.open("","_blank");if(!e)return void this.$message.error("打印窗口被阻止，请允许弹出窗口");let t="";if(this.wifiInfo)t=`\n            <div class="info-section">\n              <p><strong>WiFi名称:</strong> ${this.wifiInfo.ssid}</p>\n              <p><strong>WiFi密码:</strong> ${this.wifiInfo.password}</p>\n              ${this.printInfo&&this.printInfo.merchant?`<p><strong>商户名称:</strong> ${this.printInfo.merchant}</p>`:""}\n            </div>\n          `;else if(this.printInfo){t='<div class="info-section">';for(const e in this.printInfo)Object.prototype.hasOwnProperty.call(this.printInfo,e)&&(t+=`<p><strong>${e}:</strong> ${this.printInfo[e]}</p>`);t+="</div>"}const n=`\n          <html>\n            <head>\n              <title>${this.title}</title>\n              <style>\n                body {\n                  font-family: Arial, sans-serif;\n                  text-align: center;\n                  padding: 20px;\n                }\n                .qrcode-container {\n                  margin: 20px auto;\n                }\n                .qrcode-image {\n                  max-width: 300px;\n                }\n                .info-section {\n                  margin-top: 20px;\n                  font-size: 16px;\n                }\n              </style>\n            </head>\n            <body>\n              <h2>${this.title}</h2>\n              <div class="qrcode-container">\n                <img src="${this.qrcodeDataUrl}" alt="二维码" class="qrcode-image">\n              </div>\n              ${t}\n            </body>\n          </html>\n        `;e.document.open(),e.document.write(n),e.document.close(),setTimeout(()=>{e.print(),e.close(),this.$emit("printed")},500)}catch(e){console.error("打印二维码失败:",e),this.$message.error("打印二维码失败")}else this.$message.error("二维码尚未生成")},regenerate(){this.generateQRCode()}}},c=s,l=(n("8d19"),n("2877")),d=Object(l["a"])(c,a,i,!1,null,"3d337a14",null),u=d.exports;u.install=function(e){e.component(u.name,u)};t["a"]=u},"8cf8":function(e,t,n){"use strict";n("186b")},"8d19":function(e,t,n){"use strict";n("fe18")},"8df1":function(e,t,n){e.exports={menuText:"#bfcbd9",menuActiveText:"#409eff",subMenuActiveText:"#f4f4f5",menuBg:"#304156",menuHover:"#263445",subMenuBg:"#1f2d3d",subMenuHover:"#001528",sideBarWidth:"210px"}},"9d64":function(e,t){e.exports="data:image/png;base64,"},a18c:function(e,t,n){"use strict";n.d(t,"b",(function(){return ge}));var a=n("2b0e"),i=n("8c4f"),o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"app-wrapper"},[t("sidebar",{staticClass:"sidebar-container"}),t("div",{staticClass:"main-container"},[t("div",{staticClass:"header-container"},[t("navbar")],1),t("app-main")],1)],1)},r=[],s=function(){var e=this,t=e._self._c;return t("section",{staticClass:"app-main"},[t("transition",{attrs:{name:"fade-transform",mode:"out-in"}},[t("router-view",{key:e.key})],1)],1)},c=[],l={name:"AppMain",computed:{key(){return this.$route.path}}},d=l,u=(n("8cf8"),n("2877")),m=Object(u["a"])(d,s,c,!1,null,"7ad24f2e",null),h=m.exports,p=function(){var e=this,t=e._self._c;return t("div",{staticClass:"navbar"},[t("div",{staticClass:"left-menu"},[t("div",{staticClass:"hamburger-container",on:{click:e.toggleSideBar}},[t("i",{class:["el-icon-s-fold",e.isActive?"is-active":""]})]),t("breadcrumb",{staticClass:"breadcrumb-container"})],1),t("div",{staticClass:"right-menu"},[t("el-dropdown",{staticClass:"avatar-container",attrs:{trigger:"click"}},[t("div",{staticClass:"avatar-wrapper"},[t("img",{staticClass:"user-avatar",attrs:{src:"https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif?imageView2/1/w/80/h/80"}}),t("i",{staticClass:"el-icon-caret-bottom"})]),t("el-dropdown-menu",{staticClass:"user-dropdown",attrs:{slot:"dropdown"},slot:"dropdown"},[t("router-link",{attrs:{to:"/"}},[t("el-dropdown-item",[e._v(" 首页 ")])],1),t("a",{attrs:{target:"_blank",href:"https://github.com/"}},[t("el-dropdown-item",[e._v(" 项目地址 ")])],1),t("el-dropdown-item",{attrs:{divided:""},nativeOn:{click:function(t){return e.logout.apply(null,arguments)}}},[t("span",{staticStyle:{display:"block"}},[e._v("退出登录")])])],1)],1)],1)])},f=[],b=(n("14d9"),n("2f62")),g=function(){var e=this,t=e._self._c;return t("el-breadcrumb",{staticClass:"app-breadcrumb",attrs:{separator:"/"}},[t("transition-group",{attrs:{name:"breadcrumb"}},e._l(e.levelList,(function(n,a){return t("el-breadcrumb-item",{key:n.path},["noRedirect"===n.redirect||a===e.levelList.length-1?t("span",{staticClass:"no-redirect"},[e._v(" "+e._s(n.meta.title)+" ")]):t("a",{on:{click:function(t){return t.preventDefault(),e.handleLink(n)}}},[e._v(e._s(n.meta.title))])])})),1)],1)},v=[],k=(n("e9f5"),n("910d"),n("0fd2")),w=n.n(k),y={name:"Breadcrumb",data(){return{levelList:null}},watch:{$route(){this.getBreadcrumb()}},created(){this.getBreadcrumb()},methods:{getBreadcrumb(){let e=this.$route.matched.filter(e=>e.meta&&e.meta.title);const t=e[0];t&&"/dashboard"!==t.path&&(e=[{path:"/dashboard",meta:{title:"首页"}}].concat(e)),this.levelList=e.filter(e=>e.meta&&e.meta.title&&!1!==e.meta.breadcrumb)},pathCompile(e){const{params:t}=this.$route,n=w.a.compile(e);return n(t)},handleLink(e){const{redirect:t,path:n}=e;t?this.$router.push(t):this.$router.push(this.pathCompile(n))}}},C=y,M=(n("cbf3"),Object(u["a"])(C,g,v,!1,null,"d2b2784e",null)),S=M.exports,O={name:"Navbar",components:{Breadcrumb:S},computed:{...Object(b["b"])(["sidebar","avatar"]),isActive(){return this.sidebar.opened}},methods:{toggleSideBar(){this.$store.dispatch("app/toggleSideBar")},async logout(){await this.$store.dispatch("user/logout"),this.$router.push("/login?redirect="+this.$route.fullPath)}}},_=O,E=(n("de47"),Object(u["a"])(_,p,f,!1,null,"586ab839",null)),T=E.exports,x=function(){var e=this,t=e._self._c;return t("div",{class:{"has-logo":!0}},[t("logo",{attrs:{collapse:e.isCollapse}}),t("el-scrollbar",{attrs:{"wrap-class":"scrollbar-wrapper"}},[t("el-menu",{attrs:{"default-active":e.activeMenu,collapse:e.isCollapse,"background-color":e.variables.menuBg,"text-color":e.variables.menuText,"unique-opened":!1,"active-text-color":e.variables.menuActiveText,"collapse-transition":!1,mode:"vertical"}},e._l(e.routes,(function(e){return t("sidebar-item",{key:e.path,attrs:{item:e,"base-path":e.path}})})),1)],1)],1)},A=[],j=function(){var e=this,t=e._self._c;return t("div",{staticClass:"sidebar-logo-container",class:{collapse:e.collapse}},[t("transition",{attrs:{name:"sidebarLogoFade"}},[e.collapse?t("router-link",{key:"collapse",staticClass:"sidebar-logo-link",attrs:{to:"/"}},[t("img",{staticClass:"sidebar-logo",attrs:{src:n("9d64")}})]):t("router-link",{key:"expand",staticClass:"sidebar-logo-link",attrs:{to:"/"}},[t("img",{staticClass:"sidebar-logo",attrs:{src:n("9d64")}}),t("h1",{staticClass:"sidebar-title"},[e._v(e._s(e.title))])])],1)],1)},L=[],I={name:"Logo",props:{collapse:{type:Boolean,required:!0}},data(){return{title:"WIFI共享商业系统"}}},P=I,$=(n("6434"),Object(u["a"])(P,j,L,!1,null,"463a3aa2",null)),B=$.exports,R=function(){var e=this,t=e._self._c;return e.item.hidden?e._e():t("div",{staticClass:"menu-wrapper"},[!e.hasOneShowingChild(e.item.children,e.item)||e.onlyOneChild.children&&!e.onlyOneChild.noShowingChildren||e.item.alwaysShow?t("el-submenu",{ref:"subMenu",attrs:{index:e.resolvePath(e.item.path),"popper-append-to-body":""}},[t("template",{slot:"title"},[e.item.meta?t("item",{attrs:{icon:e.item.meta&&e.item.meta.icon,title:e.item.meta.title}}):e._e()],1),e._l(e.item.children,(function(n){return t("sidebar-item",{key:n.path,staticClass:"nest-menu",attrs:{"is-nest":!0,item:n,"base-path":e.resolvePath(n.path)}})}))],2):[e.onlyOneChild.meta?t("app-link",{attrs:{to:e.resolvePath(e.onlyOneChild.path)}},[t("el-menu-item",{class:{"submenu-title-noDropdown":!e.isNest},attrs:{index:e.resolvePath(e.onlyOneChild.path)}},[t("item",{attrs:{icon:e.onlyOneChild.meta.icon||e.item.meta&&e.item.meta.icon,title:e.onlyOneChild.meta.title}})],1)],1):e._e()]],2)},q=[],D=n("df7c"),F=n.n(D);function W(e){return/^(https?:|mailto:|tel:)/.test(e)}var U,N,G={name:"MenuItem",functional:!0,props:{icon:{type:String,default:""},title:{type:String,default:""}},render(e,t){const{icon:n,title:a}=t.props,i=[];return n&&i.push(e("i",{class:n})),a&&i.push(e("span",{slot:"title"},[a])),i}},H=G,Q=Object(u["a"])(H,U,N,!1,null,null,null),z=Q.exports,K=function(){var e=this,t=e._self._c;return t(e.type,e._b({tag:"component"},"component",e.linkProps(e.to),!1),[e._t("default")],2)},V=[],J={props:{to:{type:String,required:!0}},computed:{isExternal(){return W(this.to)},type(){return this.isExternal?"a":"router-link"}},methods:{linkProps(e){return this.isExternal?{href:e,target:"_blank",rel:"noopener"}:{to:e}}}},Y=J,X=Object(u["a"])(Y,K,V,!1,null,null,null),Z=X.exports,ee={name:"SidebarItem",components:{Item:z,AppLink:Z},props:{item:{type:Object,required:!0},isNest:{type:Boolean,default:!1},basePath:{type:String,default:""}},data(){return this.onlyOneChild=null,{}},methods:{hasOneShowingChild(e=[],t){const n=e.filter(e=>!e.hidden&&(this.onlyOneChild=e,!0));return 1===n.length||0===n.length&&(this.onlyOneChild={...t,path:"",noShowingChildren:!0},!0)},resolvePath(e){return W(e)?e:W(this.basePath)?this.basePath:F.a.resolve(this.basePath,e)}}},te=ee,ne=Object(u["a"])(te,R,q,!1,null,null,null),ae=ne.exports,ie=n("8df1"),oe=n.n(ie),re={components:{SidebarItem:ae,Logo:B},computed:{...Object(b["b"])(["sidebar"]),routes(){return this.$router.options.routes},activeMenu(){const e=this.$route,{meta:t,path:n}=e;return t.activeMenu?t.activeMenu:n},isCollapse(){return!this.sidebar.opened},variables(){return oe.a}}},se=re,ce=Object(u["a"])(se,x,A,!1,null,null,null),le=ce.exports,de={name:"Layout",components:{Sidebar:le,AppMain:h,Navbar:T}},ue=de,me=(n("df93"),Object(u["a"])(ue,o,r,!1,null,"25539c5c",null)),he=me.exports;a["default"].use(i["a"]);const pe=[{path:"/login",component:()=>n.e("chunk-30b68098").then(n.bind(null,"9ed6")),hidden:!0},{path:"/404",component:()=>n.e("chunk-7278e502").then(n.bind(null,"2754")),hidden:!0},{path:"/",component:he,redirect:"/dashboard",children:[{path:"dashboard",component:()=>Promise.all([n.e("chunk-6ef55aeb"),n.e("chunk-9b6408e0"),n.e("chunk-2d0b8e66"),n.e("chunk-e77d8caa")]).then(n.bind(null,"9406")),name:"Dashboard",meta:{title:"仪表盘",icon:"dashboard",affix:!0}},{path:"platform-stats",component:()=>Promise.all([n.e("chunk-6ef55aeb"),n.e("chunk-9b6408e0"),n.e("chunk-2d0b8e66"),n.e("chunk-6d83931a")]).then(n.bind(null,"f0e7")),name:"PlatformStats",meta:{title:"平台数据统计",icon:"chart",affix:!1}}]},{path:"/wifi",component:he,redirect:"/wifi/list",name:"WiFiManage",meta:{title:"WiFi码管理",icon:"wifi"},children:[{path:"list",component:()=>n.e("chunk-5d7bd3fc").then(n.bind(null,"21e5")),name:"WiFiList",meta:{title:"WiFi码列表"}},{path:"create",component:()=>n.e("chunk-a321dbc6").then(n.bind(null,"d0c3")),name:"WiFiCreate",meta:{title:"创建WiFi码"}},{path:"edit/:id",component:()=>n.e("chunk-a321dbc6").then(n.bind(null,"d0c3")),name:"WiFiEdit",meta:{title:"编辑WiFi码",activeMenu:"/wifi/list"},hidden:!0},{path:"detail/:id",component:()=>n.e("chunk-7caaaced").then(n.bind(null,"6877")),name:"WiFiDetail",meta:{title:"WiFi码详情",activeMenu:"/wifi/list"},hidden:!0},{path:"stats",component:()=>Promise.all([n.e("chunk-6ef55aeb"),n.e("chunk-318fd48e")]).then(n.bind(null,"b877")),name:"WiFiStats",meta:{title:"WiFi码统计"}}]},{path:"/user",component:he,redirect:"/user/list",name:"UserManage",meta:{title:"用户管理",icon:"user"},children:[{path:"list",component:()=>n.e("chunk-69f562dc").then(n.bind(null,"3903")),name:"UserList",meta:{title:"用户列表"}},{path:"detail/:id",component:()=>n.e("chunk-7977403a").then(n.bind(null,"d124")),name:"UserDetail",meta:{title:"用户详情",activeMenu:"/user/list"},hidden:!0},{path:"tag",component:()=>n.e("chunk-63f1def8").then(n.bind(null,"50e4")),name:"UserTag",meta:{title:"用户标签"}},{path:"alliance",component:()=>n.e("chunk-ceb0208a").then(n.bind(null,"c6cd6")),name:"AllianceList",meta:{title:"联盟申请"}},{path:"alliance/detail/:id",component:()=>n.e("chunk-df93d3ac").then(n.bind(null,"c4a2")),name:"AllianceDetail",meta:{title:"申请详情",activeMenu:"/user/alliance"},hidden:!0}]},{path:"/team",component:he,redirect:"/team/list",name:"TeamManage",meta:{title:"团队管理",icon:"team"},children:[{path:"list",component:()=>n.e("chunk-146c590a").then(n.bind(null,"29b5")),name:"TeamList",meta:{title:"团队列表"}},{path:"detail/:id",component:()=>n.e("chunk-3a4494bc").then(n.bind(null,"d034")),name:"TeamDetail",meta:{title:"团队详情",activeMenu:"/team/list"},hidden:!0},{path:"create",component:()=>n.e("chunk-517cd6cc").then(n.bind(null,"bce4")),name:"TeamCreate",meta:{title:"创建团队"}},{path:"edit/:id",component:()=>n.e("chunk-517cd6cc").then(n.bind(null,"bce4")),name:"TeamEdit",meta:{title:"编辑团队",activeMenu:"/team/list"},hidden:!0},{path:"members/:id",component:()=>n.e("chunk-4b53819e").then(n.bind(null,"fe24")),name:"TeamMembers",meta:{title:"成员管理",activeMenu:"/team/list"},hidden:!0}]},{path:"/mall",component:he,redirect:"/mall/goods",name:"MallManage",meta:{title:"商城管理",icon:"shopping"},children:[{path:"goods",component:()=>n.e("chunk-5662520c").then(n.bind(null,"02d0")),name:"GoodsList",meta:{title:"商品列表"}},{path:"goods/create",component:()=>n.e("chunk-fffc8e28").then(n.bind(null,"487e")),name:"GoodsCreate",meta:{title:"创建商品",activeMenu:"/mall/goods"},hidden:!0},{path:"goods/edit/:id",component:()=>n.e("chunk-fffc8e28").then(n.bind(null,"487e")),name:"GoodsEdit",meta:{title:"编辑商品",activeMenu:"/mall/goods"},hidden:!0},{path:"goods/detail/:id",component:()=>n.e("chunk-7a11e527").then(n.bind(null,"b808")),name:"GoodsDetail",meta:{title:"商品详情",activeMenu:"/mall/goods"},hidden:!0},{path:"order",component:()=>n.e("chunk-2ee25716").then(n.bind(null,"2f90")),name:"OrderList",meta:{title:"订单列表"}},{path:"order/detail/:id",component:()=>n.e("chunk-33b437fd").then(n.bind(null,"226d")),name:"OrderDetail",meta:{title:"订单详情",activeMenu:"/mall/order"},hidden:!0}]},{path:"/profit",component:he,redirect:"/profit/rules",name:"ProfitManage",meta:{title:"分润管理",icon:"money"},children:[{path:"rules",component:()=>n.e("chunk-0e80cf1a").then(n.bind(null,"8935")),name:"ProfitRules",meta:{title:"分润规则"}},{path:"bill",component:()=>Promise.all([n.e("chunk-6ef55aeb"),n.e("chunk-9b6408e0"),n.e("chunk-afdd2304")]).then(n.bind(null,"a7de")),name:"BillList",meta:{title:"分润账单"}},{path:"bill/detail/:id",component:()=>n.e("chunk-44411ae0").then(n.bind(null,"2edf")),name:"BillDetail",meta:{title:"账单详情",activeMenu:"/profit/bill"},hidden:!0},{path:"withdraw",component:()=>n.e("chunk-f8fcabec").then(n.bind(null,"b6cc")),name:"WithdrawList",meta:{title:"提现管理"}},{path:"withdraw/detail/:id",component:()=>n.e("chunk-9d1b964c").then(n.bind(null,"10ec")),name:"WithdrawDetail",meta:{title:"提现详情",activeMenu:"/profit/withdraw"},hidden:!0}]},{path:"/ad",component:he,redirect:"/ad/space",name:"AdManage",meta:{title:"广告管理",icon:"ad"},children:[{path:"space",component:()=>n.e("chunk-56b99bb8").then(n.bind(null,"0867")),name:"AdSpaceList",meta:{title:"广告位管理"}},{path:"space/create",component:()=>n.e("chunk-62d8c230").then(n.bind(null,"89d2")),name:"AdSpaceCreate",meta:{title:"创建广告位",activeMenu:"/ad/space"},hidden:!0},{path:"space/edit/:id",component:()=>n.e("chunk-62d8c230").then(n.bind(null,"89d2")),name:"AdSpaceEdit",meta:{title:"编辑广告位",activeMenu:"/ad/space"},hidden:!0},{path:"content",component:()=>n.e("chunk-0742c010").then(n.bind(null,"0eb9")),name:"AdContentList",meta:{title:"广告内容管理"}},{path:"content/create",component:()=>n.e("chunk-1df60e83").then(n.bind(null,"372b")),name:"AdContentCreate",meta:{title:"创建广告内容",activeMenu:"/ad/content"},hidden:!0},{path:"content/edit/:id",component:()=>n.e("chunk-1df60e83").then(n.bind(null,"372b")),name:"AdContentEdit",meta:{title:"编辑广告内容",activeMenu:"/ad/content"},hidden:!0},{path:"stats",component:()=>Promise.all([n.e("chunk-6ef55aeb"),n.e("chunk-9b6408e0"),n.e("chunk-2d0b8e66"),n.e("chunk-2c6f5b2e")]).then(n.bind(null,"87b5")),name:"AdStats",meta:{title:"广告统计"}}]},{path:"/region",component:he,redirect:"/region/list",name:"RegionManage",meta:{title:"地区管理",icon:"location"},children:[{path:"list",component:()=>n.e("chunk-30bb6741").then(n.bind(null,"e587")),name:"RegionList",meta:{title:"地区列表"}},{path:"detail/:id",component:()=>n.e("chunk-7c28e14e").then(n.bind(null,"c29d")),name:"RegionDetail",meta:{title:"地区详情",activeMenu:"/region/list"},hidden:!0}]},{path:"/wallet",component:he,redirect:"/wallet/list",name:"WalletManage",meta:{title:"钱包管理",icon:"wallet"},children:[{path:"list",component:()=>n.e("chunk-7d3b2848").then(n.bind(null,"af1c")),name:"WalletList",meta:{title:"钱包列表"}},{path:"detail/:userId",component:()=>n.e("chunk-16bc5790").then(n.bind(null,"6418")),name:"WalletDetail",meta:{title:"钱包详情",activeMenu:"/wallet/list"},hidden:!0},{path:"transactions/:userId",component:()=>n.e("chunk-028d69b8").then(n.bind(null,"b6df")),name:"WalletTransactions",meta:{title:"交易记录",activeMenu:"/wallet/list"},hidden:!0}]},{path:"/platform",component:he,redirect:"/platform/revenue",name:"PlatformManage",meta:{title:"平台管理",icon:"platform"},children:[{path:"revenue",component:()=>Promise.all([n.e("chunk-6ef55aeb"),n.e("chunk-9b6408e0"),n.e("chunk-2d0b8e66"),n.e("chunk-74e17ec6")]).then(n.bind(null,"ca3e")),name:"PlatformRevenue",meta:{title:"收益管理"}}]},{path:"/system",component:he,redirect:"/system/config",name:"SystemManage",meta:{title:"系统设置",icon:"setting"},children:[{path:"config",component:()=>n.e("chunk-670ee9fe").then(n.bind(null,"97d1")),name:"SystemConfig",meta:{title:"基础设置"}},{path:"role",component:()=>n.e("chunk-a6af48c6").then(n.bind(null,"7f67")),name:"RoleList",meta:{title:"角色管理"}},{path:"role/create",component:()=>n.e("chunk-8d44f6a0").then(n.bind(null,"3260")),name:"RoleCreate",meta:{title:"创建角色",activeMenu:"/system/role"},hidden:!0},{path:"role/edit/:id",component:()=>n.e("chunk-8d44f6a0").then(n.bind(null,"3260")),name:"RoleEdit",meta:{title:"编辑角色",activeMenu:"/system/role"},hidden:!0},{path:"account",component:()=>n.e("chunk-02ae6eec").then(n.bind(null,"4b54")),name:"AccountList",meta:{title:"账号管理"}},{path:"account/create",component:()=>n.e("chunk-2589c2b1").then(n.bind(null,"73ca")),name:"AccountCreate",meta:{title:"创建账号",activeMenu:"/system/account"},hidden:!0},{path:"account/edit/:id",component:()=>n.e("chunk-2589c2b1").then(n.bind(null,"73ca")),name:"AccountEdit",meta:{title:"编辑账号",activeMenu:"/system/account"},hidden:!0},{path:"log",component:()=>n.e("chunk-2398ebba").then(n.bind(null,"2628")),name:"LogManage",meta:{title:"日志管理"}}]},{path:"*",redirect:"/404",hidden:!0}],fe=()=>new i["a"]({mode:"history",scrollBehavior:()=>({y:0}),routes:pe}),be=fe();function ge(){const e=fe();be.matcher=e.matcher}t["a"]=be},b775:function(e,t,n){"use strict";n("d9e2");var a=n("bc3a"),i=n.n(a),o=n("5c96"),r=n("4360"),s=n("5f87");const c=i.a.create({baseURL:"http://101.37.255.139:4000",timeout:12e4,headers:{"Content-Type":"application/json",Accept:"application/json","Cache-Control":"no-cache",Pragma:"no-cache"},withCredentials:!1});c.interceptors.request.use(e=>{if(e.url&&e.url.includes("api.yhchj.com"))return console.warn("阻止了对 api.yhchj.com 的请求:",e.url),Promise.reject(new Error("Blocked request to api.yhchj.com"));if(e.url&&e.url.startsWith("/api")){const t=e.baseURL||"";t.includes("://")&&(e.url=t+e.url,e.baseURL="")}return r["a"].getters.token&&(e.headers.Authorization="Bearer "+Object(s["a"])()),console.log("Request Config:",{url:e.url,method:e.method,baseURL:e.baseURL,fullURL:(e.baseURL||"")+(e.url||""),headers:e.headers,data:e.data,params:e.params}),e},e=>(console.error("Request Error:",e),Promise.reject(e))),c.interceptors.response.use(e=>{const t=e.data;return"blob"===e.config.responseType||"arraybuffer"===e.config.responseType?e.data:t},e=>{console.error("Response Error:",{error:e,response:e.response,config:e.config});let t=e.message;if(e.response&&e.response.data&&e.response.data.message)t=e.response.data.message;else if(e.response)switch(e.response.status){case 400:t="请求参数错误";break;case 401:t="未授权，请重新登录",r["a"].dispatch("user/resetToken").then(()=>{location.reload()});break;case 403:t="拒绝访问";break;case 404:t="请求错误，未找到该资源";break;case 405:t="请求方法不允许";break;case 408:t="请求超时";break;case 500:t="服务器内部错误";break;case 501:t="服务未实现";break;case 502:t="网关错误";break;case 503:t="服务不可用";break;case 504:t="网关超时";break;case 505:t="HTTP版本不受支持";break;default:t="连接错误 "+e.response.status}else t=e.request?"网络连接异常，请检查您的网络连接":"发送请求失败";return Object(o["Message"])({message:t,type:"error",duration:5e3}),Promise.reject(e)}),t["a"]=c},c24f:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"a",(function(){return r})),n.d(t,"c",(function(){return s})),n.d(t,"d",(function(){return c}));n("d9e2");var a=n("b775");const i=!1;function o(e){return i?new Promise(t=>{setTimeout(()=>{"admin"===e.username&&"admin123"===e.password?t({code:200,data:{token:"admin-token-mock"},message:"登录成功"}):t({code:401,message:"用户名或密码错误"})},300)}):(console.log("发送登录请求:",e),Object(a["a"])({url:"/api/v1/admin/auth/admin-login",method:"post",data:e}))}function r(e){return i?new Promise(t=>{setTimeout(()=>{t("admin-token-mock"===e?{code:200,data:{roles:["admin"],name:"管理员",avatar:"https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif"},message:"获取用户信息成功"}:{code:50008,message:"登录状态已过期，请重新登录"})},300)}):(console.log("获取用户信息，token:",e),e?Object(a["a"])({url:"/api/v1/admin/auth/user-info",method:"get"}):(console.error("Token不存在，无法获取用户信息"),Promise.reject(new Error("Token不存在"))))}function s(){return i?new Promise(e=>{setTimeout(()=>{e({code:200,data:{},message:"登出成功"})},300)}):Object(a["a"])({url:"/api/v1/admin/auth/logout",method:"post"})}function c(e){return Object(a["a"])({url:"/api/v1/admin/user/search",method:"get",params:e})}},cbf3:function(e,t,n){"use strict";n("5758")},de47:function(e,t,n){"use strict";n("253b")},df93:function(e,t,n){"use strict";n("3bab")},e4b1:function(e,t,n){"use strict";n("3b62")},fe18:function(e,t,n){}},[[0,"runtime","chunk-elementUI","chunk-libs"]]]);