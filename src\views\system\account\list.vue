<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="queryParams.username"
        placeholder="用户名"
        style="width: 200px;"
        class="filter-item"
        clearable
      />
      <el-select v-model="queryParams.status" placeholder="状态" clearable style="width: 120px" class="filter-item">
        <el-option label="启用" value="1" />
        <el-option label="禁用" value="0" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
      <el-button class="filter-item" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleCreate">创建账号</el-button>
      <el-button class="filter-item" type="warning" icon="el-icon-refresh-right" @click="forceRefresh">强制刷新</el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="accountList"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="username" label="用户名" width="150" />
      <el-table-column prop="real_name" label="真实姓名" width="150" />
      <el-table-column label="头像" width="100">
        <template slot-scope="scope">
          <el-avatar v-if="scope.row.avatar" :src="scope.row.avatar" :size="40"></el-avatar>
          <el-avatar v-else icon="el-icon-user-solid" :size="40"></el-avatar>
        </template>
      </el-table-column>
      <el-table-column prop="role_name" label="角色" width="150" />
      <el-table-column prop="email" label="邮箱" width="180" />
      <el-table-column prop="phone" label="手机号" width="130" />
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="last_login_time" label="最后登录时间" width="180" />
      <el-table-column prop="created_at" label="创建时间" width="180" />
      <el-table-column label="操作" width="200" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleUpdate(scope.row)"
          >编辑</el-button>
          <el-button
            size="mini"
            :type="scope.row.status === 1 ? 'warning' : 'success'"
            @click="handleChangeStatus(scope.row)"
          >{{ scope.row.status === 1 ? '禁用' : '启用' }}</el-button>
          <el-button
            v-if="scope.row.username !== 'admin'"
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { getAccountList, deleteAccount, updateAccount } from '@/api/system'

export default {
  name: 'AccountList',
  data () {
    return {
      loading: false,
      total: 0,
      queryParams: {
        page: 1,
        limit: 10,
        username: undefined,
        status: undefined
      },
      accountList: []
    }
  },
  created () {
    this.getList()
  },
  activated() {
    // 在keep-alive组件激活时，重新获取列表，确保数据最新
    console.log('账号列表组件被激活，强制刷新列表')
    this.forceRefresh()
  },
  // 路由进入前钩子
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 如果是从账号创建页面返回，强制刷新账号列表
      if (from.path === '/system/account/create' || from.path === '/system/role/create') {
        console.log('从账号创建或角色创建页面返回，强制刷新账号列表')
        vm.forceRefresh()
      }
    })
  },
  methods: {
    getList () {
      this.loading = true
      console.log('开始获取账号列表...')
      
      // 清除可能的缓存
      localStorage.removeItem('wifi_admin_accounts_processed')
      localStorage.removeItem('wifi_admin_accounts_cache')
      
      // 直接从localStorage获取账号数据
      try {
        const storedAccounts = localStorage.getItem('wifi_admin_accounts')
        console.log('从localStorage获取的原始账号数据:', storedAccounts)
        
        if (storedAccounts) {
          let accounts = JSON.parse(storedAccounts)
          console.log('解析后的账号数据:', accounts)
          
          // 获取最新的角色数据
          const storedRoles = localStorage.getItem('wifi_admin_roles')
          console.log('从localStorage获取的角色数据:', storedRoles)
          
          if (storedRoles) {
            const roles = JSON.parse(storedRoles)
            
            // 更新角色名称
            accounts = accounts.map(account => {
              const role = roles.find(r => r.id === account.role_id)
              return {
                ...account,
                role_name: role ? role.name : '未知角色'
              }
            })
            
            // 根据查询条件过滤
            if (this.queryParams.username) {
              accounts = accounts.filter(item => item.username.includes(this.queryParams.username))
            }
            if (this.queryParams.status !== undefined && this.queryParams.status !== '') {
              accounts = accounts.filter(item => item.status === parseInt(this.queryParams.status))
            }
            
            // 更新列表数据
            this.accountList = accounts
            this.total = accounts.length
            console.log('最终显示的账号列表:', this.accountList)
            this.loading = false
            return
          }
        }
      } catch (e) {
        console.error('从localStorage获取账号数据失败:', e)
      }
      
      // 如果从localStorage获取失败，则通过API获取
      getAccountList(this.queryParams).then(response => {
        this.accountList = response.data.list || []
        this.total = response.data.total || 0
        this.loading = false
        console.log('通过API获取的账户列表:', this.accountList)
      }).catch(() => {
        this.loading = false
      })
    },
    handleQuery () {
      this.queryParams.page = 1
      this.getList()
    },
    resetQuery () {
      this.queryParams = {
        page: 1,
        limit: 10,
        username: undefined,
        status: undefined
      }
      this.getList()
    },
    handleSizeChange (val) {
      this.queryParams.limit = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.queryParams.page = val
      this.getList()
    },
    handleCreate () {
      this.$router.push('/system/account/create')
    },
    handleUpdate (row) {
      console.log('编辑账号:', row)
      // 确保ID是字符串类型，避免路由参数类型问题
      const id = String(row.id)
      this.$router.push(`/system/account/edit/${id}`)
    },
    handleChangeStatus (row) {
      const newStatus = row.status === 1 ? 0 : 1
      const statusText = newStatus === 1 ? '启用' : '禁用'

      this.$confirm(`确认要${statusText}该账号吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateAccount(row.id, { status: newStatus }).then(() => {
          this.$message.success(`${statusText}成功`)
          row.status = newStatus // 直接更新本地状态
        })
      }).catch(() => {})
    },
    handleDelete (row) {
      console.log('删除账号:', row)
      this.$confirm('确认删除该账号吗? 删除后不可恢复!', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 确保ID是字符串类型
        const id = String(row.id)
        deleteAccount(id).then(() => {
          this.$message.success('删除成功')
          
          // 同步更新localStorage中的数据
          try {
            const storedAccounts = localStorage.getItem('wifi_admin_accounts')
            if (storedAccounts) {
              let accounts = JSON.parse(storedAccounts)
              // 使用parseInt确保ID类型匹配
              const index = accounts.findIndex(item => parseInt(item.id) === parseInt(id))
              if (index > -1) {
                accounts.splice(index, 1)
                localStorage.setItem('wifi_admin_accounts', JSON.stringify(accounts))
              }
            }
          } catch (e) {
            console.error('更新localStorage账号数据失败:', e)
          }
          
          this.getList()
        })
      }).catch(() => {})
    },
    forceRefresh () {
      localStorage.removeItem('wifi_admin_accounts_processed')
      localStorage.removeItem('wifi_admin_accounts_cache')
      getAccountList().then(response => {
        if (response.code === 200) {
          // 强制更新列表
          this.getList()
          this.$message.success('强制刷新成功！')
        }
      }).catch(error => {
        console.error('强制刷新失败:', error)
        this.$message.error('强制刷新失败，请重试')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
}
.pagination-container {
  margin-top: 15px;
  text-align: right;
}
</style>
