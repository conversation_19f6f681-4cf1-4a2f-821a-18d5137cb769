<template>
  <div class="app-container">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="操作日志" name="operation">
        <el-form :inline="true" :model="queryParams" class="demo-form-inline">
          <el-form-item label="管理员">
            <el-input v-model="queryParams.adminId" placeholder="管理员ID" clearable></el-input>
          </el-form-item>
          <el-form-item label="模块">
            <el-input v-model="queryParams.module" placeholder="模块名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="操作">
            <el-input v-model="queryParams.action" placeholder="操作类型" clearable></el-input>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table
          v-loading="loading"
          :data="operationLogList"
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="adminId" label="管理员ID" width="100" />
          <el-table-column prop="module" label="模块" width="120" />
          <el-table-column prop="action" label="操作" width="120" />
          <el-table-column prop="ip" label="IP地址" width="120" />
          <el-table-column prop="content" label="操作内容" />
          <el-table-column prop="created_at" label="创建时间" width="180" />
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="queryParams.limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </el-tab-pane>

      <el-tab-pane label="登录日志" name="login">
        <el-form :inline="true" :model="queryParams" class="demo-form-inline">
          <el-form-item label="用户ID">
            <el-input v-model="queryParams.userId" placeholder="用户ID" clearable></el-input>
          </el-form-item>
          <el-form-item label="用户类型">
            <el-select v-model="queryParams.userType" placeholder="用户类型" clearable>
              <el-option label="管理员" :value="1"></el-option>
              <el-option label="小程序用户" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="queryParams.status" placeholder="状态" clearable>
              <el-option label="成功" :value="1"></el-option>
              <el-option label="失败" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table
          v-loading="loading"
          :data="loginLogList"
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="user_type" label="用户类型" width="100">
            <template slot-scope="scope">
              {{ scope.row.user_type === 1 ? '管理员' : '小程序用户' }}
            </template>
          </el-table-column>
          <el-table-column prop="user_id" label="用户ID" width="100" />
          <el-table-column prop="ip" label="IP地址" width="120" />
          <el-table-column prop="device" label="设备信息" />
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="登录时间" width="180" />
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="queryParams.limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </el-tab-pane>

      <el-tab-pane label="异常日志" name="error">
        <el-form :inline="true" :model="queryParams" class="demo-form-inline">
          <el-form-item label="模块">
            <el-input v-model="queryParams.module" placeholder="模块名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table
          v-loading="loading"
          :data="errorLogList"
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="module" label="模块" width="120" />
          <el-table-column prop="level" label="级别" width="100">
            <template slot-scope="scope">
              <el-tag :type="getErrorLevelType(scope.row.level)">
                {{ scope.row.level }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="错误信息" />
          <el-table-column prop="stack" label="堆栈信息">
            <template slot-scope="scope">
              <el-button type="text" @click="showStackInfo(scope.row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180" />
        </el-table>

        <div class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="queryParams.page"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="queryParams.limit"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          ></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 查看堆栈信息对话框 -->
    <el-dialog title="堆栈信息" :visible.sync="stackDialogVisible" width="70%">
      <pre>{{ currentStackInfo }}</pre>
    </el-dialog>
  </div>
</template>

<script>
import { getOperationLog, getLoginLog, getErrorLog } from '@/api/system'

export default {
  name: 'LogManagement',
  data () {
    return {
      activeTab: 'operation',
      loading: false,
      dateRange: [],
      queryParams: {
        page: 1,
        limit: 10,
        adminId: undefined,
        module: undefined,
        action: undefined,
        userId: undefined,
        userType: undefined,
        status: undefined,
        startDate: undefined,
        endDate: undefined
      },
      total: 0,
      operationLogList: [],
      loginLogList: [],
      errorLogList: [],
      stackDialogVisible: false,
      currentStackInfo: ''
    }
  },
  created () {
    this.getList()
  },
  watch: {
    activeTab () {
      this.resetQuery()
      this.getList()
    }
  },
  methods: {
    getList () {
      this.loading = true

      // 处理日期范围
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.startDate = this.dateRange[0]
        this.queryParams.endDate = this.dateRange[1]
      }

      const requestMap = {
        operation: getOperationLog,
        login: getLoginLog,
        error: getErrorLog
      }

      const request = requestMap[this.activeTab]
      if (request) {
        request(this.queryParams).then(response => {
          if (this.activeTab === 'operation') {
            this.operationLogList = response.data.list
          } else if (this.activeTab === 'login') {
            this.loginLogList = response.data.list
          } else if (this.activeTab === 'error') {
            this.errorLogList = response.data.list
          }
          this.total = response.data.total
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      }
    },
    handleQuery () {
      this.queryParams.page = 1
      this.getList()
    },
    resetQuery () {
      this.dateRange = []
      this.queryParams = {
        page: 1,
        limit: 10,
        adminId: undefined,
        module: undefined,
        action: undefined,
        userId: undefined,
        userType: undefined,
        status: undefined,
        startDate: undefined,
        endDate: undefined
      }
    },
    handleSizeChange (val) {
      this.queryParams.limit = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.queryParams.page = val
      this.getList()
    },
    getErrorLevelType (level) {
      const levelMap = {
        ERROR: 'danger',
        WARNING: 'warning',
        INFO: 'info',
        DEBUG: 'success'
      }
      return levelMap[level] || 'info'
    },
    showStackInfo (row) {
      this.currentStackInfo = row.stack || '无堆栈信息'
      this.stackDialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.pagination-container {
  margin-top: 15px;
  text-align: right;
}
</style>
