(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-318fd48e"],{"007d":function(t,e,a){"use strict";var i=a("22b4"),r=a("2da7");Object(i["a"])(r["a"])},"13d5":function(t,e,a){"use strict";var i=a("23e7"),r=a("d58f").left,s=a("a640"),n=a("1212"),o=a("9adc"),c=!o&&n>79&&n<83,d=c||!s("reduce");i({target:"Array",proto:!0,forced:d},{reduce:function(t){var e=arguments.length;return r(this,t,e,e>1?arguments[1]:void 0)}})},"3eba":function(t,e,a){"use strict";var i=a("aa74");a.d(e,"a",(function(){return i["a"]}));var r=a("22b4"),s=(a("1be7"),a("f95e")),n=a("5e81"),o=a("ee29");Object(r["a"])([s["a"],n["a"]]);Object(r["a"])(o["a"])},"627c":function(t,e,a){"use strict";var i=a("22b4"),r=a("9394");Object(i["a"])(r["a"])},"6ae5":function(t,e,a){},8558:function(t,e,a){"use strict";var i=a("cfe9"),r=a("b5db"),s=a("c6b6"),n=function(t){return r.slice(0,t.length)===t};t.exports=function(){return n("Bun/")?"BUN":n("Cloudflare-Workers")?"CLOUDFLARE":n("Deno/")?"DENO":n("Node.js/")?"NODE":i.Bun&&"string"==typeof Bun.version?"BUN":i.Deno&&"object"==typeof Deno.version?"DENO":"process"===s(i.process)?"NODE":i.window&&i.document?"BROWSER":"REST"}()},9485:function(t,e,a){"use strict";var i=a("23e7"),r=a("2266"),s=a("59ed"),n=a("825a"),o=a("46c4"),c=a("2a62"),d=a("f99f"),l=a("2ba4"),u=a("d039"),f=TypeError,h=u((function(){[].keys().reduce((function(){}),void 0)})),p=!h&&d("reduce",f);i({target:"Iterator",proto:!0,real:!0,forced:h||p},{reduce:function(t){n(this);try{s(t)}catch(u){c(this,"throw",u)}var e=arguments.length<2,a=e?void 0:arguments[1];if(p)return l(p,this,e?[t]:[t,a]);var i=o(this),d=0;if(r(i,(function(i){e?(e=!1,a=i):a=t(a,i,d),d++}),{IS_RECORD:!0}),e)throw new f("Reduce of empty iterator with no initial value");return a}})},"9adc":function(t,e,a){"use strict";var i=a("8558");t.exports="NODE"===i},a640:function(t,e,a){"use strict";var i=a("d039");t.exports=function(t,e){var a=[][t];return!!a&&i((function(){a.call(null,e||function(){return 1},1)}))}},ab43:function(t,e,a){"use strict";var i=a("23e7"),r=a("c65b"),s=a("59ed"),n=a("825a"),o=a("46c4"),c=a("c5cc"),d=a("9bdd"),l=a("2a62"),u=a("2baa"),f=a("f99f"),h=a("c430"),p=!h&&!u("map",(function(){})),m=!h&&!p&&f("map",TypeError),v=h||p||m,g=c((function(){var t=this.iterator,e=n(r(this.next,t)),a=this.done=!!e.done;if(!a)return d(t,this.mapper,[e.value,this.counter++],!0)}));i({target:"Iterator",proto:!0,real:!0,forced:v},{map:function(t){n(this);try{s(t)}catch(e){l(this,"throw",e)}return m?r(m,this,t):new g(o(this),{mapper:t})}})},b877:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"card-header"},[e("span",[t._v("WiFi码总数")])]),e("div",{staticClass:"card-body"},[e("div",{staticClass:"card-value"},[t._v(t._s(t.stats.total||0))])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"card-header"},[e("span",[t._v("启用中")])]),e("div",{staticClass:"card-body"},[e("div",{staticClass:"card-value"},[t._v(t._s(t.stats.active||0))])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"card-header"},[e("span",[t._v("已禁用")])]),e("div",{staticClass:"card-body"},[e("div",{staticClass:"card-value"},[t._v(t._s(t.stats.inactive||0))])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"card-header"},[e("span",[t._v("总使用次数")])]),e("div",{staticClass:"card-body"},[e("div",{staticClass:"card-value"},[t._v(t._s(t.stats.total_use_count||0))])])])],1)],1),e("el-card",{staticClass:"chart-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("WiFi码使用趋势")]),e("el-radio-group",{staticStyle:{float:"right"},attrs:{size:"mini"},model:{value:t.timeRange,callback:function(e){t.timeRange=e},expression:"timeRange"}},[e("el-radio-button",{attrs:{label:"week"}},[t._v("最近一周")]),e("el-radio-button",{attrs:{label:"month"}},[t._v("最近一月")]),e("el-radio-button",{attrs:{label:"year"}},[t._v("最近一年")])],1)],1),e("div",{ref:"chartContainer",staticClass:"chart-container"})]),e("el-card",{staticClass:"top-wifi-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("热门WiFi码排行")])]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.topWifiList,border:""}},[e("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),e("el-table-column",{attrs:{prop:"title",label:"WiFi标题"}}),e("el-table-column",{attrs:{prop:"name",label:"WiFi名称"}}),e("el-table-column",{attrs:{prop:"merchant_name",label:"商户名称"}}),e("el-table-column",{attrs:{prop:"use_count",label:"使用次数",sortable:""}}),e("el-table-column",{attrs:{label:"操作",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.viewDetail(a.id)}}},[t._v("查看")])]}}])})],1)],1)],1)])},r=[],s=(a("14d9"),a("e9f5"),a("ab43"),a("d251")),n=a("3eba"),o=(a("ef97"),a("007d"),a("627c"),a("d28f"),a("cd12"),{name:"WiFiStats",data(){return{loading:!0,stats:{total:0,active:0,inactive:0,total_use_count:0},timeRange:"week",topWifiList:[],chart:null,trendData:[]}},watch:{timeRange(){this.fetchData()}},mounted(){this.fetchData(),window.addEventListener("resize",this.resizeChart)},beforeDestroy(){this.chart&&this.chart.dispose(),window.removeEventListener("resize",this.resizeChart)},methods:{fetchData(){this.loading=!0,console.log("Fetching stats with time range:",this.timeRange),Object(s["e"])({time_range:this.timeRange}).then(t=>{console.log("Stats response:",t),t.data&&(this.stats=t.data.stats||{total:0,active:0,inactive:0,total_use_count:0},this.trendData=t.data.trend_data||[],this.topWifiList=t.data.top_wifi_list||[],this.$nextTick(()=>{this.initChart(this.trendData)}))}).catch(t=>{console.error("Failed to fetch stats:",t)}).finally(()=>{this.loading=!1})},initChart(t){if(this.$refs.chartContainer){this.chart&&this.chart.dispose();try{this.chart=n["a"](this.$refs.chartContainer);const e=t.map(t=>t.date),a=t.map(t=>t.count),i={tooltip:{trigger:"axis"},xAxis:{type:"category",boundaryGap:!1,data:e},yAxis:{type:"value",name:"使用次数"},series:[{name:"WiFi使用次数",type:"line",smooth:!0,data:a,areaStyle:{opacity:.3},itemStyle:{color:"#409EFF"}}]};this.chart.setOption(i)}catch(e){console.error("Failed to initialize chart:",e)}}else console.error("Chart container not found")},resizeChart(){this.chart&&this.chart.resize()},viewDetail(t){this.$router.push("/wifi/detail/"+t)}}}),c=o,d=(a("d855"),a("2877")),l=Object(d["a"])(c,i,r,!1,null,"42b64bf5",null);e["default"]=l.exports},cd12:function(t,e,a){"use strict";var i=a("22b4"),r=a("4b2a");Object(i["a"])(r["a"])},d251:function(t,e,a){"use strict";a.d(e,"d",(function(){return l})),a.d(e,"c",(function(){return u})),a.d(e,"a",(function(){return f})),a.d(e,"f",(function(){return h})),a.d(e,"b",(function(){return p})),a.d(e,"g",(function(){return m})),a.d(e,"e",(function(){return v}));a("14d9"),a("13d5"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("9485");var i=a("b775");const r=!1,s="wifi_admin_wifi_list",n=[{id:1,title:"WiFi测试1",name:"Test WiFi",password:"12345678",merchant_name:"测试商家1",qrcode:"",use_count:123,user_id:1,status:1,created_at:"2023-06-01 12:30:45"},{id:2,title:"WiFi测试2",name:"Office WiFi",password:"87654321",merchant_name:"测试商家2",qrcode:"",use_count:456,user_id:2,status:1,created_at:"2023-06-02 10:20:30"}];function o(){try{const t=localStorage.getItem(s);return t?JSON.parse(t):n}catch(t){return console.warn("读取WiFi数据失败，使用默认数据:",t),n}}function c(t){try{localStorage.setItem(s,JSON.stringify(t))}catch(e){console.error("保存WiFi数据失败:",e)}}function d(){const t=new Date,e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0"),r=String(t.getHours()).padStart(2,"0"),s=String(t.getMinutes()).padStart(2,"0"),n=String(t.getSeconds()).padStart(2,"0");return`${e}-${a}-${i} ${r}:${s}:${n}`}function l(t){return r?new Promise(e=>{setTimeout(()=>{const a=o(),i=parseInt(t.page)||1,r=parseInt(t.limit)||10,s=(i-1)*r,n=s+r;let c=a;if(t.keyword){const e=t.keyword.toLowerCase();c=a.filter(t=>t.title.toLowerCase().includes(e)||t.name.toLowerCase().includes(e)||t.merchant_name.toLowerCase().includes(e))}void 0!==t.status&&""!==t.status&&(c=c.filter(e=>e.status===parseInt(t.status)));const d=c.slice(s,n);e({code:200,data:{list:d,total:c.length},message:"获取WiFi列表成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/wifi/list",method:"get",params:t})}function u(t){return r?new Promise(e=>{setTimeout(()=>{const a=o(),i=a.find(e=>e.id===parseInt(t));e(i?{code:200,data:i,message:"获取WiFi详情成功"}:{code:404,message:"未找到该WiFi码"})},300)}):(console.log("请求WiFi详情, ID:",t),Object(i["a"])({url:"/api/v1/admin/wifi/detail/"+t,method:"get"}))}function f(t){return r?new Promise(e=>{setTimeout(()=>{const a=o(),i=a.length>0?Math.max(...a.map(t=>t.id))+1:1,r={id:i,title:t.title,name:t.name,password:t.password,merchant_name:t.merchant_name,qrcode:"",use_count:0,user_id:1,status:t.status,created_at:d()};a.push(r),c(a),console.log("模拟数据 - 创建WiFi码成功:",r),console.log("当前WiFi列表:",a),e({code:200,data:{id:i},message:"创建WiFi码成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/wifi/create",method:"post",data:t})}function h(t,e){return r?new Promise(a=>{setTimeout(()=>{const i=o(),r=i.findIndex(e=>e.id===parseInt(t));-1!==r?(i[r]={...i[r],...e},c(i),console.log("模拟数据 - 更新WiFi码成功:",i[r]),a({code:200,data:{},message:"更新WiFi码成功"})):a({code:404,message:"未找到该WiFi码"})},300)}):Object(i["a"])({url:"/api/v1/admin/wifi/update/"+t,method:"put",data:e})}function p(t){return r?new Promise(e=>{setTimeout(()=>{const a=o(),i=a.findIndex(e=>e.id===parseInt(t));-1!==i?(a.splice(i,1),c(a),console.log("模拟数据 - 删除WiFi码成功, ID:",t),console.log("当前WiFi列表:",a),e({code:200,data:{},message:"删除WiFi码成功"})):e({code:404,message:"未找到该WiFi码"})},300)}):Object(i["a"])({url:"/api/v1/admin/wifi/"+t,method:"delete"})}function m(t,e){return r?new Promise(a=>{setTimeout(()=>{const i=o(),r=i.findIndex(e=>e.id===parseInt(t));-1!==r?(i[r].status=e.status,c(i),console.log("模拟数据 - 更新WiFi码状态成功:",i[r]),a({code:200,data:{},message:"更新WiFi码状态成功"})):a({code:404,message:"未找到该WiFi码"})},300)}):Object(i["a"])({url:"/api/v1/admin/wifi/status/"+t,method:"put",data:e})}function v(t){return r?new Promise(e=>{setTimeout(()=>{const a=o(),i=a.length,r=a.filter(t=>1===t.status).length,s=i-r,n=a.reduce((t,e)=>t+e.use_count,0),c=[],d=new Date;let l=7;t&&t.time_range&&("month"===t.time_range?l=30:"year"===t.time_range&&(l=365));for(let t=l-1;t>=0;t--){const e=new Date(d);e.setDate(e.getDate()-t);const a=`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`;c.push({date:a,count:Math.floor(100*Math.random())+10})}const u=[...a].sort((t,e)=>e.use_count-t.use_count).slice(0,5);e({code:200,data:{stats:{total:i,active:r,inactive:s,total_use_count:n},trend_data:c,top_wifi_list:u},message:"获取WiFi统计数据成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/wifi/stats",method:"get",params:t})}},d28f:function(t,e,a){"use strict";var i=a("22b4"),r=a("ff32");Object(i["a"])(r["a"])},d58f:function(t,e,a){"use strict";var i=a("59ed"),r=a("7b0b"),s=a("44ad"),n=a("07fa"),o=TypeError,c="Reduce of empty array with no initial value",d=function(t){return function(e,a,d,l){var u=r(e),f=s(u),h=n(u);if(i(a),0===h&&d<2)throw new o(c);var p=t?h-1:0,m=t?-1:1;if(d<2)while(1){if(p in f){l=f[p],p+=m;break}if(p+=m,t?p<0:h<=p)throw new o(c)}for(;t?p>=0:h>p;p+=m)p in f&&(l=a(l,f[p],p,u));return l}};t.exports={left:d(!1),right:d(!0)}},d855:function(t,e,a){"use strict";a("6ae5")},ef97:function(t,e,a){"use strict";var i=a("22b4"),r=a("3620");Object(i["a"])(r["a"])},f665:function(t,e,a){"use strict";var i=a("23e7"),r=a("c65b"),s=a("2266"),n=a("59ed"),o=a("825a"),c=a("46c4"),d=a("2a62"),l=a("f99f"),u=l("find",TypeError);i({target:"Iterator",proto:!0,real:!0,forced:u},{find:function(t){o(this);try{n(t)}catch(i){d(this,"throw",i)}if(u)return r(u,this,t);var e=c(this),a=0;return s(e,(function(e,i){if(t(e,a++))return i(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})}}]);