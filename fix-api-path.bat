@echo off
chcp 65001 >nul
echo ===== WiFi共享管理后台API路径修复脚本 =====
echo.

echo 1. 检查当前配置...
echo 当前API配置:
type .env.development
echo.

echo 2. 重新构建前端项目...
call npm run build
if errorlevel 1 (
    echo 构建失败，请检查错误信息
    pause
    exit /b 1
)

echo 3. 重启前端服务...
pm2 restart wifi-share-admin
if errorlevel 1 (
    echo 重启失败，尝试启动服务...
    pm2 start ecosystem.config.js
)

echo 4. 显示服务状态...
pm2 status

echo.
echo ===== 修复完成 =====
echo 请访问: http://localhost:8081
echo 如果仍有问题，请检查后端服务器是否运行在4000端口
echo.
pause
