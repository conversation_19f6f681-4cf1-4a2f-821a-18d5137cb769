@echo off
chcp 65001 >nul
echo === WiFi共享管理后台 API路径修复脚本 ===
echo.

echo 1. 清理旧的构建文件...
if exist "dist" rmdir /s /q dist

echo 2. 重新构建项目...
npm run build

if %errorlevel% equ 0 (
    echo ✅ 构建成功！
) else (
    echo ❌ 构建失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 3. 检查构建结果...
if exist "dist" (
    echo ✅ dist目录已生成
    echo 📁 构建文件列表:
    dir dist
) else (
    echo ❌ dist目录未生成
    pause
    exit /b 1
)

echo.
echo === 部署说明 ===
echo 请在宝塔面板中执行以下操作：
echo.
echo 1. 将 dist/ 目录中的所有文件上传到网站根目录
echo 2. 确保Nginx配置包含以下代理设置：
echo.
echo    location /api/ {
echo        proxy_pass http://localhost:4000;
echo        proxy_set_header Host $host;
echo        proxy_set_header X-Real-IP $remote_addr;
echo        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
echo    }
echo.
echo 3. 确保后端服务在4000端口运行：
echo    pm2 status
echo    pm2 start /www/wwwroot/wifi-share-server/app.js --name wifi-share-server
echo.
echo 4. 重载Nginx配置：
echo    nginx -s reload
echo.
echo 修复完成！现在可以访问 http://**************/login 进行测试
echo.
pause
