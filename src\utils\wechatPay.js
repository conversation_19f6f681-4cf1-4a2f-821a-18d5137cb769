// 尝试导入微信支付SDK，如果失败则使用模拟模式
let Payment = null;
try {
  const wechatpaySDK = require('wechatpay-node-v3');
  Payment = wechatpaySDK.Payment;
  console.log('微信支付SDK加载成功');
} catch (err) {
  console.warn('微信支付SDK加载失败，将使用模拟支付:', err.message);
}
const fs = require('fs');
const path = require('path');

// 微信支付配置
let wechatPayConfig = {
  appid: process.env.WECHAT_APPID || 'your_wechat_appid',
  mchid: process.env.WECHAT_MCHID || 'your_merchant_id',
  // 商户API私钥路径
  private_key: process.env.WECHAT_PRIVATE_KEY_PATH || path.join(__dirname, '../config/wechat_private_key.pem'),
  // 商户证书序列号
  serial_no: process.env.WECHAT_SERIAL_NO || 'your_serial_no',
  // API v3密钥
  apiv3_private_key: process.env.WECHAT_APIV3_KEY || 'your_apiv3_key',
  // 支付回调地址
  notify_url: process.env.WECHAT_NOTIFY_URL || 'https://your-domain.com/api/v1/client/payment/wechat-notify',
  // 是否启用真实支付
  enabled: process.env.WECHAT_PAY_ENABLED === 'true' || false
};

// 尝试加载本地配置文件
try {
  const localConfig = require('../config/wechat');
  if (localConfig && localConfig.payment) {
    wechatPayConfig = {
      ...wechatPayConfig,
      appid: localConfig.payment.appId || wechatPayConfig.appid,
      mchid: localConfig.payment.mchId || wechatPayConfig.mchid,
      private_key: localConfig.payment.privateKeyPath ?
        path.join(__dirname, '../../', localConfig.payment.privateKeyPath) :
        wechatPayConfig.private_key,
      serial_no: localConfig.payment.serialNo || wechatPayConfig.serial_no,
      apiv3_private_key: localConfig.payment.apiV3Key || wechatPayConfig.apiv3_private_key,
      notify_url: localConfig.payment.notifyUrl || wechatPayConfig.notify_url,
      enabled: localConfig.payment.enabled !== undefined ? localConfig.payment.enabled : wechatPayConfig.enabled
    };
  }
} catch (err) {
  console.log('未找到本地微信配置文件，使用环境变量配置');
}

// 初始化微信支付实例
let wechatPayInstance = null;

const initWechatPay = () => {
  try {
    // 检查SDK是否可用
    if (!Payment) {
      console.log('微信支付SDK不可用，将使用模拟支付');
      return null;
    }

    // 检查是否启用真实支付
    if (!wechatPayConfig.enabled) {
      console.log('微信支付未启用，将使用模拟支付');
      return null;
    }

    // 检查私钥文件是否存在
    if (!fs.existsSync(wechatPayConfig.private_key)) {
      console.warn('微信支付私钥文件不存在，将使用模拟支付');
      console.warn('私钥文件路径:', wechatPayConfig.private_key);
      return null;
    }

    // 检查必要的配置参数
    if (!wechatPayConfig.appid || wechatPayConfig.appid === 'your_wechat_appid' ||
        !wechatPayConfig.mchid || wechatPayConfig.mchid === 'your_merchant_id' ||
        !wechatPayConfig.serial_no || wechatPayConfig.serial_no === 'your_serial_no' ||
        !wechatPayConfig.apiv3_private_key || wechatPayConfig.apiv3_private_key === 'your_apiv3_key') {
      console.warn('微信支付配置不完整，将使用模拟支付');
      console.warn('请检查配置:', {
        appid: wechatPayConfig.appid,
        mchid: wechatPayConfig.mchid,
        serial_no: wechatPayConfig.serial_no ? '已配置' : '未配置',
        apiv3_private_key: wechatPayConfig.apiv3_private_key ? '已配置' : '未配置'
      });
      return null;
    }

    wechatPayInstance = new Payment({
      appid: wechatPayConfig.appid,
      mchid: wechatPayConfig.mchid,
      private_key: fs.readFileSync(wechatPayConfig.private_key),
      serial_no: wechatPayConfig.serial_no,
      apiv3_private_key: wechatPayConfig.apiv3_private_key,
    });

    console.log('微信支付初始化成功');
    console.log('配置信息:', {
      appid: wechatPayConfig.appid,
      mchid: wechatPayConfig.mchid,
      notify_url: wechatPayConfig.notify_url
    });
    return wechatPayInstance;
  } catch (error) {
    console.error('微信支付初始化失败:', error);
    return null;
  }
};

/**
 * 创建微信小程序支付订单
 * @param {Object} orderInfo 订单信息
 * @param {string} orderInfo.orderNo 订单号
 * @param {number} orderInfo.amount 支付金额（元）
 * @param {string} orderInfo.description 商品描述
 * @param {string} orderInfo.openid 用户openid
 * @returns {Object} 支付参数
 */
const createMiniProgramPayment = async (orderInfo) => {
  try {
    const { orderNo, amount, description, openid } = orderInfo;
    
    // 如果微信支付未初始化，返回模拟支付参数
    if (!wechatPayInstance) {
      console.log('微信支付未配置，使用模拟支付');
      return createMockPayment(orderInfo);
    }

    // 创建支付订单
    const result = await wechatPayInstance.jsapi({
      description: description || '商品购买',
      out_trade_no: orderNo,
      notify_url: wechatPayConfig.notify_url,
      amount: {
        total: Math.round(amount * 100), // 转换为分
        currency: 'CNY'
      },
      payer: {
        openid: openid
      }
    });

    // 生成小程序支付参数
    const paymentParams = wechatPayInstance.buildMiniProgramPayment(result.prepay_id);
    
    return {
      ...paymentParams,
      orderId: orderInfo.orderId,
      orderNo: orderNo,
      amount: amount,
      isReal: true
    };
  } catch (error) {
    console.error('创建微信支付订单失败:', error);
    // 如果真实支付失败，降级到模拟支付
    console.log('降级到模拟支付');
    return createMockPayment(orderInfo);
  }
};

/**
 * 创建模拟支付参数
 * @param {Object} orderInfo 订单信息
 * @returns {Object} 模拟支付参数
 */
const createMockPayment = (orderInfo) => {
  const timeStamp = Math.floor(Date.now() / 1000).toString();
  const nonceStr = Math.random().toString(36).substring(2, 15);
  
  return {
    timeStamp,
    nonceStr,
    package: `prepay_id=mock_${timeStamp}`,
    signType: 'RSA',
    paySign: `mock_sign_${timeStamp}_${nonceStr}`,
    orderId: orderInfo.orderId,
    orderNo: orderInfo.orderNo,
    amount: orderInfo.amount,
    isReal: false // 标识为模拟支付
  };
};

/**
 * 验证微信支付回调签名
 * @param {Object} headers 请求头
 * @param {string} body 请求体
 * @returns {boolean} 验证结果
 */
const verifyNotifySignature = (headers, body) => {
  try {
    if (!wechatPayInstance) {
      console.log('微信支付未配置，跳过签名验证');
      return true; // 模拟支付时跳过验证
    }

    const signature = headers['wechatpay-signature'];
    const timestamp = headers['wechatpay-timestamp'];
    const nonce = headers['wechatpay-nonce'];
    const serial = headers['wechatpay-serial'];

    return wechatPayInstance.verifySignature({
      signature,
      timestamp,
      nonce,
      serial,
      body
    });
  } catch (error) {
    console.error('验证微信支付回调签名失败:', error);
    return false;
  }
};

/**
 * 查询支付订单状态
 * @param {string} orderNo 订单号
 * @returns {Object} 订单状态
 */
const queryPaymentStatus = async (orderNo) => {
  try {
    if (!wechatPayInstance) {
      // 模拟支付状态查询
      return {
        trade_state: 'SUCCESS',
        trade_state_desc: '支付成功',
        out_trade_no: orderNo,
        transaction_id: `mock_${orderNo}`,
        amount: { total: 0 }
      };
    }

    const result = await wechatPayInstance.query({
      out_trade_no: orderNo
    });

    return result;
  } catch (error) {
    console.error('查询支付状态失败:', error);
    throw error;
  }
};

/**
 * 关闭支付订单
 * @param {string} orderNo 订单号
 * @returns {boolean} 关闭结果
 */
const closePaymentOrder = async (orderNo) => {
  try {
    if (!wechatPayInstance) {
      console.log('模拟关闭支付订单:', orderNo);
      return true;
    }

    await wechatPayInstance.close({
      out_trade_no: orderNo
    });

    return true;
  } catch (error) {
    console.error('关闭支付订单失败:', error);
    return false;
  }
};

// 初始化微信支付
initWechatPay();

module.exports = {
  createMiniProgramPayment,
  verifyNotifySignature,
  queryPaymentStatus,
  closePaymentOrder,
  isWechatPayEnabled: () => !!wechatPayInstance
};
