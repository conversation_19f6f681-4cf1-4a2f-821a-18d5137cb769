# WiFi共享管理后台 - 宝塔面板部署说明

## 🚨 错误解决方案

**错误原因**: 在宝塔面板中错误地将前端项目配置为直接运行源码文件

**正确做法**: 前端项目需要先构建，然后部署静态文件或使用服务器运行构建后的文件

## ✅ 推荐部署方式

### 方式一：静态网站部署（强烈推荐）

#### 1. 本地构建
```bash
cd wifi-share-admin
npm install
npm run build:prod
```

#### 2. 宝塔面板配置
- 创建网站（选择静态网站）
- 域名：your-domain.com
- 根目录：/www/wwwroot/wifi-share-admin
- 将 dist 目录内容上传到网站根目录

#### 3. Nginx 配置
```nginx
location / {
    try_files $uri $uri/ /index.html;
}

location /api {
    proxy_pass http://localhost:4000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}
```

### 方式二：Node.js 项目部署

#### 1. 宝塔面板 Node.js 项目配置
- **项目名称**: wifi-share-admin
- **Node版本**: v18.x 或更高
- **启动文件**: server.js
- **运行目录**: /www/wwwroot/wifi-share-admin
- **端口**: 3000

#### 2. 环境变量配置
```bash
NODE_ENV=production
PORT=3000
VUE_APP_API_BASE_URL=https://your-domain.com
VUE_APP_USE_MOCK=false
```

#### 3. 部署步骤
```bash
# 1. 上传项目文件到服务器
# 2. 安装依赖
npm install

# 3. 构建项目
npm run build:prod

# 4. 启动服务
npm start
```

## 🔧 修复当前错误的步骤

### 1. 删除错误的 Node.js 项目
在宝塔面板中删除当前配置错误的 Node.js 项目

### 2. 重新配置
选择上述任一方式重新配置

### 3. 确保后端服务运行
确保 wifi-share-server 后端服务正常运行在 4000 端口

## 📝 重要注意事项

1. **不要直接运行源码**: Vue.js 项目必须先构建
2. **API 地址配置**: 确保 VUE_APP_API_BASE_URL 指向正确的后端服务
3. **端口配置**: 避免端口冲突
4. **依赖安装**: 确保所有依赖都已正确安装

## 🎯 成功标志

部署成功后，您应该能够：
- 访问管理后台登录页面
- 正常登录系统
- 各功能模块正常工作
- API 请求正常响应
