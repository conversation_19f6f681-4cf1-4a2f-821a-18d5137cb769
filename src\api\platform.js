import request from '@/utils/request'

// 获取平台概览统计
export function getPlatformOverview() {
  return request({
    url: '/api/v1/admin/platform/overview',
    method: 'get'
  })
}

// 获取收益趋势
export function getRevenueTrend(params) {
  return request({
    url: '/api/v1/admin/platform/revenue-trend',
    method: 'get',
    params
  })
}

// 获取用户增长趋势
export function getUserGrowth(params) {
  return request({
    url: '/api/v1/admin/platform/user-growth',
    method: 'get',
    params
  })
}

// 获取业务类型统计
export function getBusinessTypeStats() {
  return request({
    url: '/api/v1/admin/platform/business-type',
    method: 'get'
  })
}

// 获取地区统计
export function getRegionStats() {
  return request({
    url: '/api/v1/admin/platform/region',
    method: 'get'
  })
}

// 获取平台收益统计
export function getPlatformStats() {
  return request({
    url: '/api/v1/admin/platform/stats',
    method: 'get'
  })
}

// 获取余额变动记录
export function getBalanceLogs(params) {
  return request({
    url: '/api/v1/admin/platform/balance-logs',
    method: 'get',
    params
  })
}

// 刷新团队业绩缓存
export function refreshTeamCache() {
  return request({
    url: '/api/v1/admin/platform/refresh-team-cache',
    method: 'post'
  })
}
