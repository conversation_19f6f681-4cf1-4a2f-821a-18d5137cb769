module.exports = {
  apps: [{
    name: 'wifi-share-admin',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 8081
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 8081
    },
    env_development: {
      NODE_ENV: 'development',
      PORT: 8081
    },
    // 日志配置
    log_file: './logs/wifi-share-admin.log',
    out_file: './logs/wifi-share-admin-out.log',
    error_file: './logs/wifi-share-admin-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    
    // 进程管理
    min_uptime: '10s',
    max_restarts: 10,
    
    // 监控
    monitoring: false,
    
    // 其他配置
    node_args: '--max-old-space-size=1024'
  }]
};
