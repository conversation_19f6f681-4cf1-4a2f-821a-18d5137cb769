(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7caaaced"],{"08d7":function(t,e,a){},"13d5":function(t,e,a){"use strict";var i=a("23e7"),s=a("d58f").left,n=a("a640"),r=a("1212"),o=a("9adc"),c=!o&&r>79&&r<83,d=c||!n("reduce");i({target:"Array",proto:!0,forced:d},{reduce:function(t){var e=arguments.length;return s(this,t,e,e>1?arguments[1]:void 0)}})},2047:function(t,e,a){"use strict";a("08d7")},6877:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"wifi-detail"},[e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("WiFi码详情")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.goBack}},[t._v("返回")])],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"detail-label"},[t._v("ID:")]),e("span",{staticClass:"detail-value"},[t._v(t._s(t.detail.id))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"detail-label"},[t._v("标题:")]),e("span",{staticClass:"detail-value"},[t._v(t._s(t.detail.title))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"detail-label"},[t._v("WiFi名称:")]),e("span",{staticClass:"detail-value"},[t._v(t._s(t.detail.name))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"detail-label"},[t._v("WiFi密码:")]),e("span",{staticClass:"detail-value"},[t._v(t._s(t.detail.password))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"detail-label"},[t._v("商户名称:")]),e("span",{staticClass:"detail-value"},[t._v(t._s(t.detail.merchant_name))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"detail-label"},[t._v("使用次数:")]),e("span",{staticClass:"detail-value"},[t._v(t._s(t.detail.use_count))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"detail-label"},[t._v("状态:")]),e("span",{staticClass:"detail-value"},[e("el-tag",{attrs:{type:1===t.detail.status?"success":"info"}},[t._v(" "+t._s(1===t.detail.status?"启用":"禁用")+" ")])],1)])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"detail-label"},[t._v("创建时间:")]),e("span",{staticClass:"detail-value"},[t._v(t._s(t.detail.created_at))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("span",{staticClass:"detail-label"},[t._v("更新时间:")]),e("span",{staticClass:"detail-value"},[t._v(t._s(t.detail.updated_at))])])])],1),e("div",{staticClass:"qrcode-container"},[e("div",{staticClass:"detail-label"},[t._v("WiFi二维码:")]),e("div",{staticClass:"qrcode-wrapper"},[e("qrcode-generator",{ref:"qrcodeGenerator",attrs:{"wifi-info":t.wifiInfo,width:200,"file-name":"wifi-qrcode-"+t.detail.id,title:t.detail.title||"WiFi二维码","print-info":{merchant:t.detail.merchant_name}},on:{generated:t.onQrcodeGenerated}})],1),t.qrcodeGenerationFailed?e("div",{staticClass:"qrcode-error"},[e("el-alert",{attrs:{title:"二维码生成失败",type:"error",description:"服务器未返回二维码数据，已自动为您生成新的二维码","show-icon":"",closable:!1}})],1):t._e(),t.qrcodeDataUrl?e("div",{staticClass:"qrcode-info"},[e("p",[t._v("此二维码可用于自动连接WiFi，无需手动输入密码")]),e("p",[t._v("用户只需用手机相机扫描二维码，即可快速连接网络")])]):t._e()])],1),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("操作")])]),e("div",{staticClass:"action-buttons"},[e("el-button",{attrs:{type:"primary"},on:{click:t.handleEdit}},[t._v("编辑")]),1===t.detail.status?e("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.handleStatusChange(0)}}},[t._v("禁用")]):e("el-button",{attrs:{type:"success"},on:{click:function(e){return t.handleStatusChange(1)}}},[t._v("启用")]),e("el-button",{attrs:{type:"danger"},on:{click:t.handleDelete}},[t._v("删除")])],1)])],1)])},s=[],n=(a("14d9"),a("d251")),r=a("87b4"),o={name:"WiFiDetail",components:{QrcodeGenerator:r["a"]},data(){return{loading:!0,detail:{},wifiId:null,qrcodeError:!1,qrcodeDataUrl:"",qrcodeGenerationFailed:!1}},computed:{wifiInfo(){return this.detail&&this.detail.name&&this.detail.password?{ssid:this.detail.name,password:this.detail.password,hidden:!1}:null}},created(){const t=this.$route.params.id;if(!t||isNaN(t))return this.$message.error("无效的WiFi ID"),void this.goBack();this.wifiId=parseInt(t),this.fetchData()},methods:{fetchData(){if(!this.wifiId||isNaN(this.wifiId))return this.$message.error("无效的WiFi ID"),void this.goBack();this.loading=!0,this.qrcodeError=!1,this.qrcodeGenerationFailed=!1,Object(n["c"])(this.wifiId).then(t=>{console.log("WiFi详情API返回数据:",t),t?("success"===t.status&&t.data||200===t.code&&t.data?this.detail=t.data:(console.error("WiFi详情数据结构异常:",t),this.$message.warning("获取WiFi详情数据格式错误，尝试使用本地数据"),this.createDefaultData()),console.log("最终设置的WiFi详情数据:",this.detail)):(console.error("WiFi详情返回空数据"),this.$message.warning("获取WiFi详情失败，使用本地数据"),this.createDefaultData()),this.detail.qrcode||(console.log("服务器未返回二维码数据，将自动生成"),this.qrcodeGenerationFailed=!0)}).catch(t=>{console.error("获取WiFi详情失败:",t),this.$message.warning("获取WiFi详情失败，使用本地数据"),this.createDefaultData()}).finally(()=>{this.loading=!1})},createDefaultData(){this.detail={id:this.wifiId,title:"WiFi-"+this.wifiId,name:"WiFi-SSID",password:"12345678",merchant_name:"默认商户",qrcode:"",use_count:0,user_id:0,status:1,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},this.qrcodeGenerationFailed=!0},onQrcodeGenerated(t){this.qrcodeDataUrl=t,console.log("二维码生成成功，数据长度:",t.length),this.qrcodeGenerationFailed&&this.detail.id&&console.log("可以将生成的二维码上传到服务器")},handleEdit(){this.$router.push("/wifi/edit/"+this.wifiId)},handleStatusChange(t){Object(n["g"])(this.wifiId,{status:t}).then(e=>{this.$message.success("状态更新成功"),this.detail.status=t}).catch(()=>{this.$message.error("状态更新失败")})},handleDelete(){this.$confirm("确认要删除这个WiFi码吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(n["b"])(this.wifiId).then(t=>{this.$message.success("删除成功"),this.goBack()}).catch(()=>{this.$message.error("删除失败")})}).catch(()=>{})},goBack(){this.$router.push("/wifi/list")}}},c=o,d=(a("2047"),a("2877")),l=Object(d["a"])(c,i,s,!1,null,"6a4461ac",null);e["default"]=l.exports},8558:function(t,e,a){"use strict";var i=a("cfe9"),s=a("b5db"),n=a("c6b6"),r=function(t){return s.slice(0,t.length)===t};t.exports=function(){return r("Bun/")?"BUN":r("Cloudflare-Workers")?"CLOUDFLARE":r("Deno/")?"DENO":r("Node.js/")?"NODE":i.Bun&&"string"==typeof Bun.version?"BUN":i.Deno&&"object"==typeof Deno.version?"DENO":"process"===n(i.process)?"NODE":i.window&&i.document?"BROWSER":"REST"}()},9485:function(t,e,a){"use strict";var i=a("23e7"),s=a("2266"),n=a("59ed"),r=a("825a"),o=a("46c4"),c=a("2a62"),d=a("f99f"),l=a("2ba4"),u=a("d039"),f=TypeError,h=u((function(){[].keys().reduce((function(){}),void 0)})),p=!h&&d("reduce",f);i({target:"Iterator",proto:!0,real:!0,forced:h||p},{reduce:function(t){r(this);try{n(t)}catch(u){c(this,"throw",u)}var e=arguments.length<2,a=e?void 0:arguments[1];if(p)return l(p,this,e?[t]:[t,a]);var i=o(this),d=0;if(s(i,(function(i){e?(e=!1,a=i):a=t(a,i,d),d++}),{IS_RECORD:!0}),e)throw new f("Reduce of empty iterator with no initial value");return a}})},"9adc":function(t,e,a){"use strict";var i=a("8558");t.exports="NODE"===i},a640:function(t,e,a){"use strict";var i=a("d039");t.exports=function(t,e){var a=[][t];return!!a&&i((function(){a.call(null,e||function(){return 1},1)}))}},ab43:function(t,e,a){"use strict";var i=a("23e7"),s=a("c65b"),n=a("59ed"),r=a("825a"),o=a("46c4"),c=a("c5cc"),d=a("9bdd"),l=a("2a62"),u=a("2baa"),f=a("f99f"),h=a("c430"),p=!h&&!u("map",(function(){})),m=!h&&!p&&f("map",TypeError),g=h||p||m,v=c((function(){var t=this.iterator,e=r(s(this.next,t)),a=this.done=!!e.done;if(!a)return d(t,this.mapper,[e.value,this.counter++],!0)}));i({target:"Iterator",proto:!0,real:!0,forced:g},{map:function(t){r(this);try{n(t)}catch(e){l(this,"throw",e)}return m?s(m,this,t):new v(o(this),{mapper:t})}})},d251:function(t,e,a){"use strict";a.d(e,"d",(function(){return l})),a.d(e,"c",(function(){return u})),a.d(e,"a",(function(){return f})),a.d(e,"f",(function(){return h})),a.d(e,"b",(function(){return p})),a.d(e,"g",(function(){return m})),a.d(e,"e",(function(){return g}));a("14d9"),a("13d5"),a("e9f5"),a("910d"),a("f665"),a("ab43"),a("9485");var i=a("b775");const s=!1,n="wifi_admin_wifi_list",r=[{id:1,title:"WiFi测试1",name:"Test WiFi",password:"12345678",merchant_name:"测试商家1",qrcode:"",use_count:123,user_id:1,status:1,created_at:"2023-06-01 12:30:45"},{id:2,title:"WiFi测试2",name:"Office WiFi",password:"87654321",merchant_name:"测试商家2",qrcode:"",use_count:456,user_id:2,status:1,created_at:"2023-06-02 10:20:30"}];function o(){try{const t=localStorage.getItem(n);return t?JSON.parse(t):r}catch(t){return console.warn("读取WiFi数据失败，使用默认数据:",t),r}}function c(t){try{localStorage.setItem(n,JSON.stringify(t))}catch(e){console.error("保存WiFi数据失败:",e)}}function d(){const t=new Date,e=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),i=String(t.getDate()).padStart(2,"0"),s=String(t.getHours()).padStart(2,"0"),n=String(t.getMinutes()).padStart(2,"0"),r=String(t.getSeconds()).padStart(2,"0");return`${e}-${a}-${i} ${s}:${n}:${r}`}function l(t){return s?new Promise(e=>{setTimeout(()=>{const a=o(),i=parseInt(t.page)||1,s=parseInt(t.limit)||10,n=(i-1)*s,r=n+s;let c=a;if(t.keyword){const e=t.keyword.toLowerCase();c=a.filter(t=>t.title.toLowerCase().includes(e)||t.name.toLowerCase().includes(e)||t.merchant_name.toLowerCase().includes(e))}void 0!==t.status&&""!==t.status&&(c=c.filter(e=>e.status===parseInt(t.status)));const d=c.slice(n,r);e({code:200,data:{list:d,total:c.length},message:"获取WiFi列表成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/wifi/list",method:"get",params:t})}function u(t){return s?new Promise(e=>{setTimeout(()=>{const a=o(),i=a.find(e=>e.id===parseInt(t));e(i?{code:200,data:i,message:"获取WiFi详情成功"}:{code:404,message:"未找到该WiFi码"})},300)}):(console.log("请求WiFi详情, ID:",t),Object(i["a"])({url:"/api/v1/admin/wifi/detail/"+t,method:"get"}))}function f(t){return s?new Promise(e=>{setTimeout(()=>{const a=o(),i=a.length>0?Math.max(...a.map(t=>t.id))+1:1,s={id:i,title:t.title,name:t.name,password:t.password,merchant_name:t.merchant_name,qrcode:"",use_count:0,user_id:1,status:t.status,created_at:d()};a.push(s),c(a),console.log("模拟数据 - 创建WiFi码成功:",s),console.log("当前WiFi列表:",a),e({code:200,data:{id:i},message:"创建WiFi码成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/wifi/create",method:"post",data:t})}function h(t,e){return s?new Promise(a=>{setTimeout(()=>{const i=o(),s=i.findIndex(e=>e.id===parseInt(t));-1!==s?(i[s]={...i[s],...e},c(i),console.log("模拟数据 - 更新WiFi码成功:",i[s]),a({code:200,data:{},message:"更新WiFi码成功"})):a({code:404,message:"未找到该WiFi码"})},300)}):Object(i["a"])({url:"/api/v1/admin/wifi/update/"+t,method:"put",data:e})}function p(t){return s?new Promise(e=>{setTimeout(()=>{const a=o(),i=a.findIndex(e=>e.id===parseInt(t));-1!==i?(a.splice(i,1),c(a),console.log("模拟数据 - 删除WiFi码成功, ID:",t),console.log("当前WiFi列表:",a),e({code:200,data:{},message:"删除WiFi码成功"})):e({code:404,message:"未找到该WiFi码"})},300)}):Object(i["a"])({url:"/api/v1/admin/wifi/"+t,method:"delete"})}function m(t,e){return s?new Promise(a=>{setTimeout(()=>{const i=o(),s=i.findIndex(e=>e.id===parseInt(t));-1!==s?(i[s].status=e.status,c(i),console.log("模拟数据 - 更新WiFi码状态成功:",i[s]),a({code:200,data:{},message:"更新WiFi码状态成功"})):a({code:404,message:"未找到该WiFi码"})},300)}):Object(i["a"])({url:"/api/v1/admin/wifi/status/"+t,method:"put",data:e})}function g(t){return s?new Promise(e=>{setTimeout(()=>{const a=o(),i=a.length,s=a.filter(t=>1===t.status).length,n=i-s,r=a.reduce((t,e)=>t+e.use_count,0),c=[],d=new Date;let l=7;t&&t.time_range&&("month"===t.time_range?l=30:"year"===t.time_range&&(l=365));for(let t=l-1;t>=0;t--){const e=new Date(d);e.setDate(e.getDate()-t);const a=`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`;c.push({date:a,count:Math.floor(100*Math.random())+10})}const u=[...a].sort((t,e)=>e.use_count-t.use_count).slice(0,5);e({code:200,data:{stats:{total:i,active:s,inactive:n,total_use_count:r},trend_data:c,top_wifi_list:u},message:"获取WiFi统计数据成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/wifi/stats",method:"get",params:t})}},d58f:function(t,e,a){"use strict";var i=a("59ed"),s=a("7b0b"),n=a("44ad"),r=a("07fa"),o=TypeError,c="Reduce of empty array with no initial value",d=function(t){return function(e,a,d,l){var u=s(e),f=n(u),h=r(u);if(i(a),0===h&&d<2)throw new o(c);var p=t?h-1:0,m=t?-1:1;if(d<2)while(1){if(p in f){l=f[p],p+=m;break}if(p+=m,t?p<0:h<=p)throw new o(c)}for(;t?p>=0:h>p;p+=m)p in f&&(l=a(l,f[p],p,u));return l}};t.exports={left:d(!1),right:d(!0)}},f665:function(t,e,a){"use strict";var i=a("23e7"),s=a("c65b"),n=a("2266"),r=a("59ed"),o=a("825a"),c=a("46c4"),d=a("2a62"),l=a("f99f"),u=l("find",TypeError);i({target:"Iterator",proto:!0,real:!0,forced:u},{find:function(t){o(this);try{r(t)}catch(i){d(this,"throw",i)}if(u)return s(u,this,t);var e=c(this),a=0;return n(e,(function(e,i){if(t(e,a++))return i(e)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})}}]);