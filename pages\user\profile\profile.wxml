<!--pages/user/profile/profile.wxml-->
<view class="profile-container">
  <!-- 顶部广告区域 -->
  <view class="ad-banner" bindtap="onAdClick">
    <image class="ad-image" src="/assets/images/user-ad-banner.jpg" mode="aspectFill"></image>
    <view class="ad-overlay">
      <text class="ad-text">广告区域</text>
    </view>
  </view>

  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-info">
      <view class="user-left" bindtap="{{!isLoggedIn ? 'onLogin' : ''}}">
        <!-- 已登录且未完善资料时显示头像选择按钮 -->
        <block wx:if="{{isLoggedIn && (!isProfileCompleted || isDemote)}}">
          <button class="avatar-button" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
            <image class="user-avatar" src="{{userInfo.avatar || 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132'}}" mode="aspectFill" binderror="onAvatarError"></image>
            <view class="avatar-edit-icon">编辑</view>
          </button>
        </block>
        <!-- 已登录且已完善资料时显示普通头像 -->
        <block wx:elif="{{isLoggedIn}}">
          <image class="user-avatar" src="{{userInfo.avatar || 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132'}}" mode="aspectFill" binderror="onAvatarError"></image>
        </block>
        <!-- 未登录时显示默认头像 -->
        <block wx:else>
          <image class="user-avatar" src="/assets/icons/user-placeholder.png" mode="aspectFill"></image>
        </block>
        
        <view class="user-detail">
          <!-- 已登录且未完善资料时显示昵称输入框 -->
          <block wx:if="{{isLoggedIn && (!isProfileCompleted || isDemote)}}">
            <input class="nickname-input" type="nickname" placeholder="请输入昵称" value="{{userInfo.nickname !== '微信用户' ? userInfo.nickname : ''}}" bindchange="onInputNickname" />
          </block>
          <!-- 已登录且已完善资料时显示昵称 -->
          <block wx:elif="{{isLoggedIn}}">
            <text class="user-nickname">{{userInfo.nickname || '微信用户'}}</text>
            <text class="user-id" wx:if="{{isLoggedIn}}">ID: {{userInfo.id || 'xxxxxxxx'}}</text>
          </block>
          <!-- 未登录时显示提示文字 -->
          <block wx:else>
            <text class="user-nickname">点击登录</text>
          </block>
        </view>
      </view>
      <view class="user-right">
        <button class="logout-btn" bindtap="{{isLoggedIn ? 'onLogout' : 'onLogin'}}">
          {{isLoggedIn ? '退出' : '登录'}}
        </button>
        <!-- 已登录且未完善资料时显示保存按钮 -->
        <button wx:if="{{isLoggedIn && (!isProfileCompleted || isDemote)}}" class="save-btn" bindtap="onSaveUserInfo">
          保存
        </button>
      </view>
    </view>
  </view>

  <!-- 我的订单区域 -->
  <view class="order-section">
    <view class="section-header" bindtap="onViewAllOrders">
      <text class="section-title">我的订单</text>
      <view class="section-action">
        <text class="action-text">全部订单</text>
        <text class="arrow-icon">></text>
      </view>
    </view>
    <view class="order-status-list">
      <view class="order-status-item" bindtap="onViewOrdersByStatus" data-status="1">
        <view class="status-icon-wrapper">
          <text class="status-icon">💰</text>
          <text class="status-badge" wx:if="{{orderStats.pending > 0}}">{{orderStats.pending}}</text>
        </view>
        <text class="status-text">待付款</text>
      </view>
      <view class="order-status-item" bindtap="onViewOrdersByStatus" data-status="2">
        <view class="status-icon-wrapper">
          <text class="status-icon">📦</text>
          <text class="status-badge" wx:if="{{orderStats.shipped > 0}}">{{orderStats.shipped}}</text>
        </view>
        <text class="status-text">待发货</text>
      </view>
      <view class="order-status-item" bindtap="onViewOrdersByStatus" data-status="3">
        <view class="status-icon-wrapper">
          <text class="status-icon">🚚</text>
          <text class="status-badge" wx:if="{{orderStats.delivering > 0}}">{{orderStats.delivering}}</text>
        </view>
        <text class="status-text">待收货</text>
      </view>
      <view class="order-status-item" bindtap="onViewOrdersByStatus" data-status="4">
        <view class="status-icon-wrapper">
          <text class="status-icon">✅</text>
        </view>
        <text class="status-text">已完成</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单区域 -->
  <view class="menu-section">
    <!-- 第一行：我的钱包 | 我的团队 -->
    <view class="menu-row">
      <view class="menu-item" bindtap="onNavigateToWallet">
        <text class="menu-icon">💳</text>
        <text class="menu-text">我的钱包</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item" bindtap="onNavigateToTeam">
        <text class="menu-icon">👥</text>
        <text class="menu-text">我的团队</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 第二行：我的客服 | 帮助中心 -->
    <view class="menu-row">
      <view class="menu-item">
        <button class="contact-button" open-type="contact" bindcontact="onContactService">
        <text class="menu-icon">🎧</text>
        <text class="menu-text">我的客服</text>
        <text class="menu-arrow">></text>
        </button>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item" bindtap="onNavigateToHelp">
        <text class="menu-icon">❓</text>
        <text class="menu-text">帮助中心</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 第三行：消息中心 | 邀请好友 -->
    <view class="menu-row">
      <view class="menu-item" bindtap="onNavigateToMessages">
        <text class="menu-icon">📧</text>
        <text class="menu-text">消息中心</text>
        <text class="menu-badge" wx:if="{{messageCount > 0}}">{{messageCount}}</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item" bindtap="onInviteFriends">
        <text class="menu-icon">🎁</text>
        <text class="menu-text">邀请好友</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 第四行：收货地址管理 -->
    <view class="menu-single-row">
      <view class="menu-item-full" bindtap="onNavigateToAddress">
        <text class="menu-icon">📍</text>
        <text class="menu-text">收货地址管理</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 腾讯广告区域 -->
    <view class="ad-container">
      <ad unit-id="{{adUnitId}}" ad-type="banner" ad-theme="white" bindload="onAdLoad" binderror="onAdError" bindtap="onAdClick"></ad>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{!isLoggedIn && !loading}}">
    <text class="empty-text">请先登录查看更多信息</text>
    <button class="login-btn" bindtap="onLogin">立即登录</button>
  </view>
</view> 