(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9d1b964c"],{"10ec":function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t._self._c;return a("div",{staticClass:"app-container"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"withdraw-detail"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("提现申请详情")]),a("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.goBack}},[t._v("返回")])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("div",{staticClass:"info-section"},[a("h3",{staticClass:"section-title"},[t._v("基本信息")]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("提现ID：")]),a("span",{staticClass:"value"},[t._v(t._s(t.detail.id))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("申请时间：")]),a("span",{staticClass:"value"},[t._v(t._s(t.detail.created_at))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("状态：")]),a("span",{staticClass:"value"},[a("el-tag",{attrs:{type:t.getStatusType(t.detail.status)}},[t._v(t._s(t.getStatusLabel(t.detail.status)))])],1)]),0!==t.detail.status?a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("审核时间：")]),a("span",{staticClass:"value"},[t._v(t._s(t.detail.audit_time||"-"))])]):t._e(),3===t.detail.status?a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("打款时间：")]),a("span",{staticClass:"value"},[t._v(t._s(t.detail.transfer_time||"-"))])]):t._e(),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("备注：")]),a("span",{staticClass:"value"},[t._v(t._s(t.detail.remark||"-"))])])]),a("div",{staticClass:"info-section"},[a("h3",{staticClass:"section-title"},[t._v("提现信息")]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("提现金额：")]),a("span",{staticClass:"value withdraw-amount"},[t._v(t._s(t.detail.amount)+" 元")])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("手续费：")]),a("span",{staticClass:"value"},[t._v(t._s(t.detail.fee||0)+" 元")])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("实际到账：")]),a("span",{staticClass:"value actual-amount"},[t._v(t._s((t.detail.amount-(t.detail.fee||0)).toFixed(2))+" 元")])]),3===t.detail.status?a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("流水号：")]),a("span",{staticClass:"value"},[t._v(t._s(t.detail.transaction_id))])]):t._e()])]),a("el-col",{attrs:{span:12}},[a("div",{staticClass:"info-section"},[a("h3",{staticClass:"section-title"},[t._v("用户信息")]),a("div",{staticClass:"user-header"},[a("el-avatar",{attrs:{size:64,src:t.userInfo.avatar}},[a("img",{attrs:{src:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"}})]),a("h4",{staticClass:"nickname"},[t._v(t._s(t.userInfo.nickname))]),a("div",{staticClass:"user-role"},[1===t.userInfo.is_leader?a("el-tag",{attrs:{type:"warning"}},[t._v("团长")]):a("el-tag",{attrs:{type:"info"}},[t._v("成员")])],1)],1),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("用户ID：")]),a("span",{staticClass:"value"},[t._v(t._s(t.userInfo.id))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("手机号：")]),a("span",{staticClass:"value"},[t._v(t._s(t.userInfo.phone||"-"))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("当前余额：")]),a("span",{staticClass:"value"},[t._v(t._s(t.userInfo.balance||0)+" 元")])]),a("div",{staticClass:"info-item"},[a("el-button",{attrs:{type:"text"},on:{click:t.viewUser}},[t._v("查看用户详情")])],1)]),a("div",{staticClass:"info-section"},[a("h3",{staticClass:"section-title"},[t._v("银行卡信息")]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("持卡人：")]),a("span",{staticClass:"value"},[t._v(t._s(t.detail.card_holder))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("银行名称：")]),a("span",{staticClass:"value"},[t._v(t._s(t.detail.bank_name))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("卡号：")]),a("span",{staticClass:"value card-number"},[t._v(t._s(t.detail.card_number))])]),a("div",{staticClass:"info-item"},[a("span",{staticClass:"label"},[t._v("开户行：")]),a("span",{staticClass:"value"},[t._v(t._s(t.detail.bank_branch||"-"))])])])])],1),0===t.detail.status||1===t.detail.status?a("div",{staticClass:"action-buttons"},[0===t.detail.status?a("el-button",{attrs:{type:"success"},on:{click:function(a){return t.handleAudit(1)}}},[t._v("通过审核")]):t._e(),0===t.detail.status?a("el-button",{attrs:{type:"danger"},on:{click:function(a){return t.handleAudit(2)}}},[t._v("拒绝申请")]):t._e(),1===t.detail.status?a("el-button",{attrs:{type:"warning"},on:{click:t.handleConfirm}},[t._v("确认打款")]):t._e()],1):t._e()],1)],1),a("el-dialog",{attrs:{title:1===t.auditForm.status?"审核通过":"审核拒绝",visible:t.auditDialogVisible,width:"500px"},on:{"update:visible":function(a){t.auditDialogVisible=a}}},[a("el-form",{ref:"auditForm",attrs:{model:t.auditForm,rules:t.auditRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:1===t.auditForm.status?"请输入审核通过备注（选填）":"请输入拒绝原因（必填）"},model:{value:t.auditForm.remark,callback:function(a){t.$set(t.auditForm,"remark",a)},expression:"auditForm.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.auditDialogVisible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.submitAudit}},[t._v("确定")])],1)],1),a("el-dialog",{attrs:{title:"确认打款",visible:t.confirmDialogVisible,width:"500px"},on:{"update:visible":function(a){t.confirmDialogVisible=a}}},[a("el-form",{ref:"confirmForm",attrs:{model:t.confirmForm,rules:t.confirmRules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"转账流水号",prop:"transaction_id"}},[a("el-input",{attrs:{placeholder:"请输入转账流水号"},model:{value:t.confirmForm.transaction_id,callback:function(a){t.$set(t.confirmForm,"transaction_id",a)},expression:"confirmForm.transaction_id"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入备注（选填）"},model:{value:t.confirmForm.remark,callback:function(a){t.$set(t.confirmForm,"remark",a)},expression:"confirmForm.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(a){t.confirmDialogVisible=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:t.submitConfirm}},[t._v("确认打款")])],1)],1)],1)},i=[],r=(e("d9e2"),e("14d9"),e("f851")),n={name:"WithdrawDetail",data(){const t=(t,a,e)=>{2!==this.auditForm.status||a&&""!==a.trim()?e():e(new Error("拒绝时，请输入拒绝原因"))};return{loading:!0,withdrawId:null,detail:{},userInfo:{},auditDialogVisible:!1,auditForm:{status:1,remark:""},auditRules:{remark:[{validator:t,trigger:"blur"}]},confirmDialogVisible:!1,confirmForm:{transaction_id:"",remark:""},confirmRules:{transaction_id:[{required:!0,message:"请输入转账流水号",trigger:"blur"},{min:5,message:"流水号长度不能少于5个字符",trigger:"blur"}]}}},created(){this.withdrawId=parseInt(this.$route.params.id),this.fetchData()},methods:{fetchData(){this.loading=!0,Object(r["f"])(this.withdrawId).then(t=>{const{detail:a,user_info:e}=t.data;this.detail=a,this.userInfo=e,this.loading=!1}).catch(()=>{this.loading=!1})},getStatusLabel(t){const a={0:"待审核",1:"已通过",2:"已拒绝",3:"已打款"};return a[t]||"未知状态"},getStatusType(t){const a={0:"info",1:"primary",2:"danger",3:"success"};return a[t]||"info"},goBack(){this.$router.push("/profit/withdraw")},viewUser(){this.$router.push("/user/detail/"+this.userInfo.id)},handleAudit(t){this.auditForm={status:t,remark:""},this.auditDialogVisible=!0},submitAudit(){this.$refs.auditForm.validate(t=>{t&&Object(r["a"])(this.withdrawId,{status:this.auditForm.status,remark:this.auditForm.remark}).then(t=>{this.$message.success(1===this.auditForm.status?"审核通过成功":"审核拒绝成功"),this.auditDialogVisible=!1,this.fetchData()}).catch(()=>{this.$message.error("审核操作失败")})})},handleConfirm(){this.confirmForm={transaction_id:"",remark:""},this.confirmDialogVisible=!0},submitConfirm(){this.$refs.confirmForm.validate(t=>{t&&Object(r["b"])(this.withdrawId,{transaction_id:this.confirmForm.transaction_id,remark:this.confirmForm.remark}).then(t=>{this.$message.success("确认打款成功"),this.confirmDialogVisible=!1,this.fetchData()}).catch(()=>{this.$message.error("确认打款失败")})})}}},o=n,l=(e("ac66"),e("2877")),u=Object(l["a"])(o,s,i,!1,null,"20cf5e77",null);a["default"]=u.exports},"13d5":function(t,a,e){"use strict";var s=e("23e7"),i=e("d58f").left,r=e("a640"),n=e("1212"),o=e("9adc"),l=!o&&n>79&&n<83,u=l||!r("reduce");s({target:"Array",proto:!0,forced:u},{reduce:function(t){var a=arguments.length;return i(this,t,a,a>1?arguments[1]:void 0)}})},8558:function(t,a,e){"use strict";var s=e("cfe9"),i=e("b5db"),r=e("c6b6"),n=function(t){return i.slice(0,t.length)===t};t.exports=function(){return n("Bun/")?"BUN":n("Cloudflare-Workers")?"CLOUDFLARE":n("Deno/")?"DENO":n("Node.js/")?"NODE":s.Bun&&"string"==typeof Bun.version?"BUN":s.Deno&&"object"==typeof Deno.version?"DENO":"process"===r(s.process)?"NODE":s.window&&s.document?"BROWSER":"REST"}()},9485:function(t,a,e){"use strict";var s=e("23e7"),i=e("2266"),r=e("59ed"),n=e("825a"),o=e("46c4"),l=e("2a62"),u=e("f99f"),c=e("2ba4"),d=e("d039"),m=TypeError,_=d((function(){[].keys().reduce((function(){}),void 0)})),f=!_&&u("reduce",m);s({target:"Iterator",proto:!0,real:!0,forced:_||f},{reduce:function(t){n(this);try{r(t)}catch(d){l(this,"throw",d)}var a=arguments.length<2,e=a?void 0:arguments[1];if(f)return c(f,this,a?[t]:[t,e]);var s=o(this),u=0;if(i(s,(function(s){a?(a=!1,e=s):e=t(e,s,u),u++}),{IS_RECORD:!0}),a)throw new m("Reduce of empty iterator with no initial value");return e}})},"9adc":function(t,a,e){"use strict";var s=e("8558");t.exports="NODE"===s},a640:function(t,a,e){"use strict";var s=e("d039");t.exports=function(t,a){var e=[][t];return!!e&&s((function(){e.call(null,a||function(){return 1},1)}))}},ac66:function(t,a,e){"use strict";e("c813")},c813:function(t,a,e){},d58f:function(t,a,e){"use strict";var s=e("59ed"),i=e("7b0b"),r=e("44ad"),n=e("07fa"),o=TypeError,l="Reduce of empty array with no initial value",u=function(t){return function(a,e,u,c){var d=i(a),m=r(d),_=n(d);if(s(e),0===_&&u<2)throw new o(l);var f=t?_-1:0,p=t?-1:1;if(u<2)while(1){if(f in m){c=m[f],f+=p;break}if(f+=p,t?f<0:_<=f)throw new o(l)}for(;t?f>=0:_>f;f+=p)f in m&&(c=e(c,m[f],f,d));return c}};t.exports={left:u(!1),right:u(!0)}},f665:function(t,a,e){"use strict";var s=e("23e7"),i=e("c65b"),r=e("2266"),n=e("59ed"),o=e("825a"),l=e("46c4"),u=e("2a62"),c=e("f99f"),d=c("find",TypeError);s({target:"Iterator",proto:!0,real:!0,forced:d},{find:function(t){o(this);try{n(t)}catch(s){u(this,"throw",s)}if(d)return i(d,this,t);var a=l(this),e=0;return r(a,(function(a,s){if(t(a,e++))return s(a)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})},f851:function(t,a,e){"use strict";e.d(a,"e",(function(){return h})),e.d(a,"h",(function(){return v})),e.d(a,"d",(function(){return b})),e.d(a,"c",(function(){return w})),e.d(a,"g",(function(){return g})),e.d(a,"f",(function(){return k})),e.d(a,"a",(function(){return y})),e.d(a,"b",(function(){return C}));e("d9e2"),e("13d5"),e("e9f5"),e("910d"),e("f665"),e("9485");var s=e("b775");const i=!1,r="wifi_admin_profit_rules",n="wifi_admin_profit_bills",o="wifi_admin_withdraw_list",l={wifi_share:{name:"WiFi分享",user_rate:70,platform_rate:30,status:1},goods_sale:{name:"商品销售",user_rate:10,leader_rate:5,platform_rate:85,status:1},advertisement:{name:"广告点击",user_rate:20,leader_rate:10,platform_rate:70,status:1},updated_at:"2025-07-08 16:30:00"};function u(){try{const t=localStorage.getItem(r);return t?JSON.parse(t):l}catch(t){return console.warn("读取分润规则数据失败，使用默认数据:",t),l}}function c(t){try{localStorage.setItem(r,JSON.stringify(t))}catch(a){console.error("保存分润规则数据失败:",a)}}const d=[{id:101,bill_no:"SB20250708001",type:"wifi_share",amount:.6,share_amount:.42,platform_amount:.18,user_id:1001,user_name:"张三",user_phone:"138****1001",source_id:1,source_type:"wifi",status:1,remark:"WiFi码分享使用收益",settle_time:null,created_at:"2025-07-08 10:15:22"},{id:102,bill_no:"SB20250708002",type:"wifi_share",amount:.4,share_amount:.28,platform_amount:.12,user_id:1002,user_name:"李四",user_phone:"138****1002",source_id:2,source_type:"wifi",status:2,remark:"WiFi码分享使用收益",settle_time:"2025-07-08 16:32:45",created_at:"2025-07-08 09:27:18"},{id:103,bill_no:"SB20250708003",type:"goods_sale",amount:25,share_amount:2.5,platform_amount:22.5,user_id:1003,user_name:"王五",user_phone:"138****1003",source_id:1,source_type:"order",status:1,remark:"商品销售分润",settle_time:null,created_at:"2025-07-08 14:52:36"},{id:104,bill_no:"SB20250708004",type:"advertisement",amount:5.6,share_amount:1.12,platform_amount:4.48,user_id:1001,user_name:"张三",user_phone:"138****1001",source_id:3,source_type:"ad_click",status:2,remark:"广告点击收益",settle_time:"2025-07-08 18:21:05",created_at:"2025-07-08 16:05:43"}];function m(){try{const t=localStorage.getItem(n);return t?JSON.parse(t):d}catch(t){return console.warn("读取账单数据失败，使用默认数据:",t),d}}const _=[{id:1,withdraw_no:"W2025070800001",user_id:1001,user_name:"张三",user_phone:"138****1001",amount:200,status:0,type:1,account_type:"支付宝",account_name:"张三",account_no:"<EMAIL>",bank_name:null,created_at:"2025-07-08 10:12:33",audit_time:null,audit_user:null,audit_remark:null,pay_time:null,pay_remark:null},{id:2,withdraw_no:"W2025070800002",user_id:1002,user_name:"李四",user_phone:"138****1002",amount:500,status:1,type:2,account_type:"银行卡",account_name:"李四",account_no:"6222xxxxxxx",bank_name:"工商银行",created_at:"2025-07-08 09:45:21",audit_time:"2025-07-08 11:23:45",audit_user:"admin",audit_remark:"审核通过",pay_time:null,pay_remark:null},{id:3,withdraw_no:"W2025070800003",user_id:1003,user_name:"王五",user_phone:"138****1003",amount:100,status:2,type:1,account_type:"支付宝",account_name:"王五",account_no:"<EMAIL>",bank_name:null,created_at:"2025-07-07 16:32:18",audit_time:"2025-07-08 09:15:30",audit_user:"admin",audit_remark:"金额不足，请重新提交",pay_time:null,pay_remark:null},{id:4,withdraw_no:"W2025070800004",user_id:1004,user_name:"赵六",user_phone:"138****1004",amount:300,status:3,type:2,account_type:"银行卡",account_name:"赵六",account_no:"6217xxxxxxx",bank_name:"招商银行",created_at:"2025-07-07 14:56:42",audit_time:"2025-07-07 16:28:35",audit_user:"admin",audit_remark:"审核通过",pay_time:"2025-07-08 10:35:12",pay_remark:"打款成功"}];function f(){try{const t=localStorage.getItem(o);return t?JSON.parse(t):_}catch(t){return console.warn("读取提现数据失败，使用默认数据:",t),_}}function p(t){try{localStorage.setItem(o,JSON.stringify(t))}catch(a){console.error("保存提现数据失败:",a)}}function h(){return i?new Promise(t=>{setTimeout(()=>{const a=u();t({code:200,data:a,message:"获取成功"})},200)}):Object(s["a"])({url:"/api/v1/admin/income/rules",method:"get"})}function v(t){return i?new Promise(a=>{setTimeout(()=>{const e=u(),s=Object.assign(e,t,{updated_at:(new Date).toISOString().replace("T"," ").slice(0,19)});c(s),a({code:200,message:"更新成功"})},500)}):Object(s["a"])({url:"/api/v1/admin/income/rules/update",method:"post",data:t})}function b(t){return i?new Promise(a=>{setTimeout(()=>{const e=m(),s=parseInt(t.page)||1,i=parseInt(t.limit)||10;let r=[...e];t.keyword&&(r=r.filter(a=>a.user_name.includes(t.keyword)||a.user_phone.includes(t.keyword)||a.bill_no.includes(t.keyword))),t.type&&(r=r.filter(a=>a.type===t.type)),void 0!==t.status&&""!==t.status&&(r=r.filter(a=>a.status===parseInt(t.status))),t.start_date&&t.end_date&&(r=r.filter(a=>{const e=new Date(a.created_at),s=new Date(t.start_date),i=new Date(t.end_date);return i.setHours(23,59,59,999),e>=s&&e<=i}));const n=r.length,o=(s-1)*i,l=o+i,u=r.slice(o,l),c=r.reduce((t,a)=>t+a.amount,0),d=r.reduce((t,a)=>t+a.share_amount,0),_=r.reduce((t,a)=>t+a.platform_amount,0);a({code:200,data:{list:u,total:n,page:s,limit:i,stats:{total_amount:c.toFixed(2),total_share_amount:d.toFixed(2),total_platform_amount:_.toFixed(2)}},message:"获取成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/income/bill/list",method:"get",params:t})}function w(t){return i?new Promise((a,e)=>{setTimeout(()=>{try{const s=m(),i=s.find(a=>a.id===parseInt(t));if(!i)return void e(new Error("账单不存在"));const r={code:200,data:{detail:{id:i.id,amount:i.amount,source_type:"wifi_share"===i.type?1:"goods_sale"===i.type?2:3,source_id:i.bill_no,created_at:i.created_at,remark:i.remark},user_info:{id:i.user_id,nickname:i.user_name,phone:i.user_phone,avatar:"",is_leader:Math.random()>.5?1:0,balance:(1e3*Math.random()).toFixed(2)},source_info:"wifi_share"===i.type?{title:"WiFi示例",name:"Test_WiFi_"+i.id,merchant_name:"示例商户",use_count:Math.floor(100*Math.random())}:"goods_sale"===i.type?{order_no:i.bill_no,total_amount:i.amount,status:1,created_at:i.created_at}:{title:"广告示例",space_name:"首页广告位",click_count:Math.floor(1e3*Math.random()),view_count:Math.floor(5e3*Math.random())},profit_detail:[{role:"分享者",rate:70,amount:i.share_amount,user_info:`${i.user_name}(${i.user_phone})`},{role:"平台",rate:30,amount:i.platform_amount,user_info:"系统平台"}]},message:"success"};a(r)}catch(s){e(s)}},200)}):Object(s["a"])({url:"/api/v1/admin/income/bill/detail/"+t,method:"get"})}function g(t){return i?new Promise(a=>{setTimeout(()=>{const e=f(),s=parseInt(t.page)||1,i=parseInt(t.limit)||10;let r=[...e];t.keyword&&(r=r.filter(a=>a.user_name.includes(t.keyword)||a.user_phone.includes(t.keyword)||a.withdraw_no.includes(t.keyword))),void 0!==t.status&&""!==t.status&&(r=r.filter(a=>a.status===parseInt(t.status))),t.start_date&&t.end_date&&(r=r.filter(a=>{const e=new Date(a.created_at),s=new Date(t.start_date),i=new Date(t.end_date);return i.setHours(23,59,59,999),e>=s&&e<=i}));const n=r.length,o=(s-1)*i,l=o+i,u=r.slice(o,l);a({code:200,data:{list:u,total:n,page:s,limit:i},message:"获取成功"})},300)}):Object(s["a"])({url:"/api/v1/admin/withdraw/list",method:"get",params:t})}function k(t){return i?new Promise((a,e)=>{setTimeout(()=>{const s=f(),i=s.find(a=>a.id===parseInt(t));if(i){const t={detail:{...i,card_holder:i.account_name,bank_name:i.bank_name||"支付宝",card_number:i.account_no,bank_branch:"工商银行"===i.bank_name?"北京市朝阳区支行":null,transfer_time:i.pay_time,transaction_id:3===i.status?"TRX202412270001":null,remark:i.audit_remark||i.pay_remark||null},user_info:{id:i.user_id,nickname:i.user_name,phone:i.user_phone,avatar:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",balance:1580.5,is_leader:101===i.user_id?1:0}};a({code:200,data:t,message:"获取成功"})}else e(new Error("提现申请不存在"))},200)}):Object(s["a"])({url:"/api/v1/admin/withdraw/detail/"+t,method:"get"})}function y(t,a){return i?new Promise((e,s)=>{setTimeout(()=>{const i=f(),r=i.findIndex(a=>a.id===parseInt(t));r>-1?(i[r].status=a.status,i[r].audit_time=(new Date).toISOString().replace("T"," ").slice(0,19),i[r].audit_user="admin",i[r].audit_remark=a.remark||(1===a.status?"审核通过":"审核拒绝"),p(i),e({code:200,message:"审核成功"})):s(new Error("提现申请不存在"))},500)}):Object(s["a"])({url:"/api/v1/admin/withdraw/audit/"+t,method:"post",data:a})}function C(t,a){return i?new Promise((e,s)=>{setTimeout(()=>{const i=f(),r=i.findIndex(a=>a.id===parseInt(t));r>-1?(i[r].status=3,i[r].pay_time=(new Date).toISOString().replace("T"," ").slice(0,19),i[r].pay_remark=a.remark||"已打款",i[r].transaction_id=a.transaction_id,p(i),e({code:200,message:"打款成功"})):s(new Error("提现申请不存在"))},500)}):Object(s["a"])({url:"/api/v1/admin/withdraw/confirm/"+t,method:"post",data:a})}}}]);