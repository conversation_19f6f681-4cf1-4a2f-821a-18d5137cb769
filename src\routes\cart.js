const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const { verifyToken, optionalAuth } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const db = require('../database');

/**
 * 获取购物车商品列表 - 客户端路由，需要认证
 * GET /api/v1/client/cart/list
 */
router.get('/client/list', async (req, res) => {
  try {
    // 从请求头中获取token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return error(res, '未授权，请先登录', 401);
    }
    
    const token = authHeader.split(' ')[1];
    // 从token中解析用户ID（这里简化处理，假设payload中有id字段）
    let userId;
    try {
      // 使用require引入jwt模块
      const jwt = require('jsonwebtoken');
      const config = require('../../config');
      
      // 解码token
      const decoded = jwt.verify(token, config.jwt.secret);
      userId = decoded.id;
      
      if (!userId) {
        return error(res, '无效的用户信息', 401);
      }
    } catch (tokenError) {
      console.error('Token解析失败:', tokenError);
      return error(res, 'Token无效或已过期', 401);
    }

    console.log('获取用户购物车数据，用户ID:', userId);
    
    // 查询购物车数据
    const cartItems = await db.query(`
      SELECT c.id, c.goods_id as goodsId, c.quantity, c.specification_id as specificationId, c.selected,
             g.title as name, g.price, g.cover, g.stock
      FROM cart c
      JOIN goods g ON c.goods_id = g.id
      WHERE c.user_id = ?
      ORDER BY c.created_at DESC
    `, [userId]);
    
    console.log('查询到的购物车数据:', cartItems);
    
    // 如果购物车为空，返回空数组
    if (!cartItems || cartItems.length === 0) {
  return success(res, {
        list: [],
        totalPrice: 0,
        totalQuantity: 0
      }, '购物车为空');
    }
  
    // 计算总价和总数量
  const totalPrice = cartItems.reduce((sum, item) => 
      item.selected ? sum + (parseFloat(item.price) * item.quantity) : sum, 0);
    
  const totalQuantity = cartItems.reduce((sum, item) => 
    item.selected ? sum + item.quantity : sum, 0);
  
  return success(res, {
    list: cartItems,
    totalPrice,
    totalQuantity
  }, '获取购物车列表成功');
  } catch (err) {
    console.error('获取购物车数据失败:', err);
    return error(res, '获取购物车数据失败', 500);
  }
});

/**
 * @route   POST /api/v1/client/cart/add
 * @desc    添加商品到购物车
 * @access  Private
 */
router.post('/add', [
  body('goodsId').notEmpty().withMessage('商品ID不能为空'),
  body('quantity').notEmpty().isInt({ min: 1 }).withMessage('数量必须是大于0的整数')
], async (req, res) => {
  try {
    // 从请求头中获取token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return error(res, '未授权，请先登录', 401);
    }
    
    const token = authHeader.split(' ')[1];
    // 从token中解析用户ID
    let userId;
    try {
      const jwt = require('jsonwebtoken');
      const config = require('../../config');
      
      const decoded = jwt.verify(token, config.jwt.secret);
      userId = decoded.id;
      
      if (!userId) {
        return error(res, '无效的用户信息', 401);
      }
    } catch (tokenError) {
      console.error('Token解析失败:', tokenError);
      return error(res, 'Token无效或已过期', 401);
    }
    
    const { goodsId, quantity, specificationId = 0 } = req.body;
    
    console.log('添加商品到购物车，用户ID:', userId, '商品ID:', goodsId, '数量:', quantity);
    
    // 查询商品是否存在
    const goodsExists = await db.query('SELECT id, title, price, stock FROM goods WHERE id = ? AND status = 1', [goodsId]);
    
    if (!goodsExists || goodsExists.length === 0) {
      return error(res, '商品不存在或已下架', 404);
    }
    
    const goods = goodsExists[0];
    
    // 调试日志，检查库存值和类型
    console.log('商品库存信息:', {
      id: goods.id,
      title: goods.title,
      stock: goods.stock,
      stockType: typeof goods.stock,
      quantity: quantity,
      quantityType: typeof quantity
    });
    
    // 确保库存是数字类型
    const stockNum = parseInt(goods.stock || 0);
    const quantityNum = parseInt(quantity || 0);
    
    // 检查库存，添加宽松条件，只有库存明确为0时才拒绝
    if (stockNum <= 0) {
      return error(res, '商品库存不足', 400);
    }
    
    // 如果库存数值合理但小于请求数量，也要提示
    if (stockNum < quantityNum) {
      return error(res, `商品库存不足，当前库存${stockNum}，请减少购买数量`, 400);
    }
    
    // 查询购物车中是否已有该商品
    const existingCartItem = await db.query(
      'SELECT id, quantity FROM cart WHERE user_id = ? AND goods_id = ? AND specification_id = ?', 
      [userId, goodsId, specificationId]
    );
    
    let cartItem;
    
    // 如果购物车中已有该商品，更新数量
    if (existingCartItem && existingCartItem.length > 0) {
      const newQuantity = parseInt(existingCartItem[0].quantity || 0) + quantityNum;
      
      // 再次检查库存
      if (stockNum < newQuantity) {
        return error(res, `商品库存不足，当前库存${stockNum}，购物车已有${existingCartItem[0].quantity}件`, 400);
      }
      
      await db.query(
        'UPDATE cart SET quantity = ?, updated_at = NOW() WHERE id = ?',
        [newQuantity, existingCartItem[0].id]
      );
      
      cartItem = {
        id: existingCartItem[0].id,
        goodsId,
        quantity: newQuantity,
        specificationId,
        selected: 1
      };
    } 
    // 如果购物车中没有该商品，添加新记录
    else {
      const result = await db.query(
        'INSERT INTO cart (user_id, goods_id, quantity, specification_id, selected) VALUES (?, ?, ?, ?, 1)',
        [userId, goodsId, quantityNum, specificationId]
      );
      
      if (!result || !result.insertId) {
        return error(res, '添加到购物车失败', 500);
      }
      
      cartItem = {
        id: result.insertId,
    goodsId,
        quantity: quantityNum,
        specificationId,
        selected: 1
  };
    }
  
  return success(res, cartItem, '商品已成功添加到购物车');
  } catch (err) {
    console.error('添加购物车失败:', err);
    return error(res, '添加到购物车失败', 500);
  }
});

/**
 * @route   POST /api/v1/client/cart/update
 * @desc    更新购物车商品数量
 * @access  Private
 */
router.post('/update', [
  body('id').notEmpty().withMessage('购物车项ID不能为空'),
  body('quantity').notEmpty().isInt({ min: 1 }).withMessage('数量必须是大于0的整数')
], async (req, res) => {
  try {
    // 从请求头中获取token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return error(res, '未授权，请先登录', 401);
    }
    
    const token = authHeader.split(' ')[1];
    // 从token中解析用户ID
    let userId;
    try {
      const jwt = require('jsonwebtoken');
      const config = require('../../config');
      
      const decoded = jwt.verify(token, config.jwt.secret);
      userId = decoded.id;
      
      if (!userId) {
        return error(res, '无效的用户信息', 401);
      }
    } catch (tokenError) {
      console.error('Token解析失败:', tokenError);
      return error(res, 'Token无效或已过期', 401);
    }
    
    const { id, quantity, selected } = req.body;
  
    // 查询购物车项是否存在且属于该用户
    const existingCartItem = await db.query(
      'SELECT c.id, c.goods_id, g.stock FROM cart c JOIN goods g ON c.goods_id = g.id WHERE c.id = ? AND c.user_id = ?', 
      [id, userId]
    );
    
    if (!existingCartItem || existingCartItem.length === 0) {
      return error(res, '购物车项不存在', 404);
    }
    
    // 检查库存
    if (existingCartItem[0].stock < quantity) {
      return error(res, '商品库存不足', 400);
    }
    
    // 更新购物车
    const updateFields = [];
    const updateValues = [];
    
    if (quantity) {
      updateFields.push('quantity = ?');
      updateValues.push(quantity);
    }
    
    if (selected !== undefined) {
      updateFields.push('selected = ?');
      updateValues.push(selected ? 1 : 0);
    }
    
    if (updateFields.length === 0) {
      return error(res, '没有需要更新的字段', 400);
    }
    
    updateFields.push('updated_at = NOW()');
    updateValues.push(id, userId);
    
    await db.query(
      `UPDATE cart SET ${updateFields.join(', ')} WHERE id = ? AND user_id = ?`,
      updateValues
    );
    
    return success(res, { 
    id: parseInt(id),
      quantity: quantity || existingCartItem[0].quantity,
      selected: selected !== undefined ? (selected ? 1 : 0) : existingCartItem[0].selected
    }, '购物车已更新');
  } catch (err) {
    console.error('更新购物车失败:', err);
    return error(res, '更新购物车失败', 500);
  }
});

/**
 * @route   DELETE /api/v1/client/cart/remove
 * @desc    从购物车移除商品
 * @access  Private
 */
router.delete('/remove', async (req, res) => {
  try {
    // 从请求头中获取token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return error(res, '未授权，请先登录', 401);
    }
    
    const token = authHeader.split(' ')[1];
    // 从token中解析用户ID
    let userId;
    try {
      const jwt = require('jsonwebtoken');
      const config = require('../../config');
      
      const decoded = jwt.verify(token, config.jwt.secret);
      userId = decoded.id;
      
      if (!userId) {
        return error(res, '无效的用户信息', 401);
      }
    } catch (tokenError) {
      console.error('Token解析失败:', tokenError);
      return error(res, 'Token无效或已过期', 401);
    }
    
  const { ids } = req.body;
  
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return error(res, 'ids不能为空且必须是数组', 400);
  }
  
    // 删除购物车项
    await db.query(
      'DELETE FROM cart WHERE id IN (?) AND user_id = ?',
      [ids, userId]
    );
  
  return success(res, { removedIds: ids }, '商品已从购物车移除');
  } catch (err) {
    console.error('从购物车移除商品失败:', err);
    return error(res, '从购物车移除商品失败', 500);
  }
});

module.exports = router; 