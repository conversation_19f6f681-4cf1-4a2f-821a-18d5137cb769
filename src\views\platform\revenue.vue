<template>
  <div class="platform-revenue">
    <!-- 收益概览卡片 -->
    <el-row :gutter="20" class="revenue-overview">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon total">
              <i class="el-icon-money"></i>
            </div>
            <div class="card-info">
              <h3>总收益</h3>
              <p class="amount">{{ formatCurrency(overview.total_revenue) }}</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon today">
              <i class="el-icon-calendar"></i>
            </div>
            <div class="card-info">
              <h3>今日收益</h3>
              <p class="amount">{{ formatCurrency(overview.today_revenue) }}</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon platform">
              <i class="el-icon-office-building"></i>
            </div>
            <div class="card-info">
              <h3>平台收益</h3>
              <p class="amount">{{ formatCurrency(overview.platform_revenue) }}</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="card-content">
            <div class="card-icon leader">
              <i class="el-icon-user"></i>
            </div>
            <div class="card-info">
              <h3>团长收益</h3>
              <p class="amount">{{ formatCurrency(overview.leader_revenue) }}</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 收益趋势图表 -->
    <el-card class="chart-card">
      <div slot="header" class="clearfix">
        <span>收益趋势</span>
        <div style="float: right;">
          <el-radio-group v-model="trendPeriod" @change="loadTrendData">
            <el-radio-button label="7d">近7天</el-radio-button>
            <el-radio-button label="30d">近30天</el-radio-button>
            <el-radio-button label="90d">近90天</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div ref="trendChart" style="height: 400px;"></div>
    </el-card>

    <!-- 业务类型分布 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header">
            <span>业务类型分布</span>
          </div>
          <div ref="businessChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <div slot="header">
            <span>收益分配比例</span>
          </div>
          <div ref="distributionChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 财务报表 -->
    <el-card class="table-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>财务报表</span>
        <div style="float: right;">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="loadFinancialData"
          />
          <el-button type="primary" icon="el-icon-download" @click="exportReport">
            导出报表
          </el-button>
        </div>
      </div>
      
      <el-table :data="financialData" border style="width: 100%">
        <el-table-column prop="date" label="日期" width="120"></el-table-column>
        <el-table-column prop="business_type" label="业务类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getBusinessTypeColor(scope.row.business_type)">
              {{ getBusinessTypeName(scope.row.business_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="total_amount" label="总收益" width="120">
          <template slot-scope="scope">
            <span>{{ formatCurrency(scope.row.total_amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="platform_amount" label="平台收益" width="120">
          <template slot-scope="scope">
            <span>{{ formatCurrency(scope.row.platform_amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="leader_amount" label="团长收益" width="120">
          <template slot-scope="scope">
            <span>{{ formatCurrency(scope.row.leader_amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="transaction_count" label="交易笔数" width="100"></el-table-column>
        <el-table-column prop="avg_amount" label="平均金额" width="120">
          <template slot-scope="scope">
            <span>{{ formatCurrency(scope.row.avg_amount) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination 
        v-show="financialTotal > 0" 
        :total="financialTotal" 
        :page.sync="financialQuery.page" 
        :limit.sync="financialQuery.limit" 
        @pagination="loadFinancialData" 
      />
    </el-card>
  </div>
</template>

<script>
import { 
  getPlatformRevenue, 
  getRevenueTrend, 
  getBusinessDistribution, 
  getFinancialReport 
} from '@/api/platform-revenue'
import Pagination from '@/components/Pagination'
import * as echarts from 'echarts'

export default {
  name: 'PlatformRevenue',
  components: { Pagination },
  data() {
    return {
      overview: {
        total_revenue: 0,
        today_revenue: 0,
        platform_revenue: 0,
        leader_revenue: 0
      },
      trendPeriod: '7d',
      trendChart: null,
      businessChart: null,
      distributionChart: null,
      dateRange: [],
      financialData: [],
      financialTotal: 0,
      financialQuery: {
        page: 1,
        limit: 20
      }
    }
  },
  mounted() {
    this.loadOverviewData()
    this.loadTrendData()
    this.loadBusinessData()
    this.loadFinancialData()
    this.initCharts()
  },
  beforeDestroy() {
    if (this.trendChart) {
      this.trendChart.dispose()
    }
    if (this.businessChart) {
      this.businessChart.dispose()
    }
    if (this.distributionChart) {
      this.distributionChart.dispose()
    }
  },
  methods: {
    async loadOverviewData() {
      try {
        const response = await getPlatformRevenue()
        this.overview = response.data
      } catch (error) {
        console.error('加载收益概览失败:', error)
      }
    },
    async loadTrendData() {
      try {
        const response = await getRevenueTrend({ period: this.trendPeriod })
        this.renderTrendChart(response.data)
      } catch (error) {
        console.error('加载收益趋势失败:', error)
      }
    },
    async loadBusinessData() {
      try {
        const response = await getBusinessDistribution()
        this.renderBusinessChart(response.data)
        this.renderDistributionChart()
      } catch (error) {
        console.error('加载业务分布失败:', error)
      }
    },
    async loadFinancialData() {
      try {
        const params = {
          ...this.financialQuery,
          start_date: this.dateRange[0],
          end_date: this.dateRange[1]
        }
        const response = await getFinancialReport(params)
        this.financialData = response.data.items
        this.financialTotal = response.data.total
      } catch (error) {
        console.error('加载财务报表失败:', error)
      }
    },
    initCharts() {
      this.$nextTick(() => {
        this.trendChart = echarts.init(this.$refs.trendChart)
        this.businessChart = echarts.init(this.$refs.businessChart)
        this.distributionChart = echarts.init(this.$refs.distributionChart)
      })
    },
    renderTrendChart(data) {
      if (!this.trendChart) return
      
      const option = {
        title: {
          text: '收益趋势图'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['平台收益', '团长收益', '总收益']
        },
        xAxis: {
          type: 'category',
          data: data.map(item => item.date)
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '平台收益',
            type: 'line',
            data: data.map(item => item.platform_amount)
          },
          {
            name: '团长收益',
            type: 'line',
            data: data.map(item => item.leader_amount)
          },
          {
            name: '总收益',
            type: 'line',
            data: data.map(item => item.total_amount)
          }
        ]
      }
      this.trendChart.setOption(option)
    },
    renderBusinessChart(data) {
      if (!this.businessChart) return
      
      const option = {
        title: {
          text: '业务类型分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: data.map(item => ({
              value: item.total_amount,
              name: this.getBusinessTypeName(item.business_type)
            }))
          }
        ]
      }
      this.businessChart.setOption(option)
    },
    renderDistributionChart() {
      if (!this.distributionChart) return
      
      const option = {
        title: {
          text: '收益分配比例',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: [
              { value: this.overview.platform_revenue, name: '平台收益' },
              { value: this.overview.leader_revenue, name: '团长收益' }
            ]
          }
        ]
      }
      this.distributionChart.setOption(option)
    },
    formatCurrency(value) {
      return '¥ ' + parseFloat(value || 0).toFixed(2)
    },
    getBusinessTypeName(type) {
      const typeMap = {
        wifi_share: 'WiFi分享',
        goods_sale: '商品销售',
        advertisement: '广告收益'
      }
      return typeMap[type] || type
    },
    getBusinessTypeColor(type) {
      const colorMap = {
        wifi_share: 'primary',
        goods_sale: 'success',
        advertisement: 'warning'
      }
      return colorMap[type] || 'info'
    },
    exportReport() {
      this.$message.success('报表导出功能开发中...')
    }
  }
}
</script>

<style scoped>
.platform-revenue {
  padding: 20px;
}

.revenue-overview {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.card-icon i {
  font-size: 24px;
  color: white;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.today {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.platform {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.leader {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-info h3 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #666;
}

.card-info .amount {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.chart-card, .table-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
