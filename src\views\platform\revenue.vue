<template>
  <div class="app-container">
    <el-card class="revenue-card">
      <div slot="header" class="clearfix">
        <span>平台收益管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshData">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
      
      <!-- 收益概览 -->
      <el-row :gutter="20" class="overview-cards">
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-icon total-icon">
              <i class="el-icon-money"></i>
            </div>
            <div class="card-content">
              <div class="card-title">总收益</div>
              <div class="card-value">¥{{ formatMoney(overview.total_revenue || 0) }}</div>
              <div class="card-footer">
                <span>今日收益: </span>
                <span class="highlight">¥{{ formatMoney(overview.today_revenue || 0) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-icon platform-icon">
              <i class="el-icon-office-building"></i>
            </div>
            <div class="card-content">
              <div class="card-title">平台分润</div>
              <div class="card-value">¥{{ formatMoney(overview.platform_revenue || 0) }}</div>
              <div class="card-footer">
                <span>占比: </span>
                <span class="highlight">{{ calculatePercentage(overview.platform_revenue, overview.total_revenue) }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-icon leader-icon">
              <i class="el-icon-user-solid"></i>
            </div>
            <div class="card-content">
              <div class="card-title">团长分润</div>
              <div class="card-value">¥{{ formatMoney(overview.leader_revenue || 0) }}</div>
              <div class="card-footer">
                <span>占比: </span>
                <span class="highlight">{{ calculatePercentage(overview.leader_revenue, overview.total_revenue) }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-icon user-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="card-content">
              <div class="card-title">用户分润</div>
              <div class="card-value">¥{{ formatMoney(overview.user_revenue || 0) }}</div>
              <div class="card-footer">
                <span>占比: </span>
                <span class="highlight">{{ calculatePercentage(overview.user_revenue, overview.total_revenue) }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 时间筛选 -->
      <el-row style="margin: 20px 0">
        <el-col :span="24">
          <el-form :inline="true" size="small">
            <el-form-item label="统计周期">
              <el-radio-group v-model="timePeriod" @change="handlePeriodChange">
                <el-radio-button label="7d">7天</el-radio-button>
                <el-radio-button label="30d">30天</el-radio-button>
                <el-radio-button label="90d">90天</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="自定义时间">
              <el-date-picker
                v-model="customDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="handleCustomDateChange"
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      
      <!-- 收益趋势图 -->
      <el-card class="chart-card">
        <div slot="header" class="clearfix">
          <span>收益趋势</span>
        </div>
        <div class="chart-container" ref="revenueTrendChart"></div>
      </el-card>
      
      <!-- 业务类型分布 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>业务类型收益分布</span>
            </div>
            <div class="chart-container" ref="businessPieChart"></div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>分润比例分布</span>
            </div>
            <div class="chart-container" ref="profitPieChart"></div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 财务报表 -->
      <el-card class="chart-card">
        <div slot="header" class="clearfix">
          <span>财务报表</span>
          <el-button
            style="float: right; padding: 3px 0"
            type="text"
            icon="el-icon-download"
            @click="exportReport"
          >
            导出报表
          </el-button>
        </div>
        
        <el-table
          v-loading="tableLoading"
          :data="financialReport"
          border
          style="width: 100%"
          show-summary
          :summary-method="getSummaries"
        >
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="business_type" label="业务类型" width="120">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.business_type === 'wifi_share'" type="primary">WiFi分享</el-tag>
              <el-tag v-else-if="scope.row.business_type === 'goods_sale'" type="success">商品销售</el-tag>
              <el-tag v-else-if="scope.row.business_type === 'advertisement'" type="warning">广告点击</el-tag>
              <el-tag v-else type="info">其他</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="total_amount" label="总收益" width="120">
            <template slot-scope="scope">
              ¥{{ formatMoney(scope.row.total_amount) }}
            </template>
          </el-table-column>
          <el-table-column prop="platform_amount" label="平台分润" width="120">
            <template slot-scope="scope">
              ¥{{ formatMoney(scope.row.platform_amount) }}
            </template>
          </el-table-column>
          <el-table-column prop="leader_amount" label="团长分润" width="120">
            <template slot-scope="scope">
              ¥{{ formatMoney(scope.row.leader_amount) }}
            </template>
          </el-table-column>
          <el-table-column prop="user_amount" label="用户分润" width="120">
            <template slot-scope="scope">
              ¥{{ formatMoney(scope.row.user_amount) }}
            </template>
          </el-table-column>
          <el-table-column prop="transaction_count" label="交易笔数" width="100" />
          <el-table-column prop="avg_amount" label="平均金额" width="120">
            <template slot-scope="scope">
              ¥{{ formatMoney(scope.row.avg_amount) }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <pagination
          v-show="safeFinancialTotal > 0"
          :total="safeFinancialTotal"
          :page.sync="financialQuery.page"
          :limit.sync="financialQuery.limit"
          @pagination="loadFinancialData"
        />
      </el-card>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getPlatformRevenue, getRevenueTrend, getBusinessDistribution, getFinancialReport } from '@/api/platform-revenue'
import Pagination from '@/components/Pagination'

export default {
  name: 'PlatformRevenue',
  components: { Pagination },
  data() {
    return {
      loading: false,
      tableLoading: false,
      timePeriod: '30d',
      customDateRange: [],
      overview: {},
      financialReport: [],
      financialTotal: 0,
      financialQuery: {
        page: 1,
        limit: 20
      },

      // 图表实例
      revenueTrendChart: null,
      businessPieChart: null,
      profitPieChart: null,

      // 图表数据
      trendData: [],
      businessData: [],
      profitData: []
    }
  },
  computed: {
    // 确保total始终是数字类型，避免分页组件报错
    safeFinancialTotal() {
      if (this.financialTotal === null || this.financialTotal === undefined) {
        return 0
      }
      if (typeof this.financialTotal === 'string') {
        const parsed = parseInt(this.financialTotal, 10)
        return isNaN(parsed) ? 0 : parsed
      }
      if (typeof this.financialTotal === 'number' && !isNaN(this.financialTotal)) {
        return Math.max(0, Math.floor(this.financialTotal))
      }
      return 0
    }
  },
  mounted() {
    this.initData()
  },
  beforeDestroy() {
    // 销毁图表实例
    if (this.revenueTrendChart) {
      this.revenueTrendChart.dispose()
    }
    if (this.businessPieChart) {
      this.businessPieChart.dispose()
    }
    if (this.profitPieChart) {
      this.profitPieChart.dispose()
    }
  },
  methods: {
    initData() {
      this.fetchOverview()
      this.fetchTrendData()
      this.fetchBusinessData()
      this.fetchFinancialReport()
    },
    refreshData() {
      this.initData()
      this.$message.success('数据已刷新')
    },
    handlePeriodChange() {
      this.customDateRange = []
      this.fetchTrendData()
      this.fetchBusinessData()
      this.fetchFinancialReport()
    },
    handleCustomDateChange(val) {
      if (val && val.length === 2) {
        this.timePeriod = 'custom'
        this.fetchTrendData()
        this.fetchBusinessData()
        this.fetchFinancialReport()
      }
    },
    async fetchOverview() {
      try {
        this.loading = true
        const response = await getPlatformRevenue()
        console.log('fetchOverview - response:', response)

        // 修复数据访问路径
        if (response && response.data && response.data.data) {
          this.overview = response.data.data
        } else if (response && response.data) {
          this.overview = response.data
        } else {
          this.overview = {}
        }

        console.log('fetchOverview - overview:', this.overview)
      } catch (error) {
        console.error('获取平台收益概览失败:', error)
        this.$message.error('获取平台收益概览失败')
        this.overview = {}
      } finally {
        this.loading = false
      }
    },
    async fetchTrendData() {
      try {
        const params = {
          period: this.timePeriod
        }

        if (this.customDateRange && this.customDateRange.length === 2) {
          params.start_date = this.customDateRange[0]
          params.end_date = this.customDateRange[1]
        }

        const response = await getRevenueTrend(params)
        console.log('fetchTrendData - response:', response)

        // 确保数据是数组格式 - 修复数据访问路径
        if (response && response.data && response.data.data) {
          // 后端返回格式: { status: 'success', message: '...', data: [...] }
          this.trendData = Array.isArray(response.data.data) ? response.data.data : []
        } else if (response && response.data && Array.isArray(response.data)) {
          // 兼容直接返回数组的情况
          this.trendData = response.data
        } else {
          this.trendData = []
        }

        console.log('fetchTrendData - trendData:', this.trendData)
        this.renderRevenueTrendChart()
      } catch (error) {
        console.error('获取收益趋势数据失败:', error)
        this.$message.error('获取收益趋势数据失败')
        this.trendData = []
      }
    },
    async fetchBusinessData() {
      try {
        const params = {
          period: this.timePeriod
        }

        if (this.customDateRange && this.customDateRange.length === 2) {
          params.start_date = this.customDateRange[0]
          params.end_date = this.customDateRange[1]
        }

        const response = await getBusinessDistribution(params)
        console.log('fetchBusinessData - response:', response)

        // 确保数据是数组格式 - 修复数据访问路径
        if (response && response.data && response.data.data) {
          // 后端返回格式: { status: 'success', message: '...', data: [...] }
          this.businessData = Array.isArray(response.data.data) ? response.data.data : []
        } else if (response && response.data && Array.isArray(response.data)) {
          // 兼容直接返回数组的情况
          this.businessData = response.data
        } else {
          this.businessData = []
        }

        console.log('fetchBusinessData - businessData:', this.businessData)
        this.renderBusinessCharts()
      } catch (error) {
        console.error('获取业务分布数据失败:', error)
        this.businessData = []
        this.$message.error('获取业务分布数据失败')
      }
    },
    async fetchFinancialReport() {
      // 重置分页并加载数据
      this.financialQuery.page = 1
      this.loadFinancialData()
    },
    async loadFinancialData() {
      try {
        this.tableLoading = true
        const params = {
          period: this.timePeriod,
          page: this.financialQuery.page,
          limit: this.financialQuery.limit
        }

        if (this.customDateRange && this.customDateRange.length === 2) {
          params.start_date = this.customDateRange[0]
          params.end_date = this.customDateRange[1]
        }

        const response = await getFinancialReport(params)
        console.log('loadFinancialData - response:', response)

        // 处理响应数据，确保数据结构正确
        if (response && response.data && response.data.data) {
          // 后端返回格式: { status: 'success', message: '...', data: [...] }
          if (Array.isArray(response.data.data)) {
            // 如果直接返回数组，模拟分页
            this.financialReport = response.data.data
            this.financialTotal = response.data.data.length
          } else if (response.data.data.items && Array.isArray(response.data.data.items)) {
            // 如果有分页结构
            this.financialReport = response.data.data.items
            this.financialTotal = response.data.data.total || response.data.data.items.length
          } else if (response.data.data.list && Array.isArray(response.data.data.list)) {
            // 另一种分页结构
            this.financialReport = response.data.data.list
            this.financialTotal = response.data.data.total || response.data.data.list.length
          } else {
            // 其他情况，尝试直接使用data
            this.financialReport = Array.isArray(response.data.data) ? response.data.data : []
            this.financialTotal = this.financialReport.length
          }
        } else if (response && response.data && Array.isArray(response.data)) {
          // 兼容直接返回数组的情况
          this.financialReport = response.data
          this.financialTotal = response.data.length
        } else {
          this.financialReport = []
          this.financialTotal = 0
        }

        console.log('loadFinancialData - financialReport:', this.financialReport)
        console.log('loadFinancialData - financialTotal:', this.financialTotal)
      } catch (error) {
        console.error('获取财务报表失败:', error)
        this.$message.error('获取财务报表失败')
        this.financialReport = []
        this.financialTotal = 0
      } finally {
        this.tableLoading = false
      }
    },
    renderRevenueTrendChart() {
      if (!this.revenueTrendChart) {
        this.revenueTrendChart = echarts.init(this.$refs.revenueTrendChart)
      }

      // 确保数据是数组，避免map错误
      console.log('renderRevenueTrendChart - trendData:', this.trendData)
      const safeData = Array.isArray(this.trendData) ? this.trendData : []
      console.log('renderRevenueTrendChart - safeData:', safeData)

      const dates = safeData.map(item => item.date || '')
      const platformRevenue = safeData.map(item => item.platform_amount || 0)
      const leaderRevenue = safeData.map(item => item.leader_amount || 0)
      const userRevenue = safeData.map(item => item.user_amount || 0)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['平台分润', '团长分润', '用户分润']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '平台分润',
            type: 'bar',
            stack: 'Total',
            data: platformRevenue,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '团长分润',
            type: 'bar',
            stack: 'Total',
            data: leaderRevenue,
            itemStyle: {
              color: '#67C23A'
            }
          },
          {
            name: '用户分润',
            type: 'bar',
            stack: 'Total',
            data: userRevenue,
            itemStyle: {
              color: '#E6A23C'
            }
          }
        ]
      }
      
      this.revenueTrendChart.setOption(option)
    },
    renderBusinessCharts() {
      this.renderBusinessPieChart()
      this.renderProfitPieChart()
    },
    renderBusinessPieChart() {
      if (!this.businessPieChart) {
        this.businessPieChart = echarts.init(this.$refs.businessPieChart)
      }

      const businessTypeMap = {
        'wifi_share': 'WiFi分享',
        'goods_sale': '商品销售',
        'advertisement': '广告点击'
      }

      // 确保数据是数组，避免map错误
      console.log('renderBusinessPieChart - businessData:', this.businessData)
      const safeData = Array.isArray(this.businessData) ? this.businessData : []
      console.log('renderBusinessPieChart - safeData:', safeData)

      const businessData = safeData.map(item => ({
        value: item.total_amount || 0,
        name: businessTypeMap[item.business_type] || item.business_type || '未知'
      }))
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: businessData.map(item => item.name)
        },
        series: [
          {
            name: '业务收益',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: businessData
          }
        ]
      }
      
      this.businessPieChart.setOption(option)
    },
    renderProfitPieChart() {
      if (!this.profitPieChart) {
        this.profitPieChart = echarts.init(this.$refs.profitPieChart)
      }
      
      const profitData = [
        { value: this.overview.platform_revenue, name: '平台分润' },
        { value: this.overview.leader_revenue, name: '团长分润' },
        { value: this.overview.user_revenue, name: '用户分润' }
      ]
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: ['平台分润', '团长分润', '用户分润']
        },
        series: [
          {
            name: '分润分布',
            type: 'pie',
            radius: '50%',
            data: profitData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      
      this.profitPieChart.setOption(option)
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        if (index === 1) {
          sums[index] = ''
          return
        }
        
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = '¥' + this.formatMoney(values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0))
        } else {
          sums[index] = ''
        }
      })
      
      return sums
    },
    exportReport() {
      // 导出财务报表功能
      this.$message.info('导出功能开发中...')
    },
    calculatePercentage(part, total) {
      if (!total || total === 0) return '0.00'
      return ((part / total) * 100).toFixed(2)
    },
    formatMoney(num) {
      return parseFloat(num || 0).toFixed(2)
    }
  }
}
</script>

<style scoped>
.revenue-card {
  margin-bottom: 20px;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
  display: flex;
  align-items: center;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.card-icon i {
  font-size: 30px;
  color: white;
}

.total-icon {
  background-color: #409EFF;
}

.platform-icon {
  background-color: #67C23A;
}

.leader-icon {
  background-color: #E6A23C;
}

.user-icon {
  background-color: #F56C6C;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #909399;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin: 5px 0;
}

.card-footer {
  font-size: 12px;
  color: #909399;
}

.highlight {
  color: #F56C6C;
  font-weight: bold;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}
</style>
