import request from '@/utils/request'

// 本地模拟数据，用于开发测试
const useMock = false  // 禁用模拟数据

// WiFi数据的本地存储管理
const WIFI_STORAGE_KEY = 'wifi_admin_wifi_list'

// 默认WiFi数据
const defaultWifiList = [
              {
                id: 1,
                title: 'WiFi测试1',
                name: 'Test WiFi',
                password: '12345678',
                merchant_name: '测试商家1',
    qrcode: '',  // 二维码将在前端生成
                use_count: 123,
                user_id: 1,
                status: 1,
                created_at: '2023-06-01 12:30:45'
              },
              {
                id: 2,
                title: 'WiFi测试2',
                name: 'Office WiFi',
                password: '87654321',
                merchant_name: '测试商家2',
    qrcode: '',  // 二维码将在前端生成
                use_count: 456,
                user_id: 2,
                status: 1,
                created_at: '2023-06-02 10:20:30'
              }
]

// 获取WiFi数据（从本地存储读取，如果没有则使用默认数据）
function getWifiData() {
  try {
    const stored = localStorage.getItem(WIFI_STORAGE_KEY)
    return stored ? JSON.parse(stored) : defaultWifiList
  } catch (error) {
    console.warn('读取WiFi数据失败，使用默认数据:', error)
    return defaultWifiList
  }
}

// 保存WiFi数据到本地存储
function saveWifiData(data) {
  try {
    localStorage.setItem(WIFI_STORAGE_KEY, JSON.stringify(data))
  } catch (error) {
    console.error('保存WiFi数据失败:', error)
  }
}

// 生成当前时间字符串
function getCurrentTime() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hour = String(now.getHours()).padStart(2, '0')
  const minute = String(now.getMinutes()).padStart(2, '0')
  const second = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}

// WiFi码列表
export function getWifiList (query) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前WiFi数据
        const mockWifiList = getWifiData()
        
        // 处理分页
        const page = parseInt(query.page) || 1
        const limit = parseInt(query.limit) || 10
        const startIndex = (page - 1) * limit
        const endIndex = startIndex + limit
        
        // 处理搜索
        let filteredList = mockWifiList
        if (query.keyword) {
          const keyword = query.keyword.toLowerCase()
          filteredList = mockWifiList.filter(item => 
            item.title.toLowerCase().includes(keyword) || 
            item.name.toLowerCase().includes(keyword) ||
            item.merchant_name.toLowerCase().includes(keyword)
          )
        }
        
        // 处理状态筛选
        if (query.status !== undefined && query.status !== '') {
          filteredList = filteredList.filter(item => item.status === parseInt(query.status))
        }
        
        const paginatedList = filteredList.slice(startIndex, endIndex)
        
        resolve({
          code: 200,
          data: {
            list: paginatedList,
            total: filteredList.length
          },
          message: '获取WiFi列表成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/wifi/list',
    method: 'get',
    params: query
  })
}

// WiFi码详情
export function getWifiDetail (id) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前WiFi数据
        const mockWifiList = getWifiData()
        const wifi = mockWifiList.find(item => item.id === parseInt(id))
        if (wifi) {
        resolve({
          code: 200,
            data: wifi,
          message: '获取WiFi详情成功'
        })
        } else {
          resolve({
            code: 404,
            message: '未找到该WiFi码'
          })
        }
      }, 300)
    })
  }

  // 修复URL路径，确保与后端路由一致
  console.log('请求WiFi详情, ID:', id)
  return request({
    url: `/api/v1/admin/wifi/detail/${id}`,
    method: 'get'
  })
}

// 创建WiFi码
export function createWifi (data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前WiFi数据
        const mockWifiList = getWifiData()
        
        // 生成新ID
        const newId = mockWifiList.length > 0 
          ? Math.max(...mockWifiList.map(item => item.id)) + 1 
          : 1
        
        // 创建新WiFi码
        const newWifi = {
          id: newId,
          title: data.title,
          name: data.name,
          password: data.password,
          merchant_name: data.merchant_name,
          qrcode: '',  // 二维码将在前端生成
          use_count: 0,
          user_id: 1, // 模拟当前用户ID
          status: data.status,
          created_at: getCurrentTime()
        }
        
        // 添加到列表
        mockWifiList.push(newWifi)
        
        // 保存到本地存储
        saveWifiData(mockWifiList)
        
        console.log('模拟数据 - 创建WiFi码成功:', newWifi)
        console.log('当前WiFi列表:', mockWifiList)
        
        resolve({
          code: 200,
          data: { id: newId },
          message: '创建WiFi码成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/wifi/create',
    method: 'post',
    data
  })
}

// 更新WiFi码
export function updateWifi (id, data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前WiFi数据
        const mockWifiList = getWifiData()
        const index = mockWifiList.findIndex(item => item.id === parseInt(id))
        if (index !== -1) {
          // 更新WiFi码
          mockWifiList[index] = {
            ...mockWifiList[index],
            ...data
          }
          
          // 保存到本地存储
          saveWifiData(mockWifiList)
          
          console.log('模拟数据 - 更新WiFi码成功:', mockWifiList[index])
          
        resolve({
          code: 200,
          data: {},
          message: '更新WiFi码成功'
        })
        } else {
          resolve({
            code: 404,
            message: '未找到该WiFi码'
          })
        }
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/wifi/update/${id}`,
    method: 'put',
    data
  })
}

// 删除WiFi码
export function deleteWifi (id) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前WiFi数据
        const mockWifiList = getWifiData()
        const index = mockWifiList.findIndex(item => item.id === parseInt(id))
        if (index !== -1) {
          // 删除WiFi码
          mockWifiList.splice(index, 1)
          
          // 保存到本地存储
          saveWifiData(mockWifiList)
          
          console.log('模拟数据 - 删除WiFi码成功, ID:', id)
          console.log('当前WiFi列表:', mockWifiList)
          
        resolve({
          code: 200,
          data: {},
          message: '删除WiFi码成功'
        })
        } else {
          resolve({
            code: 404,
            message: '未找到该WiFi码'
          })
        }
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/wifi/${id}`,
    method: 'delete'
  })
}

// 更新WiFi码状态
export function updateWifiStatus (id, data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前WiFi数据
        const mockWifiList = getWifiData()
        const index = mockWifiList.findIndex(item => item.id === parseInt(id))
        if (index !== -1) {
          // 更新状态
          mockWifiList[index].status = data.status
          
          // 保存到本地存储
          saveWifiData(mockWifiList)
          
          console.log('模拟数据 - 更新WiFi码状态成功:', mockWifiList[index])
          
        resolve({
          code: 200,
          data: {},
          message: '更新WiFi码状态成功'
        })
        } else {
          resolve({
            code: 404,
            message: '未找到该WiFi码'
          })
        }
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/wifi/status/${id}`,
    method: 'put',
    data
  })
}

// 获取WiFi统计数据
export function getWifiStats (query) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前WiFi数据
        const mockWifiList = getWifiData()
        
        const total = mockWifiList.length
        const active = mockWifiList.filter(item => item.status === 1).length
        const inactive = total - active
        const totalUseCount = mockWifiList.reduce((sum, item) => sum + item.use_count, 0)
        
        // 生成趋势数据
        const trendData = []
        const now = new Date()
        let days = 7
        
        if (query && query.time_range) {
          if (query.time_range === 'month') {
            days = 30
          } else if (query.time_range === 'year') {
            days = 365
          }
        }
        
        for (let i = days - 1; i >= 0; i--) {
          const date = new Date(now)
          date.setDate(date.getDate() - i)
          const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
          trendData.push({
            date: dateStr,
            count: Math.floor(Math.random() * 100) + 10
          })
        }
        
        // 排序生成热门WiFi列表
        const sortedWifiList = [...mockWifiList].sort((a, b) => b.use_count - a.use_count).slice(0, 5)
        
        resolve({
          code: 200,
          data: {
            stats: {
              total: total,
              active: active,
              inactive: inactive,
              total_use_count: totalUseCount
            },
            trend_data: trendData,
            top_wifi_list: sortedWifiList
          },
          message: '获取WiFi统计数据成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/wifi/stats',
    method: 'get',
    params: query
  })
}
