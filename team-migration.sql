-- ========================================
-- 团队表邀请码迁移脚本
-- 执行日期: 2024-07-14
-- ========================================

-- 查看当前团队表结构
SELECT 'Current team table structure:' as info;
DESCRIBE team;

-- 查看现有团队数据
SELECT 'Current team data:' as info;
SELECT id, name, created_at FROM team;

-- ========================================
-- 添加新字段
-- ========================================

-- 添加邀请码字段
SELECT 'Adding invite_code column...' as info;
ALTER TABLE team ADD COLUMN invite_code VARCHAR(20) UNIQUE COMMENT '团队邀请码';

-- 添加团队等级字段
SELECT 'Adding level column...' as info;
ALTER TABLE team ADD COLUMN level INT DEFAULT 1 COMMENT '团队等级';

-- ========================================
-- 数据初始化
-- ========================================

-- 为现有团队生成邀请码
SELECT 'Generating invite codes...' as info;
UPDATE team 
SET invite_code = CONCAT('TEAM', LPAD(id, 6, '0'))
WHERE invite_code IS NULL OR invite_code = '';

-- 设置默认团队等级
SELECT 'Setting default team levels...' as info;
UPDATE team 
SET level = 1 
WHERE level IS NULL OR level = 0;

-- ========================================
-- 验证结果
-- ========================================

-- 查看更新后的表结构
SELECT 'Updated team table structure:' as info;
DESCRIBE team;

-- 查看更新后的团队数据
SELECT 'Updated team data:' as info;
SELECT 
    id,
    name,
    invite_code,
    level,
    created_at,
    updated_at
FROM team 
ORDER BY id;

-- 验证邀请码唯一性
SELECT 'Checking invite code uniqueness:' as info;
SELECT 
    invite_code, 
    COUNT(*) as count 
FROM team 
WHERE invite_code IS NOT NULL 
GROUP BY invite_code 
HAVING COUNT(*) > 1;

-- 统计信息
SELECT 'Migration summary:' as info;
SELECT 
    COUNT(*) as total_teams,
    COUNT(invite_code) as teams_with_invite_code,
    COUNT(CASE WHEN level > 0 THEN 1 END) as teams_with_level
FROM team;

SELECT 'Migration completed successfully!' as result;
