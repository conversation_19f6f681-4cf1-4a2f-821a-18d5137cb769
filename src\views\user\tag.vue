<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleCreate">新增标签</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="正在加载..."
      border
      fit
      highlight-current-row
    >
      <el-table-column label="ID" prop="id" align="center" width="80" />
      <el-table-column label="标签名称" prop="name" align="center" min-width="120" />
      <el-table-column label="标签描述" prop="description" align="center" min-width="200" />
      <el-table-column label="创建时间" align="center" width="160">
        <template slot-scope="{row}">
          <span>{{ row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="success" size="mini" @click="handleUpdate(row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="dialogStatus === 'create' ? '创建标签' : '编辑标签'" :visible.sync="dialogVisible" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="标签名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入标签名称" />
        </el-form-item>
        <el-form-item label="标签描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入标签描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createUserTag, updateUserTag, deleteUserTag, getUserTagList } from '@/api/user-manage'

export default {
  name: 'UserTag',
  data () {
    return {
      list: [],
      total: 0,
      listLoading: true,
      dialogVisible: false,
      dialogStatus: 'create',
      form: {
        id: undefined,
        name: '',
        description: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入标签名称', trigger: 'blur' },
          { max: 50, message: '标签名称长度不能超过50个字符', trigger: 'blur' }
        ],
        description: [
          { max: 255, message: '标签描述长度不能超过255个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.listLoading = true
      getUserTagList().then(response => {
        this.list = response.data || []
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
        this.$message.error('获取标签列表失败')
      })
    },
    resetForm () {
      this.form = {
        id: undefined,
        name: '',
        description: ''
      }
    },
    handleCreate () {
      this.resetForm()
      this.dialogStatus = 'create'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    handleUpdate (row) {
      this.form = {
        id: row.id,
        name: row.name,
        description: row.description
      }
      this.dialogStatus = 'update'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate()
      })
    },
    handleDelete (row) {
      this.$confirm('确认要删除这个标签吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteUserTag(row.id).then(response => {
          this.$message.success('删除成功')
          this.getList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        // 取消删除
      })
    },
    submitForm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.dialogStatus === 'create') {
            createUserTag(this.form).then(response => {
              this.$message.success('创建成功')
              this.dialogVisible = false
              this.getList()
            }).catch(() => {
              this.$message.error('创建失败')
            })
          } else {
            updateUserTag(this.form.id, this.form).then(response => {
              this.$message.success('更新成功')
              this.dialogVisible = false
              this.getList()
            }).catch(() => {
              this.$message.error('更新失败')
            })
          }
        }
      })
    }
  }
}
</script>
