<!--pages/mall/cart/cart.wxml-->
<view class="cart-container">
  <!-- 广告区域 -->
  <view class="ad-section">
    <image 
      src="/assets/images/cart-banner.jpg" 
      class="ad-banner" 
      mode="aspectFill"
      bindtap="onAdClick"
    ></image>
    <view class="ad-overlay">
      <text class="ad-title">购物车专享优惠</text>
      <text class="ad-subtitle">满199减20，满399减50</text>
    </view>
  </view>

  <!-- 购物车主要内容 -->
  <view class="cart-content">
    <!-- 全选区域 -->
    <view class="select-all-section" wx:if="{{cartList.length > 0}}">
      <view class="select-all-wrapper" bindtap="onSelectAll">
        <view class="checkbox {{allSelected ? 'checked' : ''}}">
          <text class="checkbox-icon">{{allSelected ? '✓' : ''}}</text>
        </view>
        <text class="select-all-text">全选</text>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class="cart-list" wx:if="{{cartList.length > 0}}">
      <view 
        class="cart-item" 
        wx:for="{{cartList}}" 
        wx:key="id"
        data-index="{{index}}"
      >
        <view class="item-wrapper">
          <!-- 选择框 -->
          <view 
            class="item-checkbox"
            bindtap="onSelectItem"
            data-index="{{index}}"
          >
            <view class="checkbox {{item.selected ? 'checked' : ''}}">
              <text class="checkbox-icon">{{item.selected ? '✓' : ''}}</text>
            </view>
          </view>

          <!-- 商品信息 -->
          <view class="item-content">
            <!-- 商品图片 -->
            <view class="item-image">
              <image 
                src="{{item.cover || '/assets/images/goods-placeholder.jpg'}}" 
                class="goods-image" 
                mode="aspectFill"
                bindtap="onGoodsDetail"
                data-id="{{item.goodsId}}"
              ></image>
            </view>

            <!-- 商品详情 -->
            <view class="item-details">
              <view class="goods-name">{{item.name}}</view>
              <view class="goods-specs" wx:if="{{item.specifications && (item.specifications.color || item.specifications.size)}}">
                规格: 
                <text wx:if="{{item.specifications.color}}">{{item.specifications.color}}</text>
                <text wx:if="{{item.specifications.color && item.specifications.size}}">, </text>
                <text wx:if="{{item.specifications.size}}">{{item.specifications.size}}</text>
              </view>
              <view class="goods-price">¥{{item.price}}</view>

              <!-- 库存提示 -->
              <view class="stock-info" wx:if="{{item.stock !== undefined}}">
                <text class="stock-text {{item.stock === 0 ? 'out-of-stock' : (item.stock < 10 ? 'low-stock' : '')}}">
                  {{item.stock === 0 ? '库存不足' : (item.stock < 10 ? '库存紧张（剩余' + item.stock + '件）' : '库存充足')}}
                </text>
              </view>

              <!-- 数量调整区域 -->
              <view class="quantity-controls">
                <view 
                  class="quantity-btn decrease {{item.quantity <= 1 ? 'disabled' : ''}}"
                  bindtap="onDecreaseQuantity"
                  data-index="{{index}}"
                >
                  <text class="quantity-icon">-</text>
                </view>
                <view class="quantity-display">{{item.quantity}}</view>
                <view
                  class="quantity-btn increase {{(item.stock !== undefined && item.quantity >= item.stock) ? 'disabled' : ''}}"
                  bindtap="onIncreaseQuantity"
                  data-index="{{index}}"
                >
                  <text class="quantity-icon">+</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 删除按钮 -->
          <view 
            class="item-delete"
            bindtap="onDeleteItem"
            data-index="{{index}}"
          >
            <text class="delete-icon">×</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-cart" wx:if="{{cartList.length === 0 && !loading}}">
      <image src="/assets/images/empty-cart.png" class="empty-image" mode="aspectFit"></image>
      <text class="empty-title">购物车还是空的</text>
      <text class="empty-subtitle">快去添加你喜欢的商品吧</text>
      <button class="empty-action-btn" bindtap="onGoShopping">
        去逛逛
      </button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 底部结算区域 -->
  <view class="checkout-bottom" wx:if="{{cartList.length > 0}}">
    <view class="checkout-info">
      <text class="total-label">合计: </text>
      <text class="total-price">¥{{totalPrice}}</text>
    </view>
    <view 
      class="checkout-btn {{selectedItems.length > 0 ? 'active' : 'disabled'}}"
      bindtap="onCheckout"
    >
      <text class="checkout-text">去结算({{selectedItems.length}})</text>
    </view>
  </view>
</view> 