import request from '@/utils/request'

// 获取订单列表
export function getOrderList (query) {
  return request({
    url: '/api/v1/admin/order/list',
    method: 'get',
    params: query
  })
}

// 获取订单详情
export function getOrderDetail (id) {
  return request({
    url: `/api/v1/admin/order/detail/${id}`,
    method: 'get'
  })
}

// 更新订单状态
export function updateOrderStatus (id, data) {
  return request({
    url: `/api/v1/admin/order/status/${id}`,
    method: 'put',
    data
  })
}

// 更新物流信息
export function updateOrderLogistics (id, data) {
  return request({
    url: `/api/v1/admin/order/logistics/${id}`,
    method: 'put',
    data
  })
}
