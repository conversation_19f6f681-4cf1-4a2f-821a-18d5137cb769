<template>
  <div class="app-container">
    <div v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="box-card">
            <div class="card-header">
              <span>WiFi码总数</span>
            </div>
            <div class="card-body">
              <div class="card-value">{{ stats.total || 0 }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card">
            <div class="card-header">
              <span>启用中</span>
            </div>
            <div class="card-body">
              <div class="card-value">{{ stats.active || 0 }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card">
            <div class="card-header">
              <span>已禁用</span>
            </div>
            <div class="card-body">
              <div class="card-value">{{ stats.inactive || 0 }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card">
            <div class="card-header">
              <span>总使用次数</span>
            </div>
            <div class="card-body">
              <div class="card-value">{{ stats.total_use_count || 0 }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-card class="chart-card">
        <div slot="header" class="clearfix">
          <span>WiFi码使用趋势</span>
          <el-radio-group v-model="timeRange" size="mini" style="float: right">
            <el-radio-button label="week">最近一周</el-radio-button>
            <el-radio-button label="month">最近一月</el-radio-button>
            <el-radio-button label="year">最近一年</el-radio-button>
          </el-radio-group>
        </div>
        <div class="chart-container" ref="chartContainer"></div>
      </el-card>

      <el-card class="top-wifi-card">
        <div slot="header" class="clearfix">
          <span>热门WiFi码排行</span>
        </div>
        <el-table :data="topWifiList" style="width: 100%" border>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="title" label="WiFi标题" />
          <el-table-column prop="name" label="WiFi名称" />
          <el-table-column prop="merchant_name" label="商户名称" />
          <el-table-column prop="use_count" label="使用次数" sortable />
          <el-table-column label="操作" width="120" align="center">
            <template slot-scope="{row}">
              <el-button type="primary" size="mini" @click="viewDetail(row.id)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getWifiStats } from '@/api/wifi'
// 引入echarts
import * as echarts from 'echarts/lib/echarts'
import 'echarts/lib/chart/line'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/title'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/grid'

export default {
  name: 'WiFiStats',
  data () {
    return {
      loading: true,
      stats: {
        total: 0,
        active: 0,
        inactive: 0,
        total_use_count: 0
      },
      timeRange: 'week',
      topWifiList: [],
      chart: null,
      trendData: []
    }
  },
  watch: {
    timeRange () {
      this.fetchData()
    }
  },
  mounted () {
    this.fetchData()
    window.addEventListener('resize', this.resizeChart)
  },
  beforeDestroy () {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    fetchData () {
      this.loading = true
      console.log('Fetching stats with time range:', this.timeRange)
      
      getWifiStats({ time_range: this.timeRange }).then(response => {
        console.log('Stats response:', response)
        
        if (response.data) {
          this.stats = response.data.stats || {
            total: 0,
            active: 0,
            inactive: 0,
            total_use_count: 0
          }
          
          this.trendData = response.data.trend_data || []
          this.topWifiList = response.data.top_wifi_list || []
          
          this.$nextTick(() => {
            this.initChart(this.trendData)
          })
        }
      }).catch(error => {
        console.error('Failed to fetch stats:', error)
      }).finally(() => {
        this.loading = false
      })
    },
    initChart (trendData) {
      if (!this.$refs.chartContainer) {
        console.error('Chart container not found')
        return
      }
      
      if (this.chart) {
        this.chart.dispose()
      }
      
      try {
        this.chart = echarts.init(this.$refs.chartContainer)

        const dates = trendData.map(item => item.date)
        const values = trendData.map(item => item.count)

        const option = {
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: dates
          },
          yAxis: {
            type: 'value',
            name: '使用次数'
          },
          series: [{
            name: 'WiFi使用次数',
            type: 'line',
            smooth: true,
            data: values,
            areaStyle: {
              opacity: 0.3
            },
            itemStyle: {
              color: '#409EFF'
            }
          }]
        }

        this.chart.setOption(option)
      } catch (error) {
        console.error('Failed to initialize chart:', error)
      }
    },
    resizeChart () {
      if (this.chart) {
        this.chart.resize()
      }
    },
    viewDetail (id) {
      this.$router.push(`/wifi/detail/${id}`)
    }
  }
}
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
.card-header {
  font-size: 14px;
  color: #606266;
}
.card-body {
  padding: 20px 0;
  text-align: center;
}
.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}
.chart-card {
  margin-bottom: 20px;
}
.chart-container {
  height: 400px;
  width: 100%;
}
.top-wifi-card {
  margin-bottom: 20px;
}
</style>
