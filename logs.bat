@echo off
chcp 65001 >nul
echo === WiFi共享管理后台日志查看工具 ===
echo.

:: 检查日志目录是否存在
if not exist "logs" (
    echo 日志目录不存在，请先启动应用
    pause
    exit /b 1
)

:: 显示可用的日志文件
echo 可用的日志文件:
dir logs /b

echo.
echo 选择要查看的日志:
echo 1. 综合日志 (wifi-share-admin.log)
echo 2. 信息日志 (info.log)
echo 3. 警告日志 (warn.log)
echo 4. 错误日志 (error.log)
echo 5. PM2输出日志 (wifi-share-admin-out.log)
echo 6. PM2错误日志 (wifi-share-admin-error.log)
echo 7. 实时查看PM2日志
echo.

set /p choice=请输入选项 (1-7): 

if "%choice%"=="1" (
    echo === 综合日志 ===
    if exist "logs\wifi-share-admin.log" (
        type logs\wifi-share-admin.log
    ) else (
        echo 日志文件不存在
    )
) else if "%choice%"=="2" (
    echo === 信息日志 ===
    if exist "logs\info.log" (
        type logs\info.log
    ) else (
        echo 日志文件不存在
    )
) else if "%choice%"=="3" (
    echo === 警告日志 ===
    if exist "logs\warn.log" (
        type logs\warn.log
    ) else (
        echo 日志文件不存在
    )
) else if "%choice%"=="4" (
    echo === 错误日志 ===
    if exist "logs\error.log" (
        type logs\error.log
    ) else (
        echo 日志文件不存在
    )
) else if "%choice%"=="5" (
    echo === PM2输出日志 ===
    if exist "logs\wifi-share-admin-out.log" (
        type logs\wifi-share-admin-out.log
    ) else (
        echo 日志文件不存在
    )
) else if "%choice%"=="6" (
    echo === PM2错误日志 ===
    if exist "logs\wifi-share-admin-error.log" (
        type logs\wifi-share-admin-error.log
    ) else (
        echo 日志文件不存在
    )
) else if "%choice%"=="7" (
    echo === 实时查看PM2日志 (按Ctrl+C退出) ===
    pm2 logs wifi-share-admin
) else (
    echo 无效选项
)

echo.
pause
