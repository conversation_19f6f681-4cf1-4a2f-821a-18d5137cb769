import request from '@/utils/request'

// 获取商品列表
export function getGoodsList(params) {
  return request({
    url: '/api/v1/admin/goods/list',
    method: 'get',
    params
  })
}

// 获取商品详情
export function getGoodsDetail(id) {
  return request({
    url: `/api/v1/admin/goods/detail/${id}`,
    method: 'get'
  })
}

// 创建商品
export function createGoods(data) {
  return request({
    url: '/api/v1/admin/goods/create',
    method: 'post',
    data
  })
}

// 更新商品
export function updateGoods(id, data) {
  return request({
    url: `/api/v1/admin/goods/update/${id}`,
    method: 'put',
    data
  })
}

// 更新商品状态
export function updateGoodsStatus(id, data) {
  return request({
    url: `/api/v1/admin/goods/status/${id}`,
    method: 'put',
    data
  })
}

// 删除商品
export function deleteGoods(id) {
  console.log(`删除商品 ID: ${id}, 请求URL: /api/v1/admin/goods/delete/${id}`)
  return request({
    url: `/api/v1/admin/goods/delete/${id}`,
    method: 'delete'
  })
}

// 格式化图片URL，确保前缀正确
export function formatImageUrl(url) {
  if (!url) {
    return '/uploads/default-goods.jpg'
  }
  
  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  
  // 如果是相对路径，添加前缀
  if (url.startsWith('/')) {
    return url
  } else {
    return `/uploads/${url}`
  }
}
