const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

// 是否为生产环境
const isProduction = process.env.NODE_ENV === 'production'

module.exports = {
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  devServer: {
    port: 8081,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/api': {
        target: 'http://localhost:4000',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        },
        // 增加代理配置，处理大型请求体
        proxyTimeout: 120000, // 2分钟超时
        timeout: 120000,
        // 添加日志调试
        logLevel: 'debug'
      },
      // 添加对上传图片的代理
      '/uploads': {
        target: 'http://localhost:4000',
        changeOrigin: true
      }
    },
    // 使用before函数代替setupMiddlewares
    before: function(app) {
      // 增加请求体大小限制
      app.use((req, res, next) => {
        req.maxBodyLength = 50 * 1024 * 1024; // 50MB
        req.maxContentLength = 50 * 1024 * 1024; // 50MB
        next();
      });
    }
  },
  configureWebpack: {
    // 路径别名
    resolve: {
      alias: {
        '@': resolve('src')
      }
    }
  },
  chainWebpack(config) {
    // 移除 prefetch 插件，减少首屏加载带宽
    config.plugins.delete('prefetch')

    // 设置 svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    // 设置保留空格
    config.module
      .rule('vue')
      .use('vue-loader')
      .loader('vue-loader')
      .tap(options => {
        options.compilerOptions.preserveWhitespace = true
        return options
      })
      .end()

    // 生产环境配置
    config
      .when(process.env.NODE_ENV === 'production',
        config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
              inline: /runtime\..*\.js$/
            }])
            .end()
          config
            .optimization.splitChunks({
              chunks: 'all',
              cacheGroups: {
                libs: {
                  name: 'chunk-libs',
                  test: /[\\/]node_modules[\\/]/,
                  priority: 10,
                  chunks: 'initial'
                },
                elementUI: {
                  name: 'chunk-elementUI',
                  priority: 20,
                  test: /[\\/]node_modules[\\/]_?element-ui(.*)/
                },
                commons: {
                  name: 'chunk-commons',
                  test: resolve('src/components'),
                  minChunks: 3,
                  priority: 5,
                  reuseExistingChunk: true
                }
              }
            })
          // 修复 runtime.js 404 问题
          config.optimization.runtimeChunk({
            name: 'runtime'
          })
        }
      )
  }
}
