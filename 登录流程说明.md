# WiFi共享商业系统 - 登录流程说明

## 登录方案：wx.login + getUserProfile 组合方式

我们采用了微信官方推荐的 `wx.login` + `getUserProfile` 组合方式来实现小程序的登录流程，这是一种简单且功能完整的登录方案。

### 登录流程

1. **基础登录（应用启动时）**
   - 小程序启动时，自动调用 `wx.login` 获取临时登录凭证 code
   - 将 code 发送到后端服务器，换取用户的 openid 和 session_key
   - 后端生成 token 并返回给小程序
   - 小程序保存 token，完成基础登录

2. **完整登录（需要用户信息时）**
   - 当需要获取用户信息时，调用 `wx.getUserProfile` 获取用户头像、昵称等信息
   - 同时重新调用 `wx.login` 获取新的临时登录凭证 code
   - 将 code 和用户信息一起发送到后端服务器
   - 后端更新用户信息，生成新的 token 并返回给小程序
   - 小程序保存 token 和用户信息，完成完整登录

### 实现优势

1. **简化流程**：将原来的"静默登录+业务登录"两步流程简化为一步或两步（根据需要）
2. **减少请求**：减少了前后端交互次数，提高了登录效率
3. **提升体验**：用户只需在必要时才授权个人信息，符合微信官方最佳实践
4. **安全可靠**：每次获取用户信息时都重新获取 code，确保安全性

### 关键API

#### 前端

```javascript
// 基础登录（不需要用户信息）
wx.login({
  success: (res) => {
    const code = res.code;
    // 发送 code 到后端
  }
});

// 完整登录（需要用户信息）
wx.getUserProfile({
  desc: '用于完善用户资料',
  success: (res) => {
    const userInfo = res.userInfo;
    wx.login({
      success: (loginRes) => {
        const code = loginRes.code;
        // 发送 code 和 userInfo 到后端
      }
    });
  }
});
```

#### 后端

```javascript
// 统一的登录接口
router.post('/wechat/login', async (req, res) => {
  const { code, userInfo } = req.body;
  
  // 1. 使用 code 获取 openid 和 session_key
  // 2. 查找或创建用户
  // 3. 如果提供了 userInfo，更新用户信息
  // 4. 生成 token 并返回
});
```

### 使用说明

1. 小程序启动时会自动进行基础登录，无需用户操作
2. 当用户需要访问个人数据或执行需要身份验证的操作时，会提示用户进行完整登录
3. 用户点击"登录"按钮后，会弹出微信授权窗口，用户同意后即可完成完整登录

## 注意事项

1. 用户拒绝授权个人信息时，仍可使用基础功能，但无法使用需要用户信息的功能
2. token 有效期为 7 天，过期后需要重新登录
3. 如遇到登录问题，可尝试重启小程序或清除缓存后重试 