(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-62d8c230"],{"2b3f":function(e,t,s){"use strict";s.d(t,"e",(function(){return r})),s.d(t,"h",(function(){return i})),s.d(t,"b",(function(){return n})),s.d(t,"k",(function(){return c})),s.d(t,"d",(function(){return o})),s.d(t,"l",(function(){return u})),s.d(t,"g",(function(){return m})),s.d(t,"a",(function(){return d})),s.d(t,"i",(function(){return p})),s.d(t,"c",(function(){return l})),s.d(t,"j",(function(){return h})),s.d(t,"f",(function(){return g}));var a=s("b775");function r(e){return e?e.startsWith("http://")||e.startsWith("https://")||e.startsWith("/")?e:"/uploads/"+e:"/uploads/default-ad.jpg"}function i(e){return Object(a["a"])({url:"/api/v1/admin/advertisement/spaces",method:"get",params:e})}function n(e){return Object(a["a"])({url:"/api/v1/admin/advertisement/spaces",method:"post",data:e})}function c(e,t){return Object(a["a"])({url:"/api/v1/admin/advertisement/spaces/"+e,method:"put",data:t})}function o(e){return Object(a["a"])({url:"/api/v1/admin/advertisement/spaces/"+e,method:"delete"})}function u(e,t){return Object(a["a"])({url:"/api/v1/admin/advertisement/spaces/"+e,method:"put",data:{status:t}})}function m(e){return Object(a["a"])({url:"/api/v1/admin/advertisement/contents",method:"get",params:e})}function d(e){return Object(a["a"])({url:"/api/v1/admin/advertisement/contents",method:"post",data:e})}function p(e,t){return Object(a["a"])({url:"/api/v1/admin/advertisement/contents/"+e,method:"put",data:t})}function l(e){return Object(a["a"])({url:"/api/v1/admin/advertisement/contents/"+e,method:"delete"})}function h(e,t){const s=t&&void 0!==t.status?t.status:0;return Object(a["a"])({url:"/api/v1/admin/advertisement/contents/"+e,method:"put",data:{status:s}})}function g(e){return Object(a["a"])({url:"/api/v1/admin/advertisement/contents/"+e,method:"get"})}},"2c4e":function(e,t,s){},"51bb":function(e,t,s){"use strict";s("2c4e")},"89d2":function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"app-container"},[t("el-card",{staticClass:"box-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(e.isEdit?"编辑广告位":"创建广告位"))])]),t("el-form",{ref:"spaceForm",attrs:{model:e.spaceForm,rules:e.rules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"广告位名称",prop:"name"}},[t("el-input",{attrs:{placeholder:"请输入广告位名称"},model:{value:e.spaceForm.name,callback:function(t){e.$set(e.spaceForm,"name",t)},expression:"spaceForm.name"}})],1),t("el-form-item",{attrs:{label:"广告位编码",prop:"code"}},[t("el-input",{attrs:{placeholder:"请输入广告位编码",disabled:e.isEdit},model:{value:e.spaceForm.code,callback:function(t){e.$set(e.spaceForm,"code",t)},expression:"spaceForm.code"}}),t("div",{staticClass:"tip"},[e._v("广告位编码一经创建不可修改，如：home_banner")])],1),t("el-form-item",{attrs:{label:"宽度(px)",prop:"width"}},[t("el-input-number",{attrs:{min:1,max:2e3},model:{value:e.spaceForm.width,callback:function(t){e.$set(e.spaceForm,"width",t)},expression:"spaceForm.width"}})],1),t("el-form-item",{attrs:{label:"高度(px)",prop:"height"}},[t("el-input-number",{attrs:{min:1,max:2e3},model:{value:e.spaceForm.height,callback:function(t){e.$set(e.spaceForm,"height",t)},expression:"spaceForm.height"}})],1),t("el-form-item",{attrs:{label:"价格/天",prop:"price"}},[t("el-input-number",{attrs:{min:0,precision:2,step:10},model:{value:e.spaceForm.price,callback:function(t){e.$set(e.spaceForm,"price",t)},expression:"spaceForm.price"}})],1),t("el-form-item",{attrs:{label:"描述",prop:"description"}},[t("el-input",{attrs:{type:"textarea",placeholder:"请输入广告位描述",rows:3},model:{value:e.spaceForm.description,callback:function(t){e.$set(e.spaceForm,"description",t)},expression:"spaceForm.description"}})],1),t("el-form-item",{attrs:{label:"状态",prop:"status"}},[t("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-text":"启用","inactive-text":"禁用"},model:{value:e.spaceForm.status,callback:function(t){e.$set(e.spaceForm,"status",t)},expression:"spaceForm.status"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("保存")]),t("el-button",{on:{click:e.goBack}},[e._v("返回")])],1)],1)],1)],1)},r=[],i=(s("d9e2"),s("14d9"),s("2b3f")),n=s("b775"),c={name:"AdSpaceForm",data(){const e=(e,t,s)=>{/^[a-z0-9_]+$/.test(t)?s():s(new Error("广告位编码只能包含小写字母、数字和下划线"))};return{isEdit:!1,spaceId:void 0,spaceForm:{name:"",code:"",width:300,height:200,description:"",price:0,status:1},rules:{name:[{required:!0,message:"请输入广告位名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],code:[{required:!0,message:"请输入广告位编码",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"},{validator:e,trigger:"blur"}],width:[{required:!0,message:"请输入宽度",trigger:"blur"},{type:"number",message:"宽度必须为数字",trigger:"blur"}],height:[{required:!0,message:"请输入高度",trigger:"blur"},{type:"number",message:"高度必须为数字",trigger:"blur"}],price:[{required:!0,message:"请输入价格",trigger:"blur"},{type:"number",message:"价格必须为数字",trigger:"blur"}],description:[{max:255,message:"长度不能超过 255 个字符",trigger:"blur"}]}}},created(){const e=this.$route.params&&this.$route.params.id;e&&(this.isEdit=!0,this.spaceId=parseInt(e),this.getDetail(this.spaceId))},methods:{getDetail(e){this.loading=!0,Object(n["a"])({url:"/api/v1/admin/advertisement/spaces/"+e,method:"get"}).then(e=>{"success"===e.status?this.spaceForm=e.data:(this.$message.error(e.message||"获取广告位详情失败"),this.goBack())}).catch(e=>{const t=e.response&&e.response.data&&e.response.data.message?e.response.data.message:"获取广告位详情失败";this.$message.error(t),this.goBack()}).finally(()=>{this.loading=!1})},submitForm(){this.$refs.spaceForm.validate(e=>{if(e){const e={...this.spaceForm};this.isEdit?Object(i["k"])(this.spaceId,e).then(e=>{"success"===e.status?(this.$message.success("更新成功"),this.goBack()):this.$message.error(e.message||"更新失败")}).catch(e=>{const t=e.response&&e.response.data&&e.response.data.message?e.response.data.message:"更新失败";this.$message.error(t)}):Object(i["b"])(e).then(e=>{"success"===e.status?(this.$message.success("创建成功"),this.goBack()):this.$message.error(e.message||"创建失败")}).catch(e=>{const t=e.response&&e.response.data&&e.response.data.message?e.response.data.message:"创建失败";this.$message.error(t)})}})},goBack(){this.$router.push("/ad/space")}}},o=c,u=(s("51bb"),s("2877")),m=Object(u["a"])(o,a,r,!1,null,"f4db36ec",null);t["default"]=m.exports}}]);