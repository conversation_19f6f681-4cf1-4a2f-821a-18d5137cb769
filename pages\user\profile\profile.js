// pages/user/profile/profile.js
const userService = require('../../../services/user')
const { STORAGE_KEYS, PAGES } = require('../../../utils/constants')
const config = require('../../../config/config') // Added missing import

Page({
  data: {
    // 用户信息
    userInfo: {},
    isLoggedIn: false,
    isProfileCompleted: false,
    isDemote: false, // 是否为降级用户（拒绝授权）
    
    // 订单统计
    orderStats: {
      pending: 0,     // 待付款
      shipped: 0,     // 待发货
      delivering: 0,  // 待收货
      completed: 0    // 已完成
    },
    
    // 消息数量
    messageCount: 0,
    
    // 加载状态
    loading: false,

    // 临时用户信息（用于编辑）
    tempUserInfo: {
      avatar: '',
      nickname: ''
    },
    
    // 广告配置
    adUnitId: 'adunit-xxxxxxxxxxxxxxxx' // 替换为实际的广告单元ID
  },

  onLoad: function (options) {
    console.log('我的页面加载')
    this.checkLoginStatus()
  },

  onShow: function () {
    // 每次显示页面时刷新数据
    this.checkLoginStatus()
    if (this.data.isLoggedIn) {
      this.loadUserData()
    }
    
    // 微信小程序使用的是原生TabBar，不需要设置active状态
    // 如果将来使用自定义TabBar，可以取消下面的注释
    /*
    try {
      if (typeof this.getTabBar === 'function') {
        const tabBar = this.getTabBar();
        if (tabBar && typeof tabBar.setData === 'function') {
          tabBar.setData({
            active: 3 // "我的"是第4个tab，索引为3（首页0，商城1，购物车2，我的3）
          });
        }
      }
    } catch (error) {
      console.error('设置TabBar失败:', error);
    }
    */
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const app = getApp();
    
    if (!app) {
      console.error('获取app实例失败');
      return;
    }
    
    // 获取全局登录状态
    const isLoggedIn = app.globalData.isLogin;
    const userInfo = app.globalData.userInfo;
    const isProfileCompleted = app.globalData.isProfileCompleted;
    const isDemote = app.globalData.isDemote;
    
    console.log('检查登录状态:', 
      '已登录:', isLoggedIn, 
      '资料完整:', isProfileCompleted, 
      '降级用户:', isDemote, 
      '用户信息:', userInfo);
    
    // 设置页面状态
      this.setData({
      isLoggedIn,
      isProfileCompleted,
      isDemote,
      userInfo: userInfo || {
        nickname: '微信用户',
        avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132'
      }
    });
    
    // 如果已登录，设置临时用户信息（用于编辑）
    if (isLoggedIn && userInfo) {
      this.setData({
        tempUserInfo: {
          avatar: userInfo.avatar || '',
          nickname: userInfo.nickname || ''
        }
      });
      
      // 如果已登录且资料完整，加载用户数据
      if (isProfileCompleted) {
        this.loadUserData();
      }
    }
    
    // 注册登录状态变化回调
    app.loginStateCallback = (isLogin) => {
      console.log('登录状态变化:', isLogin);
      if (isLogin) {
        this.checkLoginStatus();
      }
    };
  },

  // 加载用户数据
  async loadUserData() {
    try {
      this.setData({ loading: true });
      console.log('开始加载用户数据...');
      
      // 并行加载用户信息、订单统计、消息数量
      const [userInfoRes, orderStatsRes, messageRes] = await Promise.allSettled([
        this.getUserInfo(),
        this.getOrderStats(),
        this.getMessageCount()
      ]);
      
      console.log('所有数据加载完成，开始处理结果');
      
      // 处理用户信息
      if (userInfoRes.status === 'fulfilled' && userInfoRes.value.success) {
        // 确保用户数据有效
        const userData = userInfoRes.value.data;
        console.log('获取到的用户信息:', JSON.stringify(userData));
        
        // 检查是否为降级用户（拒绝授权）
        const isDemote = userData.is_demote === true;
        this.setData({ isDemote: isDemote });
        
        // 确保头像URL是完整的
        if (userData && userData.avatar) {
          // 如果头像不是以http开头，且不是本地资源，则添加域名
          if (!userData.avatar.startsWith('http') && !userData.avatar.startsWith('/assets/')) {
            userData.avatar = userData.avatar.startsWith('/') 
              ? `${config.api.baseUrl}${userData.avatar}`
              : `${config.api.baseUrl}/${userData.avatar}`;
          }
          console.log('处理后的头像URL:', userData.avatar);
          
          // 预加载头像
          wx.getImageInfo({
            src: userData.avatar,
            success: (res) => {
              console.log('头像预加载成功:', res.width, 'x', res.height);
            },
            fail: (err) => {
              console.error('头像预加载失败:', err);
              // 使用网络上可以访问的默认头像
              userData.avatar = 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132';
              this.setData({ userInfo: userData });
              
              // 更新本地存储
              wx.setStorageSync(STORAGE_KEYS.USER_INFO, userData);
            }
          });
        } else {
          // 设置默认头像，使用网络上可以访问的微信头像
          userData.avatar = 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132';
          console.log('使用默认头像');
        }
        
        // 设置临时用户信息，用于编辑
        const tempUserInfo = {
          avatar: userData.avatar || '',
          nickname: userData.nickname || ''
        };
        
        this.setData({
          userInfo: userData,
          tempUserInfo: tempUserInfo
        });
        
        // 更新本地存储
        wx.setStorageSync(STORAGE_KEYS.USER_INFO, userData);
        
        // 更新全局状态
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.userInfo = userData;
          app.globalData.isDemote = isDemote;
        }
      } else if (userInfoRes.status === 'rejected') {
        console.error('获取用户信息失败:', userInfoRes.reason);
      }
      
      // 处理订单统计
      if (orderStatsRes.status === 'fulfilled' && orderStatsRes.value.success) {
        this.setData({
          orderStats: orderStatsRes.value.data
        });
      }
      
      // 处理消息数量
      if (messageRes.status === 'fulfilled' && messageRes.value.success) {
        this.setData({
          messageCount: messageRes.value.data.unread_count || 0
        });
      }
      
    } catch (error) {
      console.error('加载用户数据失败:', error);
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 获取用户信息
  getUserInfo() {
    return userService.getUserInfo()
  },

  // 获取订单统计
  getOrderStats() {
    return new Promise((resolve) => {
      // 暂时返回模拟数据
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            pending: 2,     // 待付款
            shipped: 1,     // 待发货  
            delivering: 3,  // 待收货
            completed: 15   // 已完成
          }
        })
      }, 500)
    })
  },

  // 获取消息数量
  getMessageCount() {
    return new Promise((resolve) => {
      // 暂时返回模拟数据
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            unread_count: 5
          }
        })
      }, 300)
    })
  },

  // 登录
  onLogin() {
    console.log('点击登录按钮，开始登录');
    
    // 显示加载提示
    wx.showLoading({
      title: '登录中...',
      mask: true
    });
    
    // 获取app实例
    const app = getApp();
    if (!app) {
      wx.hideLoading();
      wx.showToast({
        title: '获取app实例失败',
        icon: 'none'
      });
      return;
    }
    
    // 使用静默登录方式
    app.silentLogin()
      .then(res => {
        // 隐藏加载提示
        wx.hideLoading();
        
        if (res && res.status === 'success') {
          // 登录成功
          const userData = res.data.user || {
            nickname: '微信用户',
            avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132'
          };
          
          // 检查用户资料是否完整
          const hasNickname = !!(userData.nickname && userData.nickname !== '微信用户');
          const hasAvatar = !!(userData.avatar);
          const isProfileCompleted = hasNickname && hasAvatar;
          const isDemote = userData.is_demote === true;
          
          console.log('登录成功，用户资料完整性:', 
            '昵称:', hasNickname, 
            '头像:', hasAvatar, 
            '资料完整:', isProfileCompleted, 
            '降级用户:', isDemote);
          
          // 设置页面状态
          this.setData({
            isLoggedIn: true,
            isProfileCompleted: isProfileCompleted,
            isDemote: isDemote,
            userInfo: userData,
            tempUserInfo: {
              avatar: userData.avatar || '',
              nickname: userData.nickname || ''
            }
          });
          
          // 更新全局状态
          app.globalData.isProfileCompleted = isProfileCompleted;
          app.globalData.isDemote = isDemote;
          
          // 提示登录成功
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });
          
          // 如果用户资料完整，加载用户数据
          if (isProfileCompleted) {
            this.loadUserData();
          } else {
            // 如果用户资料不完整，提示用户完善资料
            wx.showModal({
              title: '提示',
              content: '请完善您的头像和昵称，以便获得更好的使用体验',
              showCancel: false,
              confirmText: '我知道了'
            });
          }
        } else {
          console.error('登录失败:', res);
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
        }
      })
      .catch(error => {
        console.error('登录失败:', error);
        wx.hideLoading();
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      });
  },

  // 选择头像
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail;
    console.log('用户选择了新头像:', avatarUrl);
    
    // 更新临时数据
    this.setData({
      'tempUserInfo.avatar': avatarUrl
    });
  },

  // 输入昵称
  onInputNickname(e) {
    const nickname = e.detail.value;
    console.log('用户输入了新昵称:', nickname);
    
    // 更新临时数据
    this.setData({
      'tempUserInfo.nickname': nickname
    });
  },

  // 保存用户信息
  async onSaveUserInfo() {
    const { tempUserInfo } = this.data;
    
    // 验证数据
    if (!tempUserInfo.avatar) {
      wx.showToast({
        title: '请选择头像',
        icon: 'none'
      });
      return;
    }
    
    if (!tempUserInfo.nickname || tempUserInfo.nickname === '微信用户') {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载提示
    wx.showLoading({
      title: '保存中...',
      mask: true
    });
    
    try {
      // 上传头像（如果是本地临时文件）
      let avatarUrl = tempUserInfo.avatar;
      if (avatarUrl.startsWith('wxfile://') || avatarUrl.startsWith('http://tmp')) {
        try {
          // 上传头像到服务器
          const uploadRes = await this.uploadAvatar(avatarUrl);
          if (uploadRes && uploadRes.success && uploadRes.data && uploadRes.data.url) {
            avatarUrl = uploadRes.data.url;
            console.log('头像上传成功，URL:', avatarUrl);
          } else {
            console.error('头像上传失败，使用默认头像');
            // 使用默认头像
            avatarUrl = 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132';
          }
        } catch (uploadError) {
          console.error('头像上传异常:', uploadError);
          // 使用默认头像
          avatarUrl = 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132';
        }
      }
      
      // 更新用户信息
      const updateData = {
        avatar: avatarUrl,
        nickname: tempUserInfo.nickname
      };
      
      console.log('准备更新用户信息:', JSON.stringify(updateData));
      
      // 调用更新用户信息接口
      const updateRes = await userService.updateUserInfo(updateData);
      
      // 隐藏加载提示
      wx.hideLoading();
      
      if (updateRes && updateRes.success) {
        // 更新本地数据
        const userInfo = { ...this.data.userInfo, ...updateData };
        
        this.setData({
          userInfo,
          isProfileCompleted: true,
          isDemote: false
        });
        
        // 更新本地存储
        wx.setStorageSync(STORAGE_KEYS.USER_INFO, userInfo);
        
        // 更新全局状态
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.userInfo = userInfo;
          app.globalData.isProfileCompleted = true;
          app.globalData.isDemote = false;
        }
        
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      } else {
        console.error('更新用户信息失败:', updateRes);
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('保存用户信息失败:', error);
      
      // 隐藏加载提示
      wx.hideLoading();
      
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  },

  // 上传头像
  uploadAvatar(filePath) {
    return new Promise((resolve, reject) => {
      // 显示上传中提示
      wx.showLoading({
        title: '上传中...',
        mask: true
      });
      
      wx.uploadFile({
        // 修改为正确的上传路径
        url: `${config.api.baseUrl}/api/v1/client/upload`,
        filePath,
        name: 'file',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync(STORAGE_KEYS.TOKEN)}`
        },
      success: (res) => {
          // 隐藏上传中提示
          wx.hideLoading();
          
          try {
            // 尝试解析返回结果
            const data = JSON.parse(res.data);
            
            // 检查返回结果是否正确（兼容code和status两种格式）
            if (data && (data.status === 'success' || data.code === 200) && data.data && data.data.url) {
              resolve({
                success: true,
                data: data.data
              });
            } else {
              console.error('上传头像失败，服务器返回:', data);
              reject(new Error('上传头像失败，服务器返回错误'));
            }
          } catch (e) {
            console.error('解析上传结果失败:', e, '原始数据:', res.data);
            reject(new Error('解析上传结果失败'));
          }
        },
        fail: (err) => {
          // 隐藏上传中提示
          wx.hideLoading();
          console.error('上传头像请求失败:', err);
          reject(err);
        }
      });
    });
  },

  // 执行降级登录（用户拒绝授权时调用）
  async performDemoteLogin() {
    let loadingShown = false;
    
    try {
      // 显示加载提示
      wx.showLoading({ title: '登录中...', mask: true });
      loadingShown = true;
      
      console.log('开始执行降级登录（无用户信息）');
      
      // 获取app实例
      const app = getApp();
      if (!app) {
        throw new Error('获取app实例失败');
      }
      
      // 调用app中的降级登录方法
      const res = await app.silentLogin(true); // 传入true表示这是一个降级登录
      
      // 确保隐藏loading
      if (loadingShown) {
        wx.hideLoading();
        loadingShown = false;
      }
      
      if (res && res.status === 'success') {
        // 更新页面状态
        this.setData({
          isLoggedIn: true,
          isProfileCompleted: false,
          isDemote: true,
          userInfo: res.data.user || {
            nickname: '微信用户',
            avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132'
          }
        });
        
        console.log('降级登录成功，使用默认用户信息');
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
        
        // 加载用户数据
        this.loadUserData();
      } else {
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('降级登录失败:', error);
      
      // 确保隐藏loading
      if (loadingShown) {
        wx.hideLoading();
        loadingShown = false;
      }
      
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 执行登录
  async performLogin(userInfo) {
    let loadingShown = false;
    
    try {
      // 显示加载提示
      wx.showLoading({ title: '登录中...', mask: true });
      loadingShown = true;
      
      console.log('开始执行登录，用户信息:', JSON.stringify(userInfo));
      
      // 获取app实例
      const app = getApp();
      if (!app) {
        throw new Error('获取app实例失败');
      }
      
      // 调用app中的完整登录方法
      const res = await app.doLoginWithUserInfo(userInfo);
      
      // 确保隐藏loading
      if (loadingShown) {
        wx.hideLoading();
        loadingShown = false;
      }
      
      if (res && res.status === 'success') {
        // 更新页面状态
        this.setData({
          isLoggedIn: true,
          isProfileCompleted: true,
          isDemote: false,
          userInfo: res.data.user
        });
        
        console.log('登录成功，获取到的用户信息:', JSON.stringify(res.data.user));
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
        
        // 加载用户数据
        this.loadUserData();
      } else {
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('登录失败:', error);
      
      // 确保隐藏loading
      if (loadingShown) {
        wx.hideLoading();
        loadingShown = false;
      }
      
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          wx.removeStorageSync(STORAGE_KEYS.TOKEN)
          wx.removeStorageSync(STORAGE_KEYS.USER_INFO)
          
          // 更新全局状态
          const app = getApp();
          if (app && app.globalData) {
            app.globalData.token = null
            app.globalData.userInfo = null
            app.globalData.isLogin = false
            app.globalData.isProfileCompleted = false
            app.globalData.isDemote = false
          }
          
          this.setData({
            isLoggedIn: false,
            isProfileCompleted: false,
            isDemote: false,
            userInfo: {},
            tempUserInfo: {
              avatar: '',
              nickname: ''
            },
            orderStats: {
              pending: 0,
              shipped: 0,
              delivering: 0,
              completed: 0
            },
            messageCount: 0
          })
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 点击广告
  onAdClick() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    wx.navigateTo({ url: PAGES.ADS })
  },

  // 查看全部订单
  onViewAllOrders() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    wx.navigateTo({ url: '/pages/mall/order/list/list' })
  },

  // 按状态查看订单
  onViewOrdersByStatus(e) {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    const status = e.currentTarget.dataset.status
    wx.navigateTo({ url: `/pages/mall/order/list/list?status=${status}` })
  },

  // 跳转到钱包页面
  onNavigateToWallet() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    wx.navigateTo({ url: '/pages/user/wallet/wallet' })
  },

  // 跳转到团队页面
  onNavigateToTeam() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    wx.navigateTo({ url: '/pages/user/team/team' })
  },

  // 联系客服
  onContactService() {
    // 使用微信客服功能，不需要登录验证
    // 使用微信开放能力，打开客服会话
    wx.showToast({
      title: '正在连接客服...',
      icon: 'loading',
      duration: 1000,
      success: () => {
        setTimeout(() => {
          // 提示用户通过按钮联系客服
    wx.showModal({
      title: '联系客服',
            content: '请点击"确定"按钮，然后通过客服按钮联系我们',
            confirmText: '确定',
      showCancel: false,
            success: (res) => {
              if (res.confirm) {
                console.log('用户确认联系客服');
              }
            }
          });
        }, 1000);
      }
    });
  },

  /**
   * 导航到帮助中心
   */
  onNavigateToHelp: function() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip();
      return;
    }
    
    // TODO: 实现帮助中心页面
    wx.showToast({
      title: '帮助中心开发中',
      icon: 'none'
    });
  },

  /**
   * 导航到收货地址管理
   */
  onNavigateToAddress: function() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip();
      return;
    }
    
    wx.navigateTo({
      url: '/pages/user/address/list'
    });
  },

  /**
   * 导航到消息中心
   */
  onNavigateToMessages: function() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip();
      return;
    }
    
    // TODO: 实现消息中心页面
    wx.showToast({
      title: '消息中心开发中',
      icon: 'none'
    });
  },

  // 邀请好友
  onInviteFriends() {
    if (!this.data.isLoggedIn) {
      this.showLoginTip()
      return
    }
    
    // 直接触发分享操作
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    
    // 显示分享提示
    wx.showModal({
      title: '邀请好友',
      content: '点击右上角"..."按钮，选择"转发"即可邀请好友',
      confirmText: '我知道了',
      showCancel: false
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: 'WiFi共享商业系统 - 邀请您加入',
      path: '/pages/index/index?inviter=' + (this.data.userInfo.id || ''),
      imageUrl: '/assets/images/share-home.jpg'
    }
  },

  // 广告加载成功
  onAdLoad() {
    console.log('广告加载成功');
  },

  // 广告加载失败
  onAdError(e) {
    console.error('广告加载失败', e.detail);
  },

  // 广告被点击
  onAdClick() {
    console.log('广告被点击');
  },

  // 显示登录提示
  showLoginTip() {
    wx.showModal({
      title: '提示',
      content: '请先登录',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          this.onLogin()
        }
      }
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.isLoggedIn) {
      this.loadUserData().then(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
  },

  // 头像加载错误处理
  onAvatarError(e) {
    console.error('头像加载失败，使用默认头像', e);
    const userInfo = this.data.userInfo;
    const oldAvatar = userInfo.avatar;
    
    // 使用网络上可以访问的默认头像
    userInfo.avatar = 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132';
    
    this.setData({
      userInfo: userInfo
    });
    
    // 更新本地存储
    wx.setStorageSync(STORAGE_KEYS.USER_INFO, userInfo);
    
    // 更新全局状态
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.userInfo = userInfo;
    }
    
    // 记录错误信息
    console.warn(`头像加载失败，原头像URL: ${oldAvatar}，已替换为默认头像`);
  }
}) 