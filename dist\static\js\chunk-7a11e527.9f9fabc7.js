(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7a11e527"],{"2fbe":function(t,s,a){},"38e2":function(t,s,a){"use strict";a("2fbe")},b808:function(t,s,a){"use strict";a.r(s);var e=function(){var t=this,s=t._self._c;return s("div",{staticClass:"app-container"},[s("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"goods-detail"},[s("el-card",{staticClass:"box-card"},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("商品详情")]),s("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.goBack}},[t._v("返回")])],1),s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:8}},[s("div",{staticClass:"goods-cover"},[s("el-image",{staticStyle:{width:"100%",height:"300px"},attrs:{src:t.formatImageUrl(t.detail.cover),fit:"contain"}},[s("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[s("i",{staticClass:"el-icon-picture-outline"})])])],1),t.imageList.length>0?s("div",{staticClass:"goods-images"},[s("el-carousel",{attrs:{interval:4e3,type:"card",height:"200px"}},t._l(t.imageList,(function(a,e){return s("el-carousel-item",{key:e},[s("el-image",{staticStyle:{width:"100%",height:"100%"},attrs:{src:t.formatImageUrl(a),fit:"contain"}})],1)})),1)],1):t._e()]),s("el-col",{attrs:{span:16}},[s("div",{staticClass:"goods-info"},[s("h2",{staticClass:"goods-title"},[t._v(t._s(t.detail.title))]),s("div",{staticClass:"goods-price"},[s("span",{staticClass:"price-label"},[t._v("售价：")]),s("span",{staticClass:"price-value"},[t._v("¥ "+t._s(t.detail.price))]),t.detail.original_price&&t.detail.original_price>t.detail.price?s("span",{staticClass:"original-price"},[t._v("¥ "+t._s(t.detail.original_price))]):t._e()]),s("div",{staticClass:"goods-item"},[s("span",{staticClass:"label"},[t._v("分类：")]),s("span",{staticClass:"value"},[t._v(t._s(t.getCategoryName(t.detail.category_id)))])]),s("div",{staticClass:"goods-item"},[s("span",{staticClass:"label"},[t._v("库存：")]),s("span",{staticClass:"value"},[t._v(t._s(t.detail.stock)+" 件")])]),s("div",{staticClass:"goods-item"},[s("span",{staticClass:"label"},[t._v("销量：")]),s("span",{staticClass:"value"},[t._v(t._s(t.detail.sales)+" 件")])]),s("div",{staticClass:"goods-item"},[s("span",{staticClass:"label"},[t._v("推荐：")]),1===t.detail.is_recommend?s("el-tag",{attrs:{type:"success"}},[t._v("是")]):s("el-tag",{attrs:{type:"info"}},[t._v("否")])],1),s("div",{staticClass:"goods-item"},[s("span",{staticClass:"label"},[t._v("热门：")]),1===t.detail.is_hot?s("el-tag",{attrs:{type:"success"}},[t._v("是")]):s("el-tag",{attrs:{type:"info"}},[t._v("否")])],1),s("div",{staticClass:"goods-item"},[s("span",{staticClass:"label"},[t._v("新品：")]),1===t.detail.is_new?s("el-tag",{attrs:{type:"success"}},[t._v("是")]):s("el-tag",{attrs:{type:"info"}},[t._v("否")])],1),s("div",{staticClass:"goods-item"},[s("span",{staticClass:"label"},[t._v("状态：")]),s("el-tag",{attrs:{type:1===t.detail.status?"success":"info"}},[t._v(" "+t._s(1===t.detail.status?"上架":"下架")+" ")])],1),s("div",{staticClass:"goods-item"},[s("span",{staticClass:"label"},[t._v("创建时间：")]),s("span",{staticClass:"value"},[t._v(t._s(t.detail.created_at))])]),s("div",{staticClass:"goods-item"},[s("span",{staticClass:"label"},[t._v("更新时间：")]),s("span",{staticClass:"value"},[t._v(t._s(t.detail.updated_at))])]),s("div",{staticClass:"goods-actions"},[s("el-button",{attrs:{type:"primary"},on:{click:t.handleEdit}},[t._v("编辑")]),1===t.detail.status?s("el-button",{attrs:{type:"warning"},on:{click:function(s){return t.handleStatusChange(0)}}},[t._v("下架")]):s("el-button",{attrs:{type:"success"},on:{click:function(s){return t.handleStatusChange(1)}}},[t._v("上架")]),s("el-button",{attrs:{type:"danger"},on:{click:t.handleDelete}},[t._v("删除")])],1)])])],1)],1),s("el-card",{staticClass:"box-card description-card"},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("商品描述")])]),s("div",{staticClass:"goods-description"},[t._v(" "+t._s(t.detail.description||"暂无描述")+" ")])]),s("el-card",{staticClass:"box-card details-card"},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[t._v("商品详情")])]),s("div",{staticClass:"goods-details",domProps:{innerHTML:t._s(t.detail.details)}})])],1)])},i=[],l=(a("14d9"),a("e9f5"),a("f665"),a("c40e")),o={name:"GoodsDetail",data(){return{loading:!0,detail:{},imageList:[],goodsId:null,categoryOptions:[{label:"数码产品",value:1},{label:"家居用品",value:2},{label:"美妆护肤",value:3},{label:"食品饮料",value:4}]}},created(){this.goodsId=parseInt(this.$route.params.id),this.fetchData()},methods:{formatImageUrl:l["c"],fetchData(){this.loading=!0,Object(l["d"])(this.goodsId).then(t=>{if(this.detail=t.data,this.detail.images)try{this.imageList="string"===typeof this.detail.images?JSON.parse(this.detail.images):this.detail.images}catch(s){console.error("解析商品图片失败",s),this.imageList=[]}this.loading=!1}).catch(()=>{this.loading=!1})},getCategoryName(t){const s=this.categoryOptions.find(s=>s.value===t);return s?s.label:"未分类"},handleEdit(){this.$router.push("/mall/goods/edit/"+this.goodsId)},handleStatusChange(t){this.$confirm(`确认要${1===t?"上架":"下架"}该商品吗?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(l["g"])(this.goodsId,{status:t}).then(s=>{this.$message.success("状态更新成功"),this.detail.status=t}).catch(()=>{this.$message.error("状态更新失败")})}).catch(()=>{})},handleDelete(){this.$confirm("确认要删除这个商品吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{console.log("正在调用删除API，商品ID: "+this.goodsId),Object(l["b"])(this.goodsId).then(t=>{console.log("删除商品成功，响应:",t),this.$message.success("删除成功"),this.goBack()}).catch(t=>{console.error("删除商品失败:",t),t.response&&404===t.response.status?(this.$message.error("商品不存在或已被删除"),this.goBack()):this.$message.error("删除失败: "+(t.message||"未知错误"))})}).catch(()=>{console.log("用户取消了删除操作")})},goBack(){this.$router.push("/mall/goods")}}},r=o,n=(a("38e2"),a("2877")),c=Object(n["a"])(r,e,i,!1,null,"d7c9fec0",null);s["default"]=c.exports},c40e:function(t,s,a){"use strict";a.d(s,"e",(function(){return i})),a.d(s,"d",(function(){return l})),a.d(s,"a",(function(){return o})),a.d(s,"f",(function(){return r})),a.d(s,"g",(function(){return n})),a.d(s,"b",(function(){return c})),a.d(s,"c",(function(){return d}));var e=a("b775");function i(t){return Object(e["a"])({url:"/api/v1/admin/goods/list",method:"get",params:t})}function l(t){return Object(e["a"])({url:"/api/v1/admin/goods/detail/"+t,method:"get"})}function o(t){return Object(e["a"])({url:"/api/v1/admin/goods/create",method:"post",data:t})}function r(t,s){return Object(e["a"])({url:"/api/v1/admin/goods/update/"+t,method:"put",data:s})}function n(t,s){return Object(e["a"])({url:"/api/v1/admin/goods/status/"+t,method:"put",data:s})}function c(t){return console.log(`删除商品 ID: ${t}, 请求URL: /api/v1/admin/goods/delete/${t}`),Object(e["a"])({url:"/api/v1/admin/goods/delete/"+t,method:"delete"})}function d(t){return t?t.startsWith("http://")||t.startsWith("https://")||t.startsWith("/")?t:"/uploads/"+t:"/uploads/default-goods.jpg"}},f665:function(t,s,a){"use strict";var e=a("23e7"),i=a("c65b"),l=a("2266"),o=a("59ed"),r=a("825a"),n=a("46c4"),c=a("2a62"),d=a("f99f"),u=d("find",TypeError);e({target:"Iterator",proto:!0,real:!0,forced:u},{find:function(t){r(this);try{o(t)}catch(e){c(this,"throw",e)}if(u)return i(u,this,t);var s=n(this),a=0;return l(s,(function(s,e){if(t(s,a++))return e(s)}),{IS_RECORD:!0,INTERRUPTED:!0}).result}})}}]);