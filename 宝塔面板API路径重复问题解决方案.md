# 宝塔面板API路径重复问题解决方案

## 🚨 问题描述

您在宝塔面板中遇到的错误：
```
[2025-07-28T13:33:10.178Z] [INFO] POST /api/api/v1/admin/auth/admin-login
[2025-07-28T13:33:10.178Z] [WARN] File not found: /api/api/v1/admin/auth/admin-login, serving index.html
```

**问题原因**：API路径被重复了，正确的路径应该是 `/api/v1/admin/auth/admin-login`，但实际请求的是 `/api/api/v1/admin/auth/admin-login`。

## 🔧 解决方案

### 第一步：确保后端服务器正常运行

1. **检查wifi-share-server是否运行**
   ```bash
   # 在宝塔面板终端中执行
   cd /www/wwwroot/wifi-share-server
   pm2 status
   ```

2. **如果后端服务器没有运行，启动它**
   ```bash
   cd /www/wwwroot/wifi-share-server
   pm2 start ecosystem.config.js
   ```

3. **验证后端服务器端口**
   - wifi-share-server 应该运行在 **4000** 端口
   - 可以通过访问 `http://your-server-ip:4000` 验证

### 第二步：修复前端配置

我已经修复了以下配置文件：

1. **src/utils/request.js** - 修复了baseURL配置
2. **.env.development** - 修正了API基础URL
3. **vue.config.js** - 修正了代理配置

### 第三步：在宝塔面板中部署

#### 方法1：使用Nginx反向代理（推荐）

1. **在宝塔面板中配置Nginx**
   - 进入"网站" -> 选择您的域名 -> "配置文件"
   - 添加以下配置：

```nginx
# API代理配置
location /api/ {
    proxy_pass http://127.0.0.1:4000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 处理CORS
    add_header Access-Control-Allow-Origin *;
    add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
    add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    
    if ($request_method = 'OPTIONS') {
        return 204;
    }
}

# 上传文件代理
location /uploads/ {
    proxy_pass http://127.0.0.1:4000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}
```

2. **重启Nginx**
   ```bash
   nginx -s reload
   ```

#### 方法2：修改生产环境配置

1. **创建生产环境配置文件**
   ```bash
   # 在wifi-share-admin目录下
   echo 'NODE_ENV=production
   VUE_APP_TITLE=WIFI共享商业系统管理后台
   VUE_APP_API_BASE_URL=http://your-server-ip:4000
   VUE_APP_PUBLIC_PATH=/
   VUE_APP_USE_MOCK=false' > .env.production
   ```

2. **重新构建项目**
   ```bash
   npm run build:prod
   ```

### 第四步：验证修复

1. **测试后端API**
   ```bash
   curl http://localhost:4000/api/v1/admin/auth/admin-login \
     -X POST \
     -H "Content-Type: application/json" \
     -d '{"username":"mrx0927","password":"hh20250701"}'
   ```

2. **检查前端访问**
   - 访问管理后台：`http://your-server-ip:8081`
   - 尝试登录，查看网络请求是否正确

### 第五步：监控日志

1. **查看后端日志**
   ```bash
   pm2 logs wifi-share-server
   ```

2. **查看前端日志**
   ```bash
   pm2 logs wifi-share-admin
   ```

## 🔍 故障排查

### 如果仍然出现API路径重复

1. **清除浏览器缓存**
2. **重启前端服务**
   ```bash
   pm2 restart wifi-share-admin
   ```

3. **检查环境变量**
   ```bash
   pm2 env wifi-share-admin
   ```

### 如果后端连接失败

1. **检查防火墙设置**
   - 确保4000端口开放
   
2. **检查数据库连接**
   ```bash
   mysql -u root -p wifi_share
   ```

3. **查看详细错误日志**
   ```bash
   tail -f /www/wwwroot/wifi-share-server/logs/error.log
   ```

## 📝 总结

修复后的配置：
- 后端服务器：http://localhost:4000
- 前端服务器：http://localhost:8081
- API路径：/api/v1/admin/auth/admin-login（不再重复）

现在您的管理后台应该能够正常登录了！
