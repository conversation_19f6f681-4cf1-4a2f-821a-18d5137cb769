(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-30b68098"],{6307:function(e,t,s){"use strict";s("63d1")},"63d1":function(e,t,s){},"9ed6":function(e,t,s){"use strict";s.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"login-container"},[t("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules,"auto-complete":"on","label-position":"left"}},[t("div",{staticClass:"title-container"},[t("h3",{staticClass:"title"},[e._v("登录管理系统")])]),t("el-form-item",{attrs:{prop:"username"}},[t("span",{staticClass:"svg-container"},[t("i",{staticClass:"el-icon-user"})]),t("el-input",{ref:"username",attrs:{placeholder:"用户名",name:"username",type:"text",tabindex:"1","auto-complete":"on"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}})],1),t("el-form-item",{attrs:{prop:"password"}},[t("span",{staticClass:"svg-container"},[t("i",{staticClass:"el-icon-lock"})]),t("el-input",{key:e.passwordType,ref:"password",attrs:{type:e.passwordType,placeholder:"密码",name:"password",tabindex:"2","auto-complete":"on"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin.apply(null,arguments)}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}}),t("span",{staticClass:"show-pwd",on:{click:e.showPwd}},[t("i",{class:"password"===e.passwordType?"el-icon-view":"el-icon-hide"})])],1),t("el-button",{staticStyle:{width:"100%","margin-bottom":"30px"},attrs:{loading:e.loading,type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin.apply(null,arguments)}}},[e._v("登录")])],1)],1)},r=[],n=(s("d9e2"),s("14d9"),{name:"Login",data(){const e=(e,t,s)=>{t.length<3?s(new Error("用户名不能小于3个字符")):s()},t=(e,t,s)=>{t.length<6?s(new Error("密码不能小于6个字符")):s()};return{loginForm:{username:"mrx0927",password:"hh20250701"},loginRules:{username:[{required:!0,trigger:"blur",validator:e}],password:[{required:!0,trigger:"blur",validator:t}]},loading:!1,passwordType:"password",redirect:void 0}},watch:{$route:{handler:function(e){this.redirect=e.query&&e.query.redirect},immediate:!0}},methods:{showPwd(){this.passwordType="password"===this.passwordType?"":"password",this.$nextTick(()=>{this.$refs.password.focus()})},handleLogin(){this.$refs.loginForm.validate(e=>{if(!e)return console.log("error submit!!"),!1;this.loading=!0,this.$store.dispatch("user/login",this.loginForm).then(()=>{this.$router.push({path:this.redirect||"/"}),this.loading=!1}).catch(()=>{this.loading=!1})})}}}),i=n,a=(s("6307"),s("a6fd"),s("2877")),l=Object(a["a"])(i,o,r,!1,null,"1e881757",null);t["default"]=l.exports},a6fd:function(e,t,s){"use strict";s("e23e")},e23e:function(e,t,s){}}]);