<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.keyword" placeholder="用户昵称/手机号" clearable class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.status" placeholder="状态" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        class="filter-item"
        style="width: 240px;"
        @change="handleDateRangeChange"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="正在加载..."
      border
      fit
      highlight-current-row
    >
      <el-table-column label="ID" prop="id" align="center" width="80" />
      <el-table-column label="用户信息" align="center" min-width="180">
        <template slot-scope="{row}">
          <div class="user-info">
            <el-avatar :size="32" :src="row.user_avatar">
              <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"/>
            </el-avatar>
            <div class="user-detail">
              <div class="nickname">{{ row.user_nickname }}</div>
              <div class="phone">{{ row.user_phone }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="提现金额" align="center" width="120">
        <template slot-scope="{row}">
          <span class="withdraw-amount">{{ row.amount }} 元</span>
        </template>
      </el-table-column>
      <el-table-column label="手续费" align="center" width="100">
        <template slot-scope="{row}">
          <span>{{ row.fee || 0 }} 元</span>
        </template>
      </el-table-column>
      <el-table-column label="实际到账" align="center" width="120">
        <template slot-scope="{row}">
          <span class="actual-amount">{{ (row.amount - (row.fee || 0)).toFixed(2) }} 元</span>
        </template>
      </el-table-column>
      <el-table-column label="银行信息" align="center" min-width="180">
        <template slot-scope="{row}">
          <div>{{ row.bank_name }}</div>
          <div class="card-number">{{ formatCardNumber(row.card_number) }}</div>
          <div>{{ row.card_holder }}</div>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="{row}">
          <el-tag :type="getStatusType(row.status)">{{ getStatusLabel(row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="申请时间" prop="created_at" align="center" width="160" />
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleView(row)">查看</el-button>
          <el-button v-if="row.status === 0" type="success" size="mini" @click="handleAudit(row, 1)">通过</el-button>
          <el-button v-if="row.status === 0" type="danger" size="mini" @click="handleAudit(row, 2)">拒绝</el-button>
          <el-button v-if="row.status === 1" type="warning" size="mini" @click="handleConfirm(row)">确认打款</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 审核对话框 -->
    <el-dialog :title="auditForm.status === 1 ? '审核通过' : '审核拒绝'" :visible.sync="auditDialogVisible" width="500px">
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="auditForm.remark"
            type="textarea"
            :rows="3"
            :placeholder="auditForm.status === 1 ? '请输入审核通过备注（选填）' : '请输入拒绝原因（必填）'"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAudit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 确认打款对话框 -->
    <el-dialog title="确认打款" :visible.sync="confirmDialogVisible" width="500px">
      <el-form ref="confirmForm" :model="confirmForm" :rules="confirmRules" label-width="120px">
        <el-form-item label="转账流水号" prop="transaction_id">
          <el-input v-model="confirmForm.transaction_id" placeholder="请输入转账流水号" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="confirmForm.remark" type="textarea" :rows="3" placeholder="请输入备注（选填）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="confirmDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitConfirm">确认打款</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWithdrawList, auditWithdraw, confirmWithdraw } from '@/api/profit'
import Pagination from '@/components/Pagination'

export default {
  name: 'WithdrawList',
  components: { Pagination },
  data () {
    const validateRemarkForReject = (rule, value, callback) => {
      if (this.auditForm.status === 2 && (!value || value.trim() === '')) {
        callback(new Error('拒绝时，请输入拒绝原因'))
      } else {
        callback()
      }
    }

    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        keyword: undefined,
        status: undefined,
        start_date: undefined,
        end_date: undefined
      },
      dateRange: null,
      statusOptions: [
        { label: '待审核', value: 0 },
        { label: '已通过', value: 1 },
        { label: '已拒绝', value: 2 },
        { label: '已打款', value: 3 }
      ],
      auditDialogVisible: false,
      auditForm: {
        id: null,
        status: 1,
        remark: ''
      },
      auditRules: {
        remark: [
          { validator: validateRemarkForReject, trigger: 'blur' }
        ]
      },
      confirmDialogVisible: false,
      confirmForm: {
        id: null,
        transaction_id: '',
        remark: ''
      },
      confirmRules: {
        transaction_id: [
          { required: true, message: '请输入转账流水号', trigger: 'blur' },
          { min: 5, message: '流水号长度不能少于5个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.listLoading = true
      getWithdrawList(this.listQuery).then(response => {
        this.list = response.data.list || []
        // 修复分页组件total属性类型错误
        this.total = (response.data.pagination && response.data.pagination.total) || response.data.total || 0
        this.listLoading = false
      }).catch(error => {
        console.error('获取提现列表失败:', error)
        this.list = []
        this.total = 0
        this.listLoading = false
        this.$message.error('获取提现列表失败')
      })
    },
    handleFilter () {
      this.listQuery.page = 1
      this.getList()
    },
    handleDateRangeChange (val) {
      if (val) {
        this.listQuery.start_date = val[0]
        this.listQuery.end_date = val[1]
      } else {
        this.listQuery.start_date = undefined
        this.listQuery.end_date = undefined
      }
    },
    handleView (row) {
      this.$router.push(`/profit/withdraw/detail/${row.id}`)
    },
    handleAudit (row, status) {
      this.auditForm = {
        id: row.id,
        status: status,
        remark: ''
      }
      this.auditDialogVisible = true
    },
    submitAudit () {
      this.$refs.auditForm.validate(valid => {
        if (valid) {
          auditWithdraw(this.auditForm.id, {
            status: this.auditForm.status,
            remark: this.auditForm.remark
          }).then(response => {
            this.$message.success(this.auditForm.status === 1 ? '审核通过成功' : '审核拒绝成功')
            this.auditDialogVisible = false
            this.getList()
          }).catch(() => {
            this.$message.error('审核操作失败')
          })
        }
      })
    },
    handleConfirm (row) {
      this.confirmForm = {
        id: row.id,
        transaction_id: '',
        remark: ''
      }
      this.confirmDialogVisible = true
    },
    submitConfirm () {
      this.$refs.confirmForm.validate(valid => {
        if (valid) {
          confirmWithdraw(this.confirmForm.id, {
            transaction_id: this.confirmForm.transaction_id,
            remark: this.confirmForm.remark
          }).then(response => {
            this.$message.success('确认打款成功')
            this.confirmDialogVisible = false
            this.getList()
          }).catch(() => {
            this.$message.error('确认打款失败')
          })
        }
      })
    },
    getStatusLabel (status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已拒绝',
        3: '已打款'
      }
      return statusMap[status] || '未知状态'
    },
    getStatusType (status) {
      const typeMap = {
        0: 'info',
        1: 'primary',
        2: 'danger',
        3: 'success'
      }
      return typeMap[status] || 'info'
    },
    formatCardNumber (cardNumber) {
      if (!cardNumber) return ''
      const lastFourDigits = cardNumber.slice(-4)
      const maskedPart = '*'.repeat(cardNumber.length - 4)
      return maskedPart + lastFourDigits
    }
  }
}
</script>

<style scoped>
.user-info {
  display: flex;
  align-items: center;
}
.user-detail {
  margin-left: 10px;
  text-align: left;
}
.nickname {
  font-size: 14px;
}
.phone {
  font-size: 12px;
  color: #909399;
}
.withdraw-amount {
  font-weight: bold;
}
.actual-amount {
  color: #67c23a;
  font-weight: bold;
}
.card-number {
  font-family: monospace;
  letter-spacing: 1px;
}
</style>
