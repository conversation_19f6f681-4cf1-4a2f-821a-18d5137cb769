<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? '编辑账号' : '新增账号' }}</span>
      </div>
      <el-form
        ref="accountForm"
        :model="accountForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="accountForm.username" placeholder="请输入用户名" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input v-model="accountForm.password" placeholder="请输入密码" type="password" />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword" v-if="!isEdit">
          <el-input v-model="accountForm.confirmPassword" placeholder="请再次输入密码" type="password" />
        </el-form-item>
        <el-form-item label="真实姓名" prop="real_name">
          <el-input v-model="accountForm.real_name" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <el-upload
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :http-request="uploadAvatar"
            :before-upload="beforeAvatarUpload">
            <img v-if="accountForm.avatar" :src="accountForm.avatar" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="角色" prop="role_id">
          <el-select v-model="accountForm.role_id" placeholder="请选择角色">
            <el-option
              v-for="item in roleOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
          <el-button type="text" @click="createNewRole" style="margin-left: 10px;">创建新角色</el-button>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="accountForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="accountForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="accountForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="goBack">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { createAccount, updateAccount, getRoleList, getAccountList } from '@/api/system'
import { uploadFile } from '@/api/upload'

export default {
  name: 'AccountForm',
  data () {
    // 自定义校验规则 - 确认密码
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.accountForm.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }

    return {
      isEdit: false,
      accountId: undefined,
      accountForm: {
        username: '',
        password: '',
        confirmPassword: '',
        real_name: '',
        avatar: '',
        role_id: '',
        email: '',
        phone: '',
        status: 1
      },
      roleOptions: [],
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 4, max: 20, message: '长度在 4 到 20 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ],
        real_name: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        role_id: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ],
        email: [
          { pattern: /^[\w.-]+@[\w.-]+\.\w+$/, message: '请输入有效的邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    // 获取角色列表
    this.getRoleOptions()

    // 判断是编辑还是新增
    const id = this.$route.params && this.$route.params.id
    if (id) {
      this.isEdit = true
      this.accountId = id
      this.getAccountDetail(id)
    }
  },
  activated() {
    // 在keep-alive组件激活时，重新获取角色列表，确保获取最新数据
    this.getRoleOptions()
  },
  // 路由进入前钩子
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 如果是从角色创建页面返回，强制刷新角色列表
      if (from.path === '/system/role/create') {
        console.log('从角色创建页面返回，强制刷新角色列表')
        vm.getRoleOptions()
      }
    })
  },
  methods: {
    getRoleOptions () {
      // 首先尝试直接从localStorage获取最新的角色列表（立即显示）
      try {
        const storedRoles = localStorage.getItem('wifi_admin_roles')
        if (storedRoles) {
          const roles = JSON.parse(storedRoles)
          this.roleOptions = roles || []
          console.log('从localStorage获取的角色列表:', this.roleOptions)
        }
      } catch (e) {
        console.error('从localStorage获取角色列表失败:', e)
      }
      
      // 然后通过API获取（确保数据最新）
      getRoleList().then(response => {
        if (response.code === 200) {
          this.roleOptions = response.data.list || []
          console.log('通过API获取的角色列表:', this.roleOptions)
        } else {
          this.$message.error(response.message || '获取角色列表失败')
        }
      }).catch(error => {
        this.$message.error(error.message || '获取角色列表失败')
      })
    },
    getAccountDetail (id) {
      console.log('获取账号详情，ID:', id)
      
      // 首先直接从localStorage获取账号详情
      try {
        const storedAccountsStr = localStorage.getItem('wifi_admin_accounts')
        console.log('从localStorage获取的原始账号数据:', storedAccountsStr)
        
        if (storedAccountsStr) {
          const accounts = JSON.parse(storedAccountsStr)
          console.log('解析后的账号数据:', accounts)
          
          // 使用parseInt确保ID类型匹配
          const parsedId = parseInt(id)
          console.log('查找账号ID:', parsedId, '类型:', typeof parsedId)
          
          // 打印所有账号ID及其类型以便调试
          accounts.forEach(acc => {
            console.log('账号ID:', acc.id, '类型:', typeof acc.id)
          })
          
          const account = accounts.find(item => parseInt(item.id) === parsedId)
          console.log('找到的账号:', account)
          
          if (account) {
            this.accountForm = {
              id: account.id,
              username: account.username,
              real_name: account.real_name || account.nickname,
              avatar: account.avatar || '',
              role_id: account.role_id,
              email: account.email || '',
              phone: account.phone || '',
              status: account.status
            }
            console.log('设置表单数据成功:', this.accountForm)
            return
          } else {
            console.error('在localStorage中未找到ID为', id, '的账号')
          }
        }
      } catch (e) {
        console.error('从localStorage获取账号详情失败:', e)
      }
      
      // 如果从localStorage获取失败，则通过API获取
      console.log('从API获取账号详情')
      getAccountList().then(response => {
        if (response.code === 200) {
          const accounts = response.data.list || []
          const account = accounts.find(item => parseInt(item.id) === parseInt(id))
          if (account) {
            this.accountForm = {
              id: account.id,
              username: account.username,
              real_name: account.real_name || account.nickname,
              avatar: account.avatar || '',
              role_id: account.role_id,
              email: account.email || '',
              phone: account.phone || '',
              status: account.status
            }
            console.log('从API获取账号详情成功:', this.accountForm)
          } else {
            console.error('API返回数据中未找到ID为', id, '的账号')
            this.$message.error('账号不存在')
            this.goBack()
          }
        }
      }).catch(error => {
        console.error('获取账号详情API调用失败:', error)
        this.$message.error(error.message || '获取账号详情失败')
        this.goBack()
      })
    },
    beforeAvatarUpload (file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG/PNG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    uploadAvatar (options) {
      const { file } = options
      uploadFile(file).then(response => {
        this.accountForm.avatar = response.data.url
        this.$message.success('头像上传成功')
      }).catch(error => {
        console.error('上传失败:', error)
        this.$message.error('头像上传失败')
      })
    },
    submitForm () {
      this.$refs.accountForm.validate(valid => {
        if (valid) {
          // 移除确认密码字段，API不需要
          const submitData = { ...this.accountForm }
          delete submitData.confirmPassword

          if (this.isEdit) {
            // 编辑模式下，不传密码字段
            delete submitData.password

            // 更新
            updateAccount(this.accountId, submitData).then(() => {
              this.$message.success('更新成功')
              // 重新获取角色列表，确保数据最新
              this.getRoleOptions()
              this.goBack()
            })
          } else {
            // 创建
            createAccount(submitData).then(response => {
              this.$message.success('创建成功')
              console.log('创建账号成功，返回数据:', response)
              
              // 重新获取角色列表，确保数据最新
              this.getRoleOptions()
              
              // 直接修改localStorage中的账号数据
              try {
                // 获取当前账号列表
                const storedAccountsStr = localStorage.getItem('wifi_admin_accounts')
                const accounts = storedAccountsStr ? JSON.parse(storedAccountsStr) : []
                
                // 创建新账号对象
                const newAccount = {
                  id: response.data && response.data.id ? response.data.id : (accounts.length > 0 ? Math.max(...accounts.map(a => a.id)) + 1 : 1),
                  username: submitData.username,
                  nickname: submitData.real_name || submitData.username,
                  real_name: submitData.real_name || '',
                  email: submitData.email || '',
                  phone: submitData.phone || '',
                  avatar: submitData.avatar || '',
                  role_id: parseInt(submitData.role_id),
                  status: parseInt(submitData.status) || 1,
                  last_login_time: '',
                  last_login_ip: '',
                  created_at: new Date().toISOString().replace('T', ' ').slice(0, 19),
                  updated_at: new Date().toISOString().replace('T', ' ').slice(0, 19)
                }
                
                // 获取角色信息
                const storedRolesStr = localStorage.getItem('wifi_admin_roles')
                if (storedRolesStr) {
                  const roles = JSON.parse(storedRolesStr)
                  const role = roles.find(r => r.id === parseInt(submitData.role_id))
                  if (role) {
                    newAccount.role_name = role.name
                  }
                }
                
                // 添加到账号列表
                accounts.push(newAccount)
                
                // 保存回localStorage
                localStorage.setItem('wifi_admin_accounts', JSON.stringify(accounts))
                console.log('更新后的账号列表:', accounts)
                
                // 清除可能的缓存
                localStorage.removeItem('wifi_admin_accounts_processed')
                localStorage.removeItem('wifi_admin_accounts_cache')
              } catch (e) {
                console.error('更新localStorage中的账号数据失败:', e)
              }
              
              this.goBack()
            })
          }
        }
      })
    },
    goBack () {
      this.$router.push('/system/account')
    },
    createNewRole () {
      this.$router.push({
        path: '/system/role/create',
        query: { referrer: 'account-create' }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;

  &:hover {
    border-color: #409EFF;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
