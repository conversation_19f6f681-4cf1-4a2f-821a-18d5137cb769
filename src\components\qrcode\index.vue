<template>
  <div class="qrcode-generator">
    <div class="qrcode-wrapper">
      <canvas ref="qrcodeCanvas" class="qrcode-canvas"></canvas>
      <div v-if="error" class="qrcode-error">
        {{ errorMessage || '二维码生成失败' }}
      </div>
    </div>
    <div v-if="showActions" class="qrcode-actions">
      <slot name="actions">
        <el-button type="primary" size="small" @click="downloadQrcode">下载</el-button>
        <el-button type="success" size="small" @click="printQrcode">打印</el-button>
      </slot>
    </div>
  </div>
</template>

<script>
import QRCode from 'qrcode'

export default {
  name: 'QrcodeGenerator',
  props: {
    // 二维码内容
    content: {
      type: String,
      default: ''
    },
    // WiFi信息对象 {ssid, password, hidden}
    wifiInfo: {
      type: Object,
      default: null
    },
    // 二维码宽度
    width: {
      type: Number,
      default: 200
    },
    // 二维码边距
    margin: {
      type: Number,
      default: 2
    },
    // 二维码颜色
    color: {
      type: Object,
      default: () => ({
        dark: '#000000',
        light: '#FFFFFF'
      })
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      default: true
    },
    // 下载时的文件名
    fileName: {
      type: String,
      default: 'qrcode'
    },
    // 打印时的标题
    title: {
      type: String,
      default: '二维码'
    },
    // 打印时的附加信息
    printInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      error: false,
      errorMessage: '',
      qrcodeDataUrl: '',
      generatedContent: ''
    }
  },
  computed: {
    // 计算最终的二维码内容
    qrcodeContent() {
      // 如果提供了WiFi信息，则生成WiFi二维码格式
      if (this.wifiInfo && this.wifiInfo.ssid && this.wifiInfo.password) {
        const hidden = this.wifiInfo.hidden ? 'true' : 'false'
        return `WIFI:T:WPA;S:${this.wifiInfo.ssid};P:${this.wifiInfo.password};H:${hidden};;`
      }
      // 否则使用普通内容
      return this.content || ''
    }
  },
  watch: {
    // 监听内容变化，重新生成二维码
    qrcodeContent: {
      handler(newValue) {
        if (newValue && newValue !== this.generatedContent) {
          this.$nextTick(() => {
            this.generateQRCode()
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    // 生成二维码
    generateQRCode() {
      if (!this.qrcodeContent) {
        console.warn('二维码内容为空')
        this.error = true
        this.errorMessage = '二维码内容为空'
        return
      }
      
      this.error = false
      this.errorMessage = ''
      
      const canvas = this.$refs.qrcodeCanvas
      if (!canvas) {
        console.error('Canvas元素未找到')
        this.error = true
        this.errorMessage = 'Canvas元素未找到'
        return
      }
      
      QRCode.toCanvas(canvas, this.qrcodeContent, {
        width: this.width,
        margin: this.margin,
        color: this.color
      }, (error) => {
        if (error) {
          console.error('生成二维码失败:', error)
          this.error = true
          this.errorMessage = '生成二维码失败: ' + error.message
        } else {
          console.log('二维码生成成功')
          this.generatedContent = this.qrcodeContent
          // 保存二维码数据URL用于下载
          this.qrcodeDataUrl = canvas.toDataURL('image/png')
          this.$emit('generated', this.qrcodeDataUrl)
        }
      })
    },
    // 下载二维码
    downloadQrcode() {
      if (!this.qrcodeDataUrl) {
        this.$message.error('二维码尚未生成')
        return
      }
      
      try {
        // 创建一个临时链接
        const link = document.createElement('a')
        link.href = this.qrcodeDataUrl
        link.download = `${this.fileName}.png`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        this.$message.success('二维码下载成功')
        this.$emit('downloaded')
      } catch (error) {
        console.error('下载二维码失败:', error)
        this.$message.error('下载二维码失败')
      }
    },
    // 打印二维码
    printQrcode() {
      if (!this.qrcodeDataUrl) {
        this.$message.error('二维码尚未生成')
        return
      }
      
      try {
        // 创建打印窗口
        const printWindow = window.open('', '_blank')
        if (!printWindow) {
          this.$message.error('打印窗口被阻止，请允许弹出窗口')
          return
        }
        
        let infoHtml = ''
        if (this.wifiInfo) {
          infoHtml = `
            <div class="info-section">
              <p><strong>WiFi名称:</strong> ${this.wifiInfo.ssid}</p>
              <p><strong>WiFi密码:</strong> ${this.wifiInfo.password}</p>
              ${this.printInfo && this.printInfo.merchant ? `<p><strong>商户名称:</strong> ${this.printInfo.merchant}</p>` : ''}
            </div>
          `
        } else if (this.printInfo) {
          infoHtml = '<div class="info-section">'
          for (const key in this.printInfo) {
            if (Object.prototype.hasOwnProperty.call(this.printInfo, key)) {
              infoHtml += `<p><strong>${key}:</strong> ${this.printInfo[key]}</p>`
            }
          }
          infoHtml += '</div>'
        }
        
        const html = `
          <html>
            <head>
              <title>${this.title}</title>
              <style>
                body {
                  font-family: Arial, sans-serif;
                  text-align: center;
                  padding: 20px;
                }
                .qrcode-container {
                  margin: 20px auto;
                }
                .qrcode-image {
                  max-width: 300px;
                }
                .info-section {
                  margin-top: 20px;
                  font-size: 16px;
                }
              </style>
            </head>
            <body>
              <h2>${this.title}</h2>
              <div class="qrcode-container">
                <img src="${this.qrcodeDataUrl}" alt="二维码" class="qrcode-image">
              </div>
              ${infoHtml}
            </body>
          </html>
        `
        
        printWindow.document.open()
        printWindow.document.write(html)
        printWindow.document.close()
        
        // 等待图片加载完成后打印
        setTimeout(() => {
          printWindow.print()
          printWindow.close()
          this.$emit('printed')
        }, 500)
        
      } catch (error) {
        console.error('打印二维码失败:', error)
        this.$message.error('打印二维码失败')
      }
    },
    // 手动重新生成二维码
    regenerate() {
      this.generateQRCode()
    }
  }
}
</script>

<style scoped>
.qrcode-generator {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.qrcode-wrapper {
  position: relative;
  display: inline-block;
  margin: 10px 0;
}
.qrcode-canvas {
  border: 1px solid #eee;
  padding: 10px;
  background-color: #fff;
}
.qrcode-error {
  color: #f56c6c;
  margin-top: 10px;
  font-size: 14px;
}
.qrcode-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}
</style> 