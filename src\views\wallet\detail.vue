<template>
  <div class="app-container">
    <div class="detail-header">
      <el-page-header @back="goBack" :content="userInfo.nickname || '钱包详情'" />
    </div>
    
    <el-card class="box-card" v-loading="loading">
      <div slot="header" class="clearfix">
        <span>用户信息</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          icon="el-icon-edit-outline"
          @click="handleAdjust"
        >
          调整余额
        </el-button>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="user-profile">
            <el-avatar :src="userInfo.avatar" :size="80">
              {{ userInfo.nickname ? userInfo.nickname.charAt(0) : 'U' }}
            </el-avatar>
            <div class="user-info">
              <h3>{{ userInfo.nickname || '未设置昵称' }}</h3>
              <p>{{ userInfo.phone || '未绑定手机' }}</p>
              <el-tag v-if="userInfo.role === 'leader'" type="warning">团长</el-tag>
              <el-tag v-else-if="userInfo.role === 'admin'" type="danger">管理员</el-tag>
              <el-tag v-else type="info">普通用户</el-tag>
            </div>
          </div>
        </el-col>
        <el-col :span="16">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户ID">{{ userInfo.id }}</el-descriptions-item>
            <el-descriptions-item label="当前余额">
              <span class="balance-amount">¥{{ formatNumber(userInfo.balance) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="累计收益">
              <span class="income-amount">¥{{ formatNumber(userInfo.total_income) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="账户状态">
              <el-tag v-if="userInfo.status === 1" type="success">正常</el-tag>
              <el-tag v-else type="danger">禁用</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">{{ userInfo.created_at }}</el-descriptions-item>
            <el-descriptions-item label="最后更新">{{ userInfo.updated_at }}</el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 交易统计 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>交易统计</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-title">总交易次数</div>
            <div class="stat-value">{{ stats.total_transactions || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-title">收入次数</div>
            <div class="stat-value income">{{ stats.income_count || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-title">支出次数</div>
            <div class="stat-value expense">{{ stats.expense_count || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-title">净收益</div>
            <div class="stat-value">¥{{ formatNumber((stats.total_income || 0) - (stats.total_expense || 0)) }}</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 最近交易记录 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>最近交易记录</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          icon="el-icon-more"
          @click="viewAllTransactions"
        >
          查看全部
        </el-button>
      </div>
      
      <el-table
        :data="recentTransactions"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="交易ID" width="80" />
        <el-table-column prop="type" label="类型" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.type === 'income'" type="success">收入</el-tag>
            <el-tag v-else type="warning">支出</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="120">
          <template slot-scope="scope">
            <span :class="scope.row.type === 'income' ? 'income-amount' : 'expense-amount'">
              {{ scope.row.type === 'income' ? '+' : '-' }}¥{{ formatNumber(scope.row.amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="business_type" label="业务类型" width="120">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.business_type === 'wifi_share'" type="primary">WiFi分享</el-tag>
            <el-tag v-else-if="scope.row.business_type === 'goods_sale'" type="success">商品销售</el-tag>
            <el-tag v-else-if="scope.row.business_type === 'advertisement'" type="warning">广告点击</el-tag>
            <el-tag v-else-if="scope.row.business_type === 'admin_adjust'" type="danger">管理员调整</el-tag>
            <el-tag v-else type="info">其他</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="交易时间" width="160" />
      </el-table>
      
      <div class="empty-block" v-if="!recentTransactions || recentTransactions.length === 0">
        <span class="empty-text">暂无交易记录</span>
      </div>
    </el-card>
    
    <!-- 余额调整对话框 -->
    <el-dialog title="调整余额" :visible.sync="adjustDialogVisible" width="500px" @close="resetAdjustForm">
      <el-form
        ref="adjustForm"
        :model="adjustForm"
        :rules="adjustRules"
        label-width="100px"
        size="small"
      >
        <el-form-item label="当前余额">
          <span class="balance-amount">¥{{ formatNumber(userInfo.balance) }}</span>
        </el-form-item>
        <el-form-item label="调整类型" prop="type">
          <el-radio-group v-model="adjustForm.type">
            <el-radio label="increase">增加余额</el-radio>
            <el-radio label="decrease">减少余额</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整金额" prop="amount">
          <el-input
            v-model="adjustForm.amount"
            placeholder="请输入调整金额"
            type="number"
            step="0.01"
            min="0"
          >
            <template slot="prepend">¥</template>
          </el-input>
        </el-form-item>
        <el-form-item label="调整说明" prop="description">
          <el-input
            v-model="adjustForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入调整说明"
          />
        </el-form-item>
        <el-form-item label="调整后余额">
          <span class="preview-balance">¥{{ previewBalance }}</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="adjustDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAdjust" :loading="adjusting">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWalletDetail, adjustBalance } from '@/api/wallet'

export default {
  name: 'WalletDetail',
  data() {
    return {
      userId: null,
      loading: false,
      userInfo: {},
      stats: {},
      recentTransactions: [],
      
      // 余额调整对话框
      adjustDialogVisible: false,
      adjusting: false,
      adjustForm: {
        type: 'increase',
        amount: '',
        description: ''
      },
      adjustRules: {
        type: [
          { required: true, message: '请选择调整类型', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入调整金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入有效的金额', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    previewBalance() {
      const currentBalance = parseFloat(this.userInfo.balance || 0)
      const adjustAmount = parseFloat(this.adjustForm.amount || 0)
      
      if (this.adjustForm.type === 'increase') {
        return this.formatNumber(currentBalance + adjustAmount)
      } else {
        return this.formatNumber(Math.max(0, currentBalance - adjustAmount))
      }
    }
  },
  created() {
    this.userId = this.$route.params.userId
    this.fetchData()
  },
  methods: {
    async fetchData() {
      if (!this.userId) return
      
      this.loading = true
      try {
        const { data } = await getWalletDetail(this.userId)
        this.userInfo = data.user
        this.stats = data.stats
        this.recentTransactions = data.recent_transactions
      } catch (error) {
        console.error('获取钱包详情失败:', error)
        this.$message.error('获取钱包详情失败')
      } finally {
        this.loading = false
      }
    },
    goBack() {
      this.$router.push('/wallet/list')
    },
    viewAllTransactions() {
      this.$router.push(`/wallet/transactions/${this.userId}`)
    },
    handleAdjust() {
      this.adjustDialogVisible = true
      this.adjustForm = {
        type: 'increase',
        amount: '',
        description: ''
      }
    },
    resetAdjustForm() {
      this.$refs.adjustForm && this.$refs.adjustForm.resetFields()
    },
    submitAdjust() {
      this.$refs.adjustForm.validate(async valid => {
        if (!valid) return
        
        this.adjusting = true
        try {
          await adjustBalance(this.userId, {
            type: this.adjustForm.type,
            amount: this.adjustForm.amount,
            description: this.adjustForm.description
          })
          this.$message.success('余额调整成功')
          this.adjustDialogVisible = false
          this.fetchData() // 重新获取数据
        } catch (error) {
          this.$message.error('余额调整失败')
        } finally {
          this.adjusting = false
        }
      })
    },
    formatNumber(num) {
      return parseFloat(num || 0).toFixed(2)
    }
  }
}
</script>

<style scoped>
.detail-header {
  margin-bottom: 20px;
}

.user-profile {
  text-align: center;
  padding: 20px;
}

.user-info {
  margin-top: 15px;
}

.user-info h3 {
  margin: 10px 0 5px 0;
  color: #303133;
}

.user-info p {
  margin: 5px 0;
  color: #909399;
}

.balance-amount {
  color: #67C23A;
  font-weight: bold;
  font-size: 16px;
}

.income-amount {
  color: #67C23A;
  font-weight: bold;
}

.expense-amount {
  color: #F56C6C;
  font-weight: bold;
}

.preview-balance {
  color: #E6A23C;
  font-weight: bold;
  font-size: 16px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-value.income {
  color: #67C23A;
}

.stat-value.expense {
  color: #F56C6C;
}

.empty-block {
  min-height: 60px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
}
</style>
