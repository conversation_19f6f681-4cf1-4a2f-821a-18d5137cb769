<template>
  <div class="app-container">
    <div v-loading="loading" class="user-detail">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="user-card">
            <div class="user-header">
              <el-avatar :size="100" :src="userInfo.avatar">
                <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"/>
              </el-avatar>
              <h3 class="nickname">{{ userInfo.nickname || '未设置昵称' }}</h3>
              <div class="user-status">
                <el-tag :type="userInfo.status === 1 ? 'success' : 'info'">{{ userInfo.status === 1 ? '启用' : '禁用' }}</el-tag>
                <el-tag v-if="userInfo.is_leader === 1" type="warning" style="margin-left: 10px;">团长</el-tag>
              </div>
            </div>
            <div class="user-info-list">
              <div class="info-item">
                <span class="info-label">用户ID:</span>
                <span class="info-value">{{ userInfo.id }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">微信openid:</span>
                <span class="info-value">{{ userInfo.openid || '无' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">手机号码:</span>
                <span class="info-value">{{ userInfo.phone || '未绑定' }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">账户余额:</span>
                <span class="info-value">{{ userInfo.balance }} 元</span>
              </div>
              <div class="info-item">
                <span class="info-label">用户等级:</span>
                <span class="info-value">{{ userInfo.level }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">注册时间:</span>
                <span class="info-value">{{ userInfo.created_at }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">最近更新:</span>
                <span class="info-value">{{ userInfo.updated_at }}</span>
              </div>
            </div>
            <div class="action-buttons">
              <el-button v-if="userInfo.status === 1" type="warning" @click="handleStatusChange(0)">禁用账户</el-button>
              <el-button v-else type="success" @click="handleStatusChange(1)">启用账户</el-button>
              <el-button type="primary" @click="handleEdit">编辑信息</el-button>
              <el-button type="warning" @click="handleBalanceAdjust">调整余额</el-button>
            </div>
          </el-card>
        </el-col>

        <el-col :span="16">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>消费统计</span>
            </div>
            <div v-if="consumptionStats.total_orders > 0" class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ consumptionStats.total_orders }}</div>
                <div class="stat-title">总订单数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ consumptionStats.total_amount }}</div>
                <div class="stat-title">总消费金额</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ consumptionStats.avg_order_amount }}</div>
                <div class="stat-title">平均订单金额</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ consumptionStats.favorite_category }}</div>
                <div class="stat-title">偏好分类</div>
              </div>
            </div>
            <div v-else class="no-data">
              <el-empty description="暂无消费数据"></el-empty>
            </div>
          </el-card>

          <el-card class="box-card" style="margin-top: 20px;">
            <div slot="header" class="clearfix">
              <span>WiFi统计</span>
            </div>
            <div v-if="wifiStats.total_wifi > 0" class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ wifiStats.total_wifi }}</div>
                <div class="stat-title">创建WiFi数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ wifiStats.total_scans }}</div>
                <div class="stat-title">总扫码次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ wifiStats.avg_scans_per_wifi }}</div>
                <div class="stat-title">平均扫码次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ wifiStats.most_popular_wifi ? wifiStats.most_popular_wifi.title : '无' }}</div>
                <div class="stat-title">最受欢迎WiFi</div>
              </div>
            </div>
            <div v-else class="no-data">
              <el-empty description="暂无WiFi数据"></el-empty>
            </div>
          </el-card>

          <el-card class="box-card" style="margin-top: 20px;">
            <div slot="header" class="clearfix">
              <span>团队信息</span>
            </div>
            <div v-if="userInfo.team_id" class="team-info">
              <div class="info-item">
                <span class="info-label">团队ID:</span>
                <span class="info-value">{{ teamInfo.id }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">团队名称:</span>
                <span class="info-value">{{ teamInfo.name }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">成员数量:</span>
                <span class="info-value">{{ teamInfo.member_count }} 人</span>
              </div>
              <div class="info-item">
                <span class="info-label">WiFi数量:</span>
                <span class="info-value">{{ teamInfo.wifi_count }} 个</span>
              </div>
              <div class="info-item">
                <span class="info-label">总收益:</span>
                <span class="info-value">{{ teamInfo.total_profit }} 元</span>
              </div>
            </div>
            <div v-else class="no-team">
              <el-empty description="该用户未加入任何团队"></el-empty>
            </div>
          </el-card>

          <el-card class="box-card" style="margin-top: 20px;">
            <div slot="header" class="clearfix">
              <span>最近WiFi码</span>
              <el-button style="float: right; padding: 3px 0" type="text" @click="viewAllWifi">查看全部</el-button>
            </div>
            <el-table
              v-if="wifiList.length > 0"
              :data="wifiList"
              style="width: 100%"
              border
            >
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="title" label="WiFi标题" min-width="150" />
              <el-table-column prop="name" label="WiFi名称" min-width="150" />
              <el-table-column prop="use_count" label="使用次数" width="120" />
              <el-table-column prop="created_at" label="创建时间" width="180" />
              <el-table-column label="操作" width="120" align="center">
                <template slot-scope="{row}">
                  <el-button type="primary" size="mini" @click="viewWifi(row.id)">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div v-else class="no-data">
              <el-empty description="暂无WiFi码数据"></el-empty>
            </div>
          </el-card>

          <el-card class="box-card" style="margin-top: 20px;">
            <div slot="header" class="clearfix">
              <span>最近订单</span>
              <el-button style="float: right; padding: 3px 0" type="text" @click="viewAllOrders">查看全部</el-button>
            </div>
            <el-table
              v-if="orderList.length > 0"
              :data="orderList"
              style="width: 100%"
              border
            >
              <el-table-column prop="order_no" label="订单号" min-width="180" />
              <el-table-column prop="total_amount" label="订单金额" width="120" />
              <el-table-column label="订单状态" width="120" align="center">
                <template slot-scope="{row}">
                  <el-tag :type="getOrderStatusType(row.status)">{{ getOrderStatusText(row.status) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="创建时间" width="180" />
              <el-table-column label="操作" width="120" align="center">
                <template slot-scope="{row}">
                  <el-button type="primary" size="mini" @click="viewOrder(row.id)">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div v-else class="no-data">
              <el-empty description="暂无订单数据"></el-empty>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <el-dialog title="编辑用户信息" :visible.sync="dialogVisible" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="form.nickname" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" />
        </el-form-item>
        <el-form-item label="用户等级" prop="level">
          <el-input-number v-model="form.level" :min="0" :max="10" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>

    <!-- 余额调整对话框 -->
    <el-dialog title="调整用户余额" :visible.sync="balanceDialogVisible" width="400px">
      <el-form ref="balanceForm" :model="balanceForm" :rules="balanceRules" label-width="100px">
        <el-form-item label="当前余额">
          <span>{{ userInfo.balance }} 元</span>
        </el-form-item>
        <el-form-item label="操作类型" prop="type">
          <el-radio-group v-model="balanceForm.type">
            <el-radio label="add">增加</el-radio>
            <el-radio label="subtract">减少</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整金额" prop="amount">
          <el-input-number v-model="balanceForm.amount" :min="0.01" :step="0.01" :precision="2" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="balanceForm.remark" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="balanceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBalanceAdjust">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserDetail, updateUser, updateUserStatus, getUserConsumptionStats, getUserWifiStats, adjustUserBalance } from '@/api/user-manage'

export default {
  name: 'UserDetail',
  data () {
    return {
      loading: true,
      userInfo: {},
      teamInfo: {},
      wifiList: [],
      orderList: [],
      consumptionStats: {},
      wifiStats: {},
      dialogVisible: false,
      balanceDialogVisible: false,
      form: {
        nickname: '',
        phone: '',
        level: 0,
        status: 1
      },
      balanceForm: {
        type: 'add',
        amount: 0,
        remark: ''
      },
      rules: {
        nickname: [
          { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },
      balanceRules: {
        amount: [
          { required: true, message: '请输入调整金额', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    const id = parseInt(this.$route.params.id)
    console.log('用户详情页面加载，用户ID:', id)
    
    if (id && !isNaN(id)) {
    this.fetchUserDetail(id)
      this.fetchConsumptionStats(id)
      this.fetchWifiStats(id)
    } else {
      console.error('无效的用户ID:', this.$route.params.id)
      this.$message.error('无效的用户ID')
      this.loading = false
    }
  },
  methods: {
    fetchUserDetail (id) {
      this.loading = true
      getUserDetail(id).then(response => {
        console.log('用户详情数据:', response)
        
        if (response.data) {
        const { user_info, team_info, wifi_list, order_list } = response.data
          this.userInfo = user_info || {}
        this.teamInfo = team_info || {}
        this.wifiList = wifi_list || []
        this.orderList = order_list || []
        } else {
          console.error('响应数据结构错误:', response)
          this.$message.error('数据格式错误')
        }
        
        this.loading = false
      }).catch(error => {
        console.error('获取用户详情失败:', error)
        this.$message.error('获取用户详情失败')
        this.loading = false
      })
    },
    fetchConsumptionStats (id) {
      getUserConsumptionStats(id).then(response => {
        this.consumptionStats = response.data || {}
      }).catch(error => {
        console.error('获取消费统计失败:', error)
      })
    },
    fetchWifiStats (id) {
      getUserWifiStats(id).then(response => {
        this.wifiStats = response.data || {}
      }).catch(error => {
        console.error('获取WiFi统计失败:', error)
      })
    },
    handleEdit () {
      this.form = {
        nickname: this.userInfo.nickname,
        phone: this.userInfo.phone,
        level: this.userInfo.level,
        status: this.userInfo.status
      }
      this.dialogVisible = true
    },
    handleBalanceAdjust () {
      this.balanceForm = {
        type: 'add',
        amount: 0,
        remark: ''
      }
      this.balanceDialogVisible = true
    },
    submitForm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 验证用户ID是否有效
          if (!this.userInfo.id) {
            this.$message.error('用户ID无效，无法更新')
            return
          }
          
          updateUser(this.userInfo.id, this.form).then(response => {
            this.$message.success('更新成功')
            this.dialogVisible = false
            this.fetchUserDetail(this.userInfo.id)
          }).catch(() => {
            this.$message.error('更新失败')
          })
        }
      })
    },
    submitBalanceAdjust () {
      this.$refs.balanceForm.validate(valid => {
        if (valid) {
          // 验证用户ID是否有效
          if (!this.userInfo.id) {
            this.$message.error('用户ID无效，无法调整余额')
            return
          }
          
          adjustUserBalance(this.userInfo.id, this.balanceForm).then(response => {
            this.$message.success('余额调整成功')
            this.balanceDialogVisible = false
            this.fetchUserDetail(this.userInfo.id)
          }).catch(() => {
            this.$message.error('余额调整失败')
          })
        }
      })
    },
    handleStatusChange (status) {
      // 验证用户ID是否有效
      if (!this.userInfo.id) {
        this.$message.error('用户ID无效，无法更新状态')
        return
      }
      
      this.$confirm(`确认要${status === 1 ? '启用' : '禁用'}该用户吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateUserStatus(this.userInfo.id, { status }).then(response => {
          this.$message.success('状态更新成功')
          this.userInfo.status = status
        }).catch(() => {
          this.$message.error('状态更新失败')
        })
      }).catch(() => {
        // 取消操作
      })
    },
    viewWifi (id) {
      this.$router.push(`/wifi/detail/${id}`)
    },
    viewAllWifi () {
      // 跳转到WiFi列表页，带上用户ID过滤
      this.$router.push({
        path: '/wifi/list',
        query: { user_id: this.userInfo.id }
      })
    },
    viewOrder (id) {
      this.$router.push(`/mall/order/detail/${id}`)
    },
    viewAllOrders () {
      // 跳转到订单列表页，带上用户ID过滤
      this.$router.push({
        path: '/mall/order',
        query: { user_id: this.userInfo.id }
      })
    },
    getOrderStatusType (status) {
      const statusMap = {
        0: 'info',
        1: 'primary',
        2: 'warning',
        3: 'success',
        4: 'danger'
      }
      return statusMap[status] || 'info'
    },
    getOrderStatusText (status) {
      const statusMap = {
        0: '待支付',
        1: '待发货',
        2: '待收货',
        3: '已完成',
        4: '已取消'
      }
      return statusMap[status] || '未知状态'
    }
  }
}
</script>

<style scoped>
.user-card {
  height: 100%;
}
.user-header {
  text-align: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}
.nickname {
  margin: 10px 0;
  font-size: 18px;
}
.user-status {
  margin-top: 10px;
}
.user-info-list {
  padding: 20px 0;
}
.info-item {
  margin-bottom: 15px;
  display: flex;
}
.info-label {
  font-weight: bold;
  width: 100px;
}
.info-value {
  flex: 1;
  color: #606266;
  word-break: break-all;
}
.action-buttons {
  display: flex;
  justify-content: space-around;
}
.team-info {
  padding: 10px 0;
}
.no-team, .no-data {
  padding: 30px 0;
  text-align: center;
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 20px 0;
}
.stat-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}
.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}
.stat-title {
  font-size: 14px;
  color: #666;
}
</style>
