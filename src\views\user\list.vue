<template>
  <div class="app-container">
    <!-- 数据统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total_users }}</div>
            <div class="stat-label">总用户数</div>
          </div>
          <i class="el-icon-user stat-icon"></i>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.active_users }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
          <i class="el-icon-check stat-icon"></i>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.leader_users }}</div>
            <div class="stat-label">团长用户</div>
          </div>
          <i class="el-icon-star-on stat-icon"></i>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.today_users }}</div>
            <div class="stat-label">今日新增</div>
          </div>
          <i class="el-icon-plus stat-icon"></i>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.month_users }}</div>
            <div class="stat-label">本月新增</div>
          </div>
          <i class="el-icon-date stat-icon"></i>
        </el-card>
      </el-col>
      <el-col :span="4">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total_balance.toFixed(2) }}</div>
            <div class="stat-label">总余额(元)</div>
          </div>
          <i class="el-icon-money stat-icon"></i>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索筛选区域 -->
    <div class="filter-container">
      <el-input v-model="listQuery.keyword" placeholder="用户昵称/手机号" clearable class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.status" placeholder="状态" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.is_leader" placeholder="是否团长" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in leaderOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button class="filter-item" type="success" icon="el-icon-refresh" @click="handleRefresh">刷新</el-button>
    </div>

    <!-- 批量操作区域 -->
    <div class="batch-actions" v-if="selectedUsers.length > 0">
      <span class="batch-info">已选择 {{ selectedUsers.length }} 个用户</span>
      <el-button size="small" type="success" @click="handleBatchEnable">批量启用</el-button>
      <el-button size="small" type="warning" @click="handleBatchDisable">批量禁用</el-button>
      <el-button size="small" type="primary" @click="showBatchLevelDialog = true">设置等级</el-button>
      <el-button size="small" type="info" @click="showBatchTagDialog = true">分配标签</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="正在加载..."
      border
      fit
      highlight-current-row
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" prop="id" align="center" width="80" />
      <el-table-column label="头像" align="center" width="80">
        <template slot-scope="{row}">
          <el-avatar :src="row.avatar" :size="40">
            <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"/>
          </el-avatar>
        </template>
      </el-table-column>
      <el-table-column label="昵称" prop="nickname" align="center" min-width="120" />
      <el-table-column label="手机号" prop="phone" align="center" min-width="120" />
      <el-table-column label="余额" align="center" width="120">
        <template slot-scope="{row}">
          <span>{{ row.balance }} 元</span>
        </template>
      </el-table-column>
      <el-table-column label="是否团长" align="center" width="100">
        <template slot-scope="{row}">
          <el-tag :type="row.is_leader === 1 ? 'success' : 'info'">
            {{ row.is_leader === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="用户等级" prop="level" align="center" width="100" />
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 1 ? 'success' : 'info'">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="注册时间" align="center" width="160">
        <template slot-scope="{row}">
          <span>{{ row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleView(row)">查看</el-button>
          <el-button type="info" size="mini" @click="handleEdit(row)">编辑</el-button>
          <el-button type="warning" size="mini" @click="handleBalanceAdjust(row)">余额</el-button>
          <el-button v-if="row.status === 1" size="mini" type="danger" @click="handleStatusChange(row, 0)">禁用</el-button>
          <el-button v-else size="mini" type="success" @click="handleStatusChange(row, 1)">启用</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="safeTotal>0"
      :total="safeTotal"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 编辑用户对话框 -->
    <el-dialog title="编辑用户信息" :visible.sync="editDialogVisible" width="500px">
      <el-form ref="editForm" :model="editForm" :rules="editRules" label-width="100px">
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editForm.nickname" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" />
        </el-form-item>
        <el-form-item label="用户等级" prop="level">
          <el-input-number v-model="editForm.level" :min="0" :max="10" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEdit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 余额调整对话框 -->
    <el-dialog title="调整用户余额" :visible.sync="balanceDialogVisible" width="400px">
      <el-form ref="balanceForm" :model="balanceForm" :rules="balanceRules" label-width="100px">
        <el-form-item label="当前余额">
          <span>{{ currentUser.balance }} 元</span>
        </el-form-item>
        <el-form-item label="操作类型" prop="type">
          <el-radio-group v-model="balanceForm.type">
            <el-radio label="add">增加</el-radio>
            <el-radio label="subtract">减少</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整金额" prop="amount">
          <el-input-number v-model="balanceForm.amount" :min="0.01" :step="0.01" :precision="2" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="balanceForm.remark" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="balanceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBalanceAdjust">确定</el-button>
      </div>
    </el-dialog>

    <!-- 批量设置等级对话框 -->
    <el-dialog title="批量设置用户等级" :visible.sync="showBatchLevelDialog" width="400px">
      <el-form label-width="100px">
        <el-form-item label="用户等级">
          <el-input-number v-model="batchLevel" :min="0" :max="10" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showBatchLevelDialog = false">取消</el-button>
        <el-button type="primary" @click="handleBatchSetLevel">确定</el-button>
      </div>
    </el-dialog>

    <!-- 批量分配标签对话框 -->
    <el-dialog title="批量分配标签" :visible.sync="showBatchTagDialog" width="500px">
      <el-form label-width="100px">
        <el-form-item label="选择标签">
          <el-checkbox-group v-model="selectedTags">
            <el-checkbox v-for="tag in availableTags" :key="tag.id" :label="tag.id">
              {{ tag.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showBatchTagDialog = false">取消</el-button>
        <el-button type="primary" @click="handleBatchAssignTags">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserList, updateUser, updateUserStatus, getUserStats, adjustUserBalance, batchOperateUsers, getUserTagList } from '@/api/user-manage'
import Pagination from '@/components/Pagination'

export default {
  name: 'UserList',
  components: { Pagination },
  data () {
    return {
      list: null,
      total: 0,
      listLoading: true,
      selectedUsers: [],
      stats: {
        total_users: 0,
        active_users: 0,
        leader_users: 0,
        today_users: 0,
        month_users: 0,
        total_balance: 0
      },
      listQuery: {
        page: 1,
        limit: 10,
        keyword: undefined,
        status: undefined,
        is_leader: undefined
      },
      statusOptions: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ],
      leaderOptions: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ],
      editDialogVisible: false,
      editForm: {
        id: null,
        nickname: '',
        phone: '',
        level: 0,
        status: 1
      },
      editRules: {
        nickname: [
          { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },
      balanceDialogVisible: false,
      balanceForm: {
        type: 'add',
        amount: 0,
        remark: ''
      },
      balanceRules: {
        amount: [
          { required: true, message: '请输入调整金额', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
        ]
      },
      currentUser: {},
      showBatchLevelDialog: false,
      batchLevel: 1,
      showBatchTagDialog: false,
      selectedTags: [],
      availableTags: []
    }
  },
  computed: {
    // 确保total始终是数字类型，避免分页组件报错
    safeTotal() {
      // 多重保护确保返回有效数字
      if (this.total === null || this.total === undefined) {
        return 0
      }
      if (typeof this.total === 'string') {
        const parsed = parseInt(this.total, 10)
        return isNaN(parsed) ? 0 : parsed
      }
      if (typeof this.total === 'number' && !isNaN(this.total)) {
        return Math.max(0, Math.floor(this.total))
      }
      return 0
    }
  },
  created () {
    this.getStats()
    this.getList()
    this.loadTags()
  },
  methods: {
    getStats () {
      getUserStats().then(response => {
        this.stats = response.data
      }).catch(() => {
        // 使用默认值
      })
    },
    getList () {
      this.listLoading = true
      getUserList(this.listQuery).then(response => {
        this.list = response.data.list || []
        this.total = response.data.total || 0
        this.listLoading = false
      }).catch(error => {
        console.error('获取用户列表失败:', error)
        this.list = []
        this.total = 0
        this.listLoading = false
        this.$message.error('获取用户列表失败')
      })
    },
    loadTags () {
      getUserTagList().then(response => {
        this.availableTags = response.data || []
      }).catch(() => {
        // 忽略错误
      })
    },
    handleFilter () {
      this.listQuery.page = 1
      this.getList()
    },
    handleRefresh () {
      this.getStats()
      this.getList()
    },
    handleSelectionChange (selection) {
      this.selectedUsers = selection
    },
    handleView (row) {
      this.$router.push(`/user/detail/${row.id}`)
    },
    handleEdit (row) {
      this.editForm = {
        id: row.id,
        nickname: row.nickname,
        phone: row.phone,
        level: row.level,
        status: row.status
      }
      this.editDialogVisible = true
    },
    submitEdit () {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          updateUser(this.editForm.id, this.editForm).then(response => {
            this.$message.success('更新成功')
            this.editDialogVisible = false
            this.getList()
          }).catch(() => {
            this.$message.error('更新失败')
          })
        }
      })
    },
    handleStatusChange (row, status) {
      updateUserStatus(row.id, { status }).then(response => {
        this.$message.success('状态更新成功')
        row.status = status
        this.getStats() // 刷新统计数据
      }).catch(() => {
        this.$message.error('状态更新失败')
      })
    },
    handleBalanceAdjust (row) {
      this.currentUser = row
      this.balanceForm = {
        type: 'add',
        amount: 0,
        remark: ''
      }
      this.balanceDialogVisible = true
    },
    submitBalanceAdjust () {
      this.$refs.balanceForm.validate(valid => {
        if (valid) {
          adjustUserBalance(this.currentUser.id, this.balanceForm).then(response => {
            this.$message.success('余额调整成功')
            this.balanceDialogVisible = false
            this.getList()
            this.getStats()
          }).catch(() => {
            this.$message.error('余额调整失败')
          })
        }
      })
    },
    handleBatchEnable () {
      this.batchOperate('enable')
    },
    handleBatchDisable () {
      this.batchOperate('disable')
    },
    handleBatchSetLevel () {
      this.batchOperate('set_level', { level: this.batchLevel })
      this.showBatchLevelDialog = false
    },
    handleBatchAssignTags () {
      // 这里需要为每个选中的用户分配标签
      this.$message.success('标签分配功能开发中')
      this.showBatchTagDialog = false
    },
    batchOperate (action, data = {}) {
      const userIds = this.selectedUsers.map(user => user.id)
      batchOperateUsers({
        user_ids: userIds,
        action: action,
        data: data
      }).then(response => {
        this.$message.success('批量操作成功')
        this.getList()
        this.getStats()
      }).catch(() => {
        this.$message.error('批量操作失败')
      })
    }
  }
}
</script>

<style scoped>
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.stat-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 30px;
  color: #E6E8EB;
}

.batch-actions {
  margin-bottom: 20px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.batch-info {
  margin-right: 15px;
  color: #606266;
}
</style>
