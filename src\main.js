import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import 'normalize.css/normalize.css'
import './assets/styles/index.scss'
import './permission' // 权限控制

// 导入全局组件
import QrcodeGenerator from '@/components/qrcode'

// 导入全局过滤器
import * as filters from './filters'

Vue.use(ElementUI, { size: 'medium' })
Vue.use(QrcodeGenerator)

// 注册全局过滤器
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
