<!--pages/user/wallet/wallet.wxml-->
<view class="wallet-container">
  <!-- 账户余额区域 -->
  <view class="balance-section">
    <view class="balance-title">账户余额</view>
    <view class="balance-amount">¥{{balance}}</view>
    <view class="balance-subtitle">可提现金额</view>
  </view>

  <!-- 操作按钮区域 -->
  <view class="action-buttons">
    <view class="action-button" bindtap="onRecharge">
      <text class="action-icon">💰</text>
      <text class="action-text">充值</text>
    </view>
    <view class="action-button" bindtap="onWithdraw">
      <text class="action-icon">💸</text>
      <text class="action-text">提现</text>
    </view>
    <view class="action-button" bindtap="onViewDetails">
      <text class="action-icon">📊</text>
      <text class="action-text">明细</text>
    </view>
  </view>

  <!-- 收益来源区域 -->
  <view class="income-section">
    <view class="section-title">收益来源</view>
    <view class="income-item">
      <text class="income-label">WiFi分享收益:</text>
      <text class="income-value">¥{{incomeStats.wifi}}</text>
    </view>
    <view class="income-item">
      <text class="income-label">团队收益:</text>
      <text class="income-value">¥{{incomeStats.team}}</text>
    </view>
    <view class="income-item">
      <text class="income-label">广告流量收益:</text>
      <text class="income-value">¥{{incomeStats.ads}}</text>
    </view>
    <view class="income-item">
      <text class="income-label">商城订单收益:</text>
      <text class="income-value">¥{{incomeStats.mall}}</text>
    </view>
  </view>

  <!-- 收支明细区域 -->
  <view class="transactions-section">
    <view class="section-title">收支明细</view>
    <block wx:if="{{transactions.length > 0}}">
      <view class="transaction-item" wx:for="{{transactions}}" wx:key="id">
        <view class="transaction-info">
          <view class="transaction-title">{{item.title}}</view>
          <view class="transaction-time">{{item.createTime}}</view>
        </view>
        <view class="transaction-amount {{item.type === 'income' ? 'income' : 'expense'}}">
          {{item.type === 'income' ? '+' : '-'}}¥{{item.amount}}
        </view>
      </view>
      <view class="view-more" bindtap="onViewMore" wx:if="{{hasMore}}">
        查看更多 >
      </view>
    </block>
    <view class="empty-transactions" wx:else>
      <text>暂无交易记录</text>
    </view>
  </view>

  <!-- 广告位 -->
  <view class="ad-container">
    <ad unit-id="adunit-id" ad-type="banner" ad-theme="white" bindload="onAdLoad" binderror="onAdError"></ad>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view> 