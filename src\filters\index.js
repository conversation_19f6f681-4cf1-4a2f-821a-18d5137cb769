/**
 * 全局过滤器
 */

/**
 * 解析时间格式化
 * @param {string|Date} time 时间
 * @param {string} cFormat 格式模板
 * @returns {string} 格式化后的时间字符串
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string')) {
      if ((/^[0-9]+$/.test(time))) {
        // 纯数字字符串，当作时间戳处理
        time = parseInt(time)
      } else {
        // 字符串时间，替换中划线为斜杠，兼容safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }
    
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    
    date = new Date(time)
  }
  
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // 注意：getDay() 返回的是 0-6，其中 0 代表周日
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  
  return time_str
}

/**
 * 格式化时间为相对时间
 * @param {string|Date} time 时间
 * @returns {string} 相对时间字符串
 */
export function timeAgo(time) {
  const between = Date.now() / 1000 - Number(new Date(time)) / 1000
  if (between < 3600) {
    return pluralize(~~(between / 60), ' minute')
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), ' hour')
  } else {
    return pluralize(~~(between / 86400), ' day')
  }
}

/**
 * 复数处理
 * @param {number} time 时间数值
 * @param {string} label 标签
 * @returns {string} 处理后的字符串
 */
function pluralize(time, label) {
  if (time === 1) {
    return time + label
  }
  return time + label + 's'
}

/**
 * 格式化文件大小
 * @param {number} size 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(size) {
  if (size === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return (size / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i]
}

/**
 * 格式化数字，添加千分位分隔符
 * @param {number} num 数字
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber(num) {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 格式化金额
 * @param {number} amount 金额
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的金额字符串
 */
export function formatMoney(amount, decimals = 2) {
  if (isNaN(amount)) return '0.00'
  return Number(amount).toFixed(decimals)
}

/**
 * 截取字符串
 * @param {string} str 字符串
 * @param {number} length 长度
 * @returns {string} 截取后的字符串
 */
export function truncate(str, length = 50) {
  if (!str) return ''
  if (str.length <= length) return str
  return str.substring(0, length) + '...'
}

/**
 * 格式化状态
 * @param {string|number} status 状态值
 * @returns {string} 状态文本
 */
export function formatStatus(status) {
  const statusMap = {
    0: '待审核',
    1: '已通过',
    2: '已拒绝',
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝',
    'active': '激活',
    'inactive': '未激活',
    'enabled': '启用',
    'disabled': '禁用'
  }
  return statusMap[status] || status
}

/**
 * 格式化用户类型
 * @param {string|number} type 用户类型
 * @returns {string} 用户类型文本
 */
export function formatUserType(type) {
  const typeMap = {
    0: '普通用户',
    1: '团队长',
    2: '管理员',
    'user': '普通用户',
    'leader': '团队长',
    'admin': '管理员'
  }
  return typeMap[type] || type
}
