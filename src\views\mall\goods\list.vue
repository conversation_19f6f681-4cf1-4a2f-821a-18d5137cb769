<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.keyword" placeholder="商品名称/标题" clearable class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.category_id" placeholder="分类" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="状态" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleCreate">新增商品</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="正在加载..."
      border
      fit
      highlight-current-row
    >
      <el-table-column label="ID" prop="id" align="center" width="80" />
      <el-table-column label="封面" align="center" width="100">
        <template slot-scope="{row}">
          <el-image
            style="width: 60px; height: 60px"
            :src="formatImageUrl(row.cover)"
            :preview-src-list="[formatImageUrl(row.cover)]"
            fit="cover">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="标题" prop="title" align="center" min-width="180" />
      <el-table-column label="分类" prop="categoryName" align="center" width="120" />
      <el-table-column label="价格" align="center" width="120">
        <template slot-scope="{row}">
          <span>{{ row.price }} 元</span>
        </template>
      </el-table-column>
      <el-table-column label="库存" prop="stock" align="center" width="80" />
      <el-table-column label="销量" prop="sales" align="center" width="80" />
      <el-table-column label="推荐" align="center" width="80">
        <template slot-scope="{row}">
          <el-tag v-if="row.isRecommend" type="success">是</el-tag>
          <el-tag v-else type="info">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 1 ? 'success' : 'info'">
            {{ row.status === 1 ? '上架' : '下架' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleView(row)">查看</el-button>
          <el-button type="success" size="mini" @click="handleUpdate(row)">编辑</el-button>
          <el-button v-if="row.status === 1" size="mini" type="warning" @click="handleStatusChange(row, 0)">下架</el-button>
          <el-button v-else size="mini" type="info" @click="handleStatusChange(row, 1)">上架</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getGoodsList, updateGoodsStatus, deleteGoods, formatImageUrl } from '@/api/goods'
import Pagination from '@/components/Pagination'

export default {
  name: 'GoodsList',
  components: { Pagination },
  data () {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        keyword: undefined,
        category_id: undefined,
        status: undefined
      },
      statusOptions: [
        { label: '上架', value: 1 },
        { label: '下架', value: 0 }
      ],
      categoryOptions: [
        { label: '数码产品', value: 1 },
        { label: '家居用品', value: 2 },
        { label: '美妆护肤', value: 3 },
        { label: '食品饮料', value: 4 }
      ]
    }
  },
  created () {
    this.getList()
  },
  methods: {
    // 格式化图片URL
    formatImageUrl,
    getList () {
      this.listLoading = true
      getGoodsList(this.listQuery).then(response => {
        console.log('商品列表响应:', response)
        // 检查返回的数据结构
        if (response.data && response.data.list) {
          // 标准API返回格式
          this.list = response.data.list
          this.total = response.data.pagination ? response.data.pagination.total : 0
          console.log('处理后的商品列表:', this.list)
        } else if (response.data && Array.isArray(response.data)) {
          // 测试数据格式
          this.list = response.data
          this.total = response.data.length
          console.log('处理后的商品列表(测试数据):', this.list)
        } else {
          this.list = []
          this.total = 0
          console.warn('未识别的数据格式:', response)
        }
        this.listLoading = false
      }).catch(err => {
        console.error('获取商品列表失败:', err)
        this.list = []
        this.total = 0
        this.listLoading = false
      })
    },
    handleFilter () {
      this.listQuery.page = 1
      this.getList()
    },
    handleCreate () {
      this.$router.push('/mall/goods/create')
    },
    handleUpdate (row) {
      this.$router.push(`/mall/goods/edit/${row.id}`)
    },
    handleView (row) {
      this.$router.push(`/mall/goods/detail/${row.id}`)
    },
    handleStatusChange (row, status) {
      updateGoodsStatus(row.id, { status }).then(response => {
        this.$message.success('状态更新成功')
        // 直接修改本地状态可能导致界面不更新，改为重新获取列表
        this.getList()
      }).catch(() => {
        this.$message.error('状态更新失败')
      })
    },
    handleDelete (row) {
      // 添加调试信息
      console.log('准备删除商品:', row)
      
      // 检查商品ID是否存在
      if (!row || !row.id) {
        this.$message.error('无效的商品ID')
        return
      }
      
      this.$confirm('确认要删除这个商品吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log(`正在调用删除API，商品ID: ${row.id}`)
        deleteGoods(row.id).then(response => {
          console.log('删除商品成功，响应:', response)
          this.$message.success('删除成功')
          this.getList()
        }).catch((error) => {
          console.error('删除商品失败:', error)
          if (error.response && error.response.status === 404) {
            this.$message.error('商品不存在或已被删除')
            // 刷新列表，因为商品可能已经不存在了
            this.getList()
          } else {
            this.$message.error(`删除失败: ${error.message || '未知错误'}`)
          }
        })
      }).catch(() => {
        // 取消删除
        console.log('用户取消了删除操作')
      })
    }
  }
}
</script>
