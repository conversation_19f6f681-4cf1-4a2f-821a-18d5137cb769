<template>
  <div class="app-container">
    <div v-loading="loading" class="order-detail">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>订单信息</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
        </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ orderInfo.order_no }}</el-descriptions-item>
          <el-descriptions-item label="下单时间">{{ orderInfo.created_at }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getOrderStatusType(orderInfo.status)">{{ getOrderStatusText(orderInfo.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">
            <el-tag v-if="orderInfo.payment_method === 1" type="primary">微信支付</el-tag>
            <el-tag v-else-if="orderInfo.payment_method === 2" type="success">余额支付</el-tag>
            <el-tag v-else type="info">其他</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ orderInfo.payment_time || '-' }}</el-descriptions-item>
          <el-descriptions-item label="发货时间">{{ orderInfo.shipping_time || '-' }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ orderInfo.completion_time || '-' }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{ orderInfo.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>用户信息</span>
            </div>
            <div class="user-info">
              <div class="info-item">
                <span class="label">用户ID：</span>
                <span class="value">{{ orderInfo.user_id }}</span>
              </div>
              <div class="info-item">
                <span class="label">用户昵称：</span>
                <span class="value">{{ userInfo.nickname || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">手机号：</span>
                <span class="value">{{ userInfo.phone || '-' }}</span>
              </div>
              <div class="info-item">
                <el-button type="text" @click="viewUser">查看用户详情</el-button>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>收货信息</span>
            </div>
            <div class="address-info">
              <div class="info-item">
                <span class="label">收货人：</span>
                <span class="value">{{ orderInfo.receiver_name }}</span>
              </div>
              <div class="info-item">
                <span class="label">联系电话：</span>
                <span class="value">{{ orderInfo.receiver_phone }}</span>
              </div>
              <div class="info-item">
                <span class="label">收货地址：</span>
                <span class="value">{{ orderInfo.receiver_address }}</span>
              </div>
              <div v-if="hasLogistics" class="info-item logistics">
                <span class="label">物流信息：</span>
                <div class="value">
                  <p>物流公司：{{ logisticsInfo.company_name }}</p>
                  <p>物流单号：{{ logisticsInfo.logistics_no }}</p>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>商品信息</span>
        </div>
        <el-table :data="goodsList" border style="width: 100%">
          <el-table-column label="商品图片" width="100" align="center">
            <template slot-scope="{row}">
              <el-image
                style="width: 60px; height: 60px"
                :src="row.goods_cover"
                fit="cover">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column prop="goods_title" label="商品名称" min-width="200" />
          <el-table-column prop="specs_name" label="规格" width="100" align="center" />
          <el-table-column prop="price" label="单价" width="100" align="center">
            <template slot-scope="{row}">
              <span>{{ row.price }} 元</span>
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量" width="100" align="center" />
          <el-table-column label="小计" width="100" align="center">
            <template slot-scope="{row}">
              <span>{{ (row.price * row.quantity).toFixed(2) }} 元</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="order-amount">
          <div class="amount-item">
            <span class="amount-label">商品总金额：</span>
            <span class="amount-value">¥ {{ orderInfo.goods_amount || 0 }}</span>
          </div>
          <div class="amount-item">
            <span class="amount-label">运费：</span>
            <span class="amount-value">¥ {{ orderInfo.shipping_fee || 0 }}</span>
          </div>
          <div class="amount-item">
            <span class="amount-label">优惠金额：</span>
            <span class="amount-value discount">- ¥ {{ orderInfo.discount_amount || 0 }}</span>
          </div>
          <div class="amount-item total">
            <span class="amount-label">订单总金额：</span>
            <span class="amount-value total">¥ {{ orderInfo.total_amount || 0 }}</span>
          </div>
        </div>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>操作</span>
        </div>
        <div class="action-buttons">
          <el-button v-if="orderInfo.status === 1" type="success" @click="handleShip">发货</el-button>
          <el-button v-if="orderInfo.status === 0" type="danger" @click="handleCancel">取消订单</el-button>
          <el-button v-if="orderInfo.status === 2" type="primary" @click="handleComplete">完成订单</el-button>
          <el-button type="info" @click="printOrder">打印订单</el-button>
        </div>
      </el-card>
    </div>

    <!-- 发货对话框 -->
    <el-dialog title="订单发货" :visible.sync="shipDialogVisible" width="500px">
      <el-form ref="shipForm" :model="shipForm" :rules="shipRules" label-width="100px">
        <el-form-item label="物流公司" prop="logistics_company">
          <el-select v-model="shipForm.logistics_company" placeholder="请选择物流公司" style="width: 100%">
            <el-option v-for="item in logisticsOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号" prop="logistics_no">
          <el-input v-model="shipForm.logistics_no" placeholder="请输入物流单号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="shipDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmShip">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOrderDetail, updateOrderStatus, updateOrderLogistics } from '@/api/order'

export default {
  name: 'OrderDetail',
  data () {
    return {
      loading: true,
      orderId: null,
      orderInfo: {},
      userInfo: {},
      goodsList: [],
      logisticsInfo: {},
      hasLogistics: false,
      shipDialogVisible: false,
      shipForm: {
        logistics_company: '',
        logistics_no: ''
      },
      shipRules: {
        logistics_company: [
          { required: true, message: '请选择物流公司', trigger: 'change' }
        ],
        logistics_no: [
          { required: true, message: '请输入物流单号', trigger: 'blur' },
          { min: 5, message: '物流单号长度不能少于5个字符', trigger: 'blur' }
        ]
      },
      logisticsOptions: [
        { label: '顺丰速运', value: 'SF' },
        { label: '中通快递', value: 'ZTO' },
        { label: '圆通速递', value: 'YTO' },
        { label: '申通快递', value: 'STO' },
        { label: '韵达快递', value: 'YD' },
        { label: '天天快递', value: 'TTKD' },
        { label: '百世快递', value: 'HTKY' },
        { label: '邮政快递包裹', value: 'YZPY' },
        { label: 'EMS', value: 'EMS' }
      ]
    }
  },
  created () {
    this.orderId = parseInt(this.$route.params.id)
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      getOrderDetail(this.orderId).then(response => {
        const { order_info, user_info, goods_list, logistics_info } = response.data
        this.orderInfo = order_info
        this.userInfo = user_info
        this.goodsList = goods_list

        if (logistics_info && logistics_info.logistics_no) {
          this.logisticsInfo = logistics_info
          this.hasLogistics = true
        }

        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    getOrderStatusType (status) {
      const statusMap = {
        0: 'info',
        1: 'primary',
        2: 'warning',
        3: 'success',
        4: 'danger'
      }
      return statusMap[status] || 'info'
    },
    getOrderStatusText (status) {
      const statusMap = {
        0: '待支付',
        1: '待发货',
        2: '待收货',
        3: '已完成',
        4: '已取消'
      }
      return statusMap[status] || '未知状态'
    },
    goBack () {
      this.$router.push('/mall/order')
    },
    viewUser () {
      this.$router.push(`/user/detail/${this.orderInfo.user_id}`)
    },
    handleShip () {
      this.shipDialogVisible = true
    },
    confirmShip () {
      this.$refs.shipForm.validate(valid => {
        if (valid) {
          updateOrderLogistics(this.orderId, {
            status: 2, // 更新为已发货状态
            logistics_company: this.shipForm.logistics_company,
            logistics_no: this.shipForm.logistics_no
          }).then(response => {
            this.$message.success('发货成功')
            this.shipDialogVisible = false
            this.fetchData()
          }).catch(() => {
            this.$message.error('发货失败')
          })
        }
      })
    },
    handleCancel () {
      this.$confirm('确认要取消该订单吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateOrderStatus(this.orderId, { status: 4 }).then(response => {
          this.$message.success('订单取消成功')
          this.fetchData()
        }).catch(() => {
          this.$message.error('订单取消失败')
        })
      }).catch(() => {
        // 取消操作
      })
    },
    handleComplete () {
      this.$confirm('确认要将订单标记为已完成吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateOrderStatus(this.orderId, { status: 3 }).then(response => {
          this.$message.success('订单已完成')
          this.fetchData()
        }).catch(() => {
          this.$message.error('操作失败')
        })
      }).catch(() => {
        // 取消操作
      })
    },
    printOrder () {
      this.$message.info('打印功能暂未实现')
      // 这里应该实现打印功能
    }
  }
}
</script>

<style scoped>
.info-item {
  margin-bottom: 15px;
  font-size: 14px;
}
.label {
  font-weight: bold;
  margin-right: 10px;
}
.value {
  color: #606266;
}
.logistics p {
  margin: 5px 0;
}
.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}
.order-amount {
  margin-top: 20px;
  text-align: right;
  padding-right: 20px;
}
.amount-item {
  margin-bottom: 10px;
}
.amount-label {
  font-weight: bold;
  margin-right: 10px;
}
.amount-value {
  color: #606266;
}
.amount-value.discount {
  color: #67c23a;
}
.amount-item.total {
  margin-top: 20px;
  font-size: 16px;
}
.amount-value.total {
  color: #f56c6c;
  font-size: 18px;
  font-weight: bold;
}
</style>
