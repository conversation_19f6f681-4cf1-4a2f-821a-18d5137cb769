<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select v-model="listQuery.status" placeholder="申请状态" clearable style="width: 120px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="ID" align="center" width="80">
        <template slot-scope="scope">
          {{ scope.row.id }}
        </template>
      </el-table-column>
      <el-table-column label="用户信息" width="180">
        <template slot-scope="scope">
          <div class="user-info">
            <el-avatar :size="40" :src="scope.row.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" />
            <div class="user-detail">
              <div>{{ scope.row.nickname || '未设置昵称' }}</div>
              <div class="user-phone">{{ scope.row.user_phone || '未绑定手机' }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="团队名称" width="180">
        <template slot-scope="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column label="联系电话" width="150">
        <template slot-scope="scope">
          {{ scope.row.phone }}
        </template>
      </el-table-column>
      <el-table-column label="区域" width="150">
        <template slot-scope="scope">
          {{ scope.row.area }}
        </template>
      </el-table-column>
      <el-table-column label="申请时间" width="180">
        <template slot-scope="scope">
          {{ scope.row.created_at | parseTime('{y}-{m}-{d} {h}:{i}') }}
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status | statusFilter">
            {{ scope.row.status | statusTextFilter }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="240" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="handleDetail(scope.row)">
            详情
          </el-button>
          <el-button v-if="scope.row.status === 0" type="success" size="mini" @click="handleApprove(scope.row)">
            通过
          </el-button>
          <el-button v-if="scope.row.status === 0" type="danger" size="mini" @click="handleReject(scope.row)">
            拒绝
          </el-button>
          <el-button v-if="scope.row.status === 2" type="danger" size="mini" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 审核对话框 -->
    <el-dialog :title="auditDialogTitle" :visible.sync="auditDialogVisible" width="30%">
      <el-form :model="auditForm" label-width="80px">
        <el-form-item label="备注">
          <el-input v-model="auditForm.remark" type="textarea" :rows="4" placeholder="请输入审核备注"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitAudit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchAllianceList, auditAlliance, deleteAlliance } from '@/api/alliance'
import waves from '@/directive/waves/index'
import Pagination from '@/components/Pagination'

export default {
  name: 'AllianceList',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        0: 'info',
        1: 'success',
        2: 'danger'
      }
      return statusMap[status]
    },
    statusTextFilter(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已拒绝'
      }
      return statusMap[status]
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        status: undefined
      },
      statusOptions: [
        { label: '待审核', value: 0 },
        { label: '已通过', value: 1 },
        { label: '已拒绝', value: 2 }
      ],
      auditDialogVisible: false,
      auditDialogTitle: '审核',
      auditForm: {
        id: null,
        status: null,
        remark: ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      fetchAllianceList(this.listQuery).then(response => {
        this.list = response.data.list || []
        this.total = (response.data.pagination && response.data.pagination.total) || response.data.total || 0
        this.listLoading = false
      }).catch(error => {
        console.error('获取联盟申请列表失败:', error)
        this.list = []
        this.total = 0
        this.listLoading = false
        this.$message.error('获取联盟申请列表失败')
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleDetail(row) {
      this.$router.push({ path: `/user/alliance/detail/${row.id}` })
    },
    handleApprove(row) {
      this.auditDialogTitle = '通过申请'
      this.auditForm = {
        id: row.id,
        status: 1,
        remark: ''
      }
      this.auditDialogVisible = true
    },
    handleReject(row) {
      this.auditDialogTitle = '拒绝申请'
      this.auditForm = {
        id: row.id,
        status: 2,
        remark: ''
      }
      this.auditDialogVisible = true
    },
    submitAudit() {
      auditAlliance(this.auditForm.id, {
        status: this.auditForm.status,
        remark: this.auditForm.remark
      }).then(response => {
        this.$message({
          type: 'success',
          message: '审核成功!'
        })
        this.auditDialogVisible = false
        this.getList()
      })
    },
    handleDelete(row) {
      this.$confirm('确定要删除该申请吗？删除后不可恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAlliance(row.id).then(response => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 10px;
  .filter-item {
    margin-right: 10px;
  }
}
.user-info {
  display: flex;
  align-items: center;
  .user-detail {
    margin-left: 10px;
    .user-phone {
      font-size: 12px;
      color: #909399;
    }
  }
}
</style> 