(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e77d8caa"],{5302:function(t,a,e){"use strict";e.d(a,"b",(function(){return r})),e.d(a,"d",(function(){return i})),e.d(a,"e",(function(){return o})),e.d(a,"a",(function(){return l})),e.d(a,"c",(function(){return n}));var s=e("b775");function r(){return Object(s["a"])({url:"/api/v1/admin/platform/overview",method:"get"})}function i(t){return Object(s["a"])({url:"/api/v1/admin/platform/revenue-trend",method:"get",params:t})}function o(t){return Object(s["a"])({url:"/api/v1/admin/platform/user-growth",method:"get",params:t})}function l(){return Object(s["a"])({url:"/api/v1/admin/platform/business-type",method:"get"})}function n(){return Object(s["a"])({url:"/api/v1/admin/platform/region",method:"get"})}},"58b3":function(t,a,e){},"8ff8":function(t,a,e){"use strict";e("58b3")},9406:function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t._self._c;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"dashboard-container"},[a("div",{staticClass:"dashboard-header"},[a("div",{staticClass:"dashboard-text"},[t._v("欢迎使用 WIFI共享商业系统管理后台")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-refresh",loading:t.loading},on:{click:t.fetchDashboardData}},[t._v(" 刷新数据 ")])],1),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("总用户数")])]),a("div",{staticClass:"card-item"},[a("div",{staticClass:"card-item-value"},[t._v(t._s(t.userTotal))]),a("div",{staticClass:"card-item-label"},[t._v("用户总数")])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("WIFI码总数")])]),a("div",{staticClass:"card-item"},[a("div",{staticClass:"card-item-value"},[t._v(t._s(t.wifiTotal))]),a("div",{staticClass:"card-item-label"},[t._v("WIFI码总数")])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("订单总数")])]),a("div",{staticClass:"card-item"},[a("div",{staticClass:"card-item-value"},[t._v(t._s(t.orderTotal))]),a("div",{staticClass:"card-item-label"},[t._v("订单总数")])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("总收入")])]),a("div",{staticClass:"card-item"},[a("div",{staticClass:"card-item-value"},[t._v("¥"+t._s(t.totalIncome))]),a("div",{staticClass:"card-item-label"},[t._v("总收入")])])])],1)],1),a("div",{staticStyle:{"margin-top":"20px"}},[a("el-card",{staticClass:"chart-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("销售趋势")]),a("el-radio-group",{staticStyle:{float:"right"},attrs:{size:"mini"},model:{value:t.chartType,callback:function(a){t.chartType=a},expression:"chartType"}},[a("el-radio-button",{attrs:{label:"week"}},[t._v("本周")]),a("el-radio-button",{attrs:{label:"month"}},[t._v("本月")]),a("el-radio-button",{attrs:{label:"year"}},[t._v("全年")])],1)],1),a("div",{staticClass:"chart-container"},[a("div",{ref:"salesChart",staticStyle:{width:"100%",height:"400px"}})])])],1)],1)},r=[],i=(e("14d9"),e("5302")),o=e("313e"),l={name:"Dashboard",data(){return{userTotal:0,wifiTotal:0,orderTotal:0,totalIncome:"0.00",chartType:"week",chartInstance:null,loading:!1}},mounted(){this.fetchDashboardData(),this.initChart()},watch:{chartType(){this.updateChart()}},methods:{async fetchDashboardData(){try{this.loading=!0;const{data:r}=await Object(i["b"])();var t,a,e,s;if(r)this.userTotal=(null===(t=r.users)||void 0===t?void 0:t.total_users)||0,this.wifiTotal=(null===(a=r.wifi)||void 0===a?void 0:a.total_wifi_codes)||0,this.orderTotal=(null===(e=r.orders)||void 0===e?void 0:e.total_orders)||0,this.totalIncome=(null===(s=r.revenue)||void 0===s?void 0:s.total_revenue)||"0.00",this.loading&&this.$message.success("数据刷新成功")}catch(r){console.error("获取仪表盘数据失败:",r),this.$message.error("获取仪表盘数据失败")}finally{this.loading=!1}},initChart(){this.$nextTick(()=>{this.$refs.salesChart&&(this.chartInstance=o["a"](this.$refs.salesChart),this.updateChart())})},updateChart(){if(!this.chartInstance)return;const t=this.generateChartData(),a={title:{text:"销售趋势",left:"center"},tooltip:{trigger:"axis"},legend:{data:["订单数","收益"],top:30},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:t.dates},yAxis:[{type:"value",name:"订单数",position:"left"},{type:"value",name:"收益(元)",position:"right"}],series:[{name:"订单数",type:"line",data:t.orders,smooth:!0,itemStyle:{color:"#409EFF"}},{name:"收益",type:"line",yAxisIndex:1,data:t.revenue,smooth:!0,itemStyle:{color:"#67C23A"}}]};this.chartInstance.setOption(a)},generateChartData(){const t=new Date,a=[],e=[],s=[];let r=7;"month"===this.chartType?r=30:"year"===this.chartType&&(r=365);for(let i=r-1;i>=0;i--){const r=new Date(t);r.setDate(r.getDate()-i),"year"===this.chartType?a.push(r.getMonth()+1+"月"):a.push(`${r.getMonth()+1}/${r.getDate()}`),e.push(Math.floor(20*Math.random())+5),s.push((1e3*Math.random()+200).toFixed(2))}return{dates:a,orders:e,revenue:s}}},beforeDestroy(){this.chartInstance&&this.chartInstance.dispose()}},n=l,c=(e("8ff8"),e("2877")),d=Object(c["a"])(n,s,r,!1,null,"22f31561",null);a["default"]=d.exports}}]);