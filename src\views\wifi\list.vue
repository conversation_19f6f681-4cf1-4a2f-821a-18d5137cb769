<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.keyword" placeholder="WiFi名称/标题" clearable class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.status" placeholder="状态" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleCreate">新增</el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="正在加载..."
      border
      fit
      highlight-current-row
    >
      <el-table-column label="ID" prop="id" align="center" width="80" />
      <el-table-column label="标题" prop="title" align="center" min-width="120" />
      <el-table-column label="WiFi名称" prop="name" align="center" min-width="120" />
      <el-table-column label="商户名称" prop="merchant_name" align="center" min-width="120" />
      <el-table-column label="使用次数" prop="use_count" align="center" width="100" />
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 1 ? 'success' : 'info'">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="160">
        <template slot-scope="{row}">
          <span>{{ row.created_at }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleView(row)">查看</el-button>
          <el-button type="success" size="mini" @click="handleUpdate(row)">编辑</el-button>
          <el-button v-if="row.status === 1" size="mini" type="warning" @click="handleStatusChange(row, 0)">禁用</el-button>
          <el-button v-else size="mini" type="info" @click="handleStatusChange(row, 1)">启用</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getWifiList, deleteWifi, updateWifiStatus } from '@/api/wifi'
import Pagination from '@/components/Pagination'

export default {
  name: 'WiFiList',
  components: { Pagination },
  data () {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        keyword: undefined,
        status: undefined
      },
      statusOptions: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.listLoading = true
      getWifiList(this.listQuery).then(response => {
        this.list = response.data.list || []
        // 修复分页组件total属性类型错误
        this.total = (response.data.pagination && response.data.pagination.total) || response.data.total || 0
        this.listLoading = false
      }).catch(error => {
        console.error('获取WiFi列表失败:', error)
        this.list = []
        this.total = 0
        this.listLoading = false
        this.$message.error('获取WiFi列表失败')
      })
    },
    handleFilter () {
      this.listQuery.page = 1
      this.getList()
    },
    handleCreate () {
      this.$router.push('/wifi/create')
    },
    handleUpdate (row) {
      this.$router.push(`/wifi/edit/${row.id}`)
    },
    handleView (row) {
      this.$router.push(`/wifi/detail/${row.id}`)
    },
    handleStatusChange (row, status) {
      updateWifiStatus(row.id, { status }).then(response => {
        this.$message.success('状态更新成功')
        row.status = status
      }).catch(() => {
        this.$message.error('状态更新失败')
      })
    },
    handleDelete (row) {
      this.$confirm('确认要删除这个WiFi码吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteWifi(row.id).then(response => {
          this.$message.success('删除成功')
          this.getList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        // 取消删除
      })
    }
  }
}
</script>
