# WiFi共享管理后台宝塔部署最终指南

## 🎯 问题解决状态

✅ **API路径重复问题已修复**
✅ **日志系统已添加**
✅ **生产环境配置已优化**
✅ **项目已重新构建**

## 📋 部署步骤

### 1. 上传文件到服务器

将以下文件上传到宝塔面板的网站目录（如：`/www/wwwroot/wifi-share-admin/`）：

```
wifi-share-admin/
├── dist/                    # 构建后的前端文件
├── logs/                    # 日志目录
├── node_modules/           # 依赖包
├── src/                    # 源代码
├── ecosystem.config.js     # PM2配置
├── package.json           # 项目配置
├── server.js              # 服务器文件
├── start.bat              # Windows启动脚本
├── stop.bat               # Windows停止脚本
└── 其他配置文件...
```

### 2. 在宝塔面板中配置

#### 方法A：使用PM2管理器（推荐）

1. 在宝塔面板中安装"PM2管理器"插件
2. 点击"添加项目"
3. 填写配置：
   - **项目名称**：wifi-share-admin
   - **启动文件**：server.js
   - **项目目录**：/www/wwwroot/wifi-share-admin
   - **运行用户**：www
   - **端口**：8081
   - **环境变量**：NODE_ENV=production

#### 方法B：使用命令行

```bash
# 进入项目目录
cd /www/wwwroot/wifi-share-admin

# 安装PM2（如果未安装）
npm install -g pm2

# 启动项目
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save
```

### 3. 配置Nginx反向代理（可选）

如果需要通过域名访问，在宝塔面板中配置Nginx：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 4. 验证部署

1. **检查服务状态**：
   ```bash
   pm2 status
   ```

2. **访问管理后台**：
   - 直接访问：http://**************:8081
   - 或通过域名：http://your-domain.com

3. **测试登录**：
   - 用户名：mrx0927
   - 密码：hh20250701

## 📊 日志查看

### 在宝塔面板中查看

1. **PM2日志**：
   - 进入PM2管理器
   - 找到wifi-share-admin项目
   - 点击"日志"查看

2. **文件管理器**：
   - 进入项目目录
   - 查看`logs/`文件夹
   - 主要日志文件：
     - `wifi-share-admin.log` - 综合日志
     - `error.log` - 错误日志
     - `info.log` - 信息日志

### 命令行查看

```bash
# 实时查看PM2日志
pm2 logs wifi-share-admin

# 查看应用日志文件
tail -f /www/wwwroot/wifi-share-admin/logs/wifi-share-admin.log

# 查看错误日志
tail -f /www/wwwroot/wifi-share-admin/logs/error.log
```

## 🔧 常用管理命令

```bash
# 查看状态
pm2 status

# 重启应用
pm2 restart wifi-share-admin

# 停止应用
pm2 stop wifi-share-admin

# 删除应用
pm2 delete wifi-share-admin

# 查看详细信息
pm2 show wifi-share-admin

# 监控资源使用
pm2 monit
```

## 🚨 故障排查

### 问题1：无法访问管理后台
**检查步骤**：
1. 确认PM2服务是否运行：`pm2 status`
2. 检查端口8081是否被占用：`netstat -tlnp | grep 8081`
3. 查看防火墙设置，确保8081端口开放
4. 检查日志：`pm2 logs wifi-share-admin`

### 问题2：登录失败
**检查步骤**：
1. 确认后端API服务是否运行（端口4000）
2. 检查数据库连接是否正常
3. 查看浏览器控制台网络请求
4. 检查API路径是否正确（不应该有重复的/api）

### 问题3：API路径重复
**解决方案**：
1. 清除浏览器缓存
2. 重新构建项目：`npm run build`
3. 重启服务：`pm2 restart wifi-share-admin`

## 📞 技术支持

如果遇到问题，请提供以下信息：

1. **错误截图**：浏览器控制台错误信息
2. **服务状态**：`pm2 status`输出
3. **日志内容**：相关错误日志
4. **网络请求**：浏览器开发者工具中的网络请求详情

## 🎉 部署完成

恭喜！WiFi共享管理后台已成功部署到宝塔面板。

- **访问地址**：http://**************:8081
- **管理员账号**：mrx0927
- **管理员密码**：hh20250701
- **日志位置**：/www/wwwroot/wifi-share-admin/logs/

现在您可以正常使用管理后台，并通过日志系统监控应用运行状态了！
