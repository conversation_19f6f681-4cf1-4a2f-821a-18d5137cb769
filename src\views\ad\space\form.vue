<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? '编辑广告位' : '创建广告位' }}</span>
      </div>
      <el-form ref="spaceForm" :model="spaceForm" :rules="rules" label-width="100px">
        <el-form-item label="广告位名称" prop="name">
          <el-input v-model="spaceForm.name" placeholder="请输入广告位名称" />
        </el-form-item>

        <el-form-item label="广告位编码" prop="code">
          <el-input v-model="spaceForm.code" placeholder="请输入广告位编码" :disabled="isEdit" />
          <div class="tip">广告位编码一经创建不可修改，如：home_banner</div>
        </el-form-item>

        <el-form-item label="宽度(px)" prop="width">
          <el-input-number v-model="spaceForm.width" :min="1" :max="2000" />
        </el-form-item>

        <el-form-item label="高度(px)" prop="height">
          <el-input-number v-model="spaceForm.height" :min="1" :max="2000" />
        </el-form-item>

        <el-form-item label="价格/天" prop="price">
          <el-input-number v-model="spaceForm.price" :min="0" :precision="2" :step="10" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            type="textarea"
            v-model="spaceForm.description"
            placeholder="请输入广告位描述"
            :rows="3" />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="spaceForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用">
          </el-switch>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="goBack">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { createAdSpace, updateAdSpace } from '@/api/advertisement'
import request from '@/utils/request'

export default {
  name: 'AdSpaceForm',
  data () {
    // 自定义校验规则 - 广告位编码
    const validateCode = (rule, value, callback) => {
      if (!/^[a-z0-9_]+$/.test(value)) {
        callback(new Error('广告位编码只能包含小写字母、数字和下划线'))
      } else {
        callback()
      }
    }

    return {
      isEdit: false,
      spaceId: undefined,
      spaceForm: {
        name: '',
        code: '',
        width: 300,
        height: 200,
        description: '',
        price: 0,
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入广告位名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入广告位编码', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
          { validator: validateCode, trigger: 'blur' }
        ],
        width: [
          { required: true, message: '请输入宽度', trigger: 'blur' },
          { type: 'number', message: '宽度必须为数字', trigger: 'blur' }
        ],
        height: [
          { required: true, message: '请输入高度', trigger: 'blur' },
          { type: 'number', message: '高度必须为数字', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入价格', trigger: 'blur' },
          { type: 'number', message: '价格必须为数字', trigger: 'blur' }
        ],
        description: [
          { max: 255, message: '长度不能超过 255 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    // 判断是编辑还是新增
    const id = this.$route.params && this.$route.params.id
    if (id) {
      this.isEdit = true
      this.spaceId = parseInt(id)
      this.getDetail(this.spaceId)
    }
  },
  methods: {
    getDetail (id) {
      // 从API获取广告位详情
      this.loading = true
      request({
        url: `/api/v1/admin/advertisement/spaces/${id}`,
        method: 'get'
      }).then(response => {
        if (response.status === 'success') {
          this.spaceForm = response.data
        } else {
          this.$message.error(response.message || '获取广告位详情失败')
          this.goBack()
        }
      }).catch(error => {
        const errorMsg = error.response && error.response.data && error.response.data.message
          ? error.response.data.message
          : '获取广告位详情失败'
        this.$message.error(errorMsg)
        this.goBack()
      }).finally(() => {
        this.loading = false
      })
    },
    submitForm () {
      this.$refs.spaceForm.validate(valid => {
        if (valid) {
          const submitData = { ...this.spaceForm }
          
          if (this.isEdit) {
            // 更新广告位
            updateAdSpace(this.spaceId, submitData)
              .then(response => {
                if (response.status === 'success') {
                  this.$message.success('更新成功')
                  this.goBack()
                } else {
                  this.$message.error(response.message || '更新失败')
                }
              })
              .catch(error => {
                // 显示后端返回的错误消息
                const errorMsg = error.response && error.response.data && error.response.data.message
                  ? error.response.data.message
                  : '更新失败'
                this.$message.error(errorMsg)
              })
          } else {
            // 创建广告位
            createAdSpace(submitData)
              .then(response => {
                if (response.status === 'success') {
                  this.$message.success('创建成功')
                  this.goBack()
                } else {
                  this.$message.error(response.message || '创建失败')
                }
              })
              .catch(error => {
                // 显示后端返回的错误消息
                const errorMsg = error.response && error.response.data && error.response.data.message
                  ? error.response.data.message
                  : '创建失败'
                this.$message.error(errorMsg)
              })
          }
        }
      })
    },
    goBack () {
      this.$router.push('/ad/space')
    }
  }
}
</script>

<style lang="scss" scoped>
.tip {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}
</style>
