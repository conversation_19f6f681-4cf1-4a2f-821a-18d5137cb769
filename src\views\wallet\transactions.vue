<template>
  <div class="app-container">
    <div class="detail-header">
      <el-page-header @back="goBack" :content="`${userInfo.nickname || '用户'} 的交易记录`" />
    </div>
    
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>交易记录</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          icon="el-icon-view"
          @click="viewWalletDetail"
        >
          查看钱包详情
        </el-button>
      </div>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="demo-form-inline" size="small">
        <el-form-item label="交易类型">
          <el-select v-model="queryParams.type" placeholder="请选择类型" clearable>
            <el-option label="收入" value="income" />
            <el-option label="支出" value="expense" />
          </el-select>
        </el-form-item>
        <el-form-item label="业务类型">
          <el-select v-model="queryParams.business_type" placeholder="请选择业务类型" clearable>
            <el-option label="WiFi分享" value="wifi_share" />
            <el-option label="商品销售" value="goods_sale" />
            <el-option label="广告点击" value="advertisement" />
            <el-option label="管理员调整" value="admin_adjust" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易日期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="transactionList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="交易ID" width="80" />
        <el-table-column prop="type" label="类型" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.type === 'income'" type="success">收入</el-tag>
            <el-tag v-else type="warning">支出</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="120">
          <template slot-scope="scope">
            <span :class="scope.row.type === 'income' ? 'income-amount' : 'expense-amount'">
              {{ scope.row.type === 'income' ? '+' : '-' }}¥{{ formatNumber(scope.row.amount) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="business_type" label="业务类型" width="120">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.business_type === 'wifi_share'" type="primary">WiFi分享</el-tag>
            <el-tag v-else-if="scope.row.business_type === 'goods_sale'" type="success">商品销售</el-tag>
            <el-tag v-else-if="scope.row.business_type === 'advertisement'" type="warning">广告点击</el-tag>
            <el-tag v-else-if="scope.row.business_type === 'admin_adjust'" type="danger">管理员调整</el-tag>
            <el-tag v-else type="info">其他</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="business_id" label="关联ID" width="100" />
        <el-table-column prop="created_at" label="交易时间" width="160" />
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </el-card>
    
    <!-- 交易统计 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>交易统计</span>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-title">总交易次数</div>
            <div class="stat-value">{{ total || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-title">收入总额</div>
            <div class="stat-value income">¥{{ formatNumber(incomeTotal) }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-title">支出总额</div>
            <div class="stat-value expense">¥{{ formatNumber(expenseTotal) }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-title">净收益</div>
            <div class="stat-value">¥{{ formatNumber(incomeTotal - expenseTotal) }}</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getWalletDetail, getTransactions } from '@/api/wallet'

export default {
  name: 'WalletTransactions',
  data() {
    return {
      userId: null,
      userInfo: {},
      loading: false,
      
      // 查询参数
      queryParams: {
        page: 1,
        limit: 20,
        type: '',
        business_type: '',
        start_date: '',
        end_date: ''
      },
      dateRange: [],
      
      // 交易列表
      transactionList: [],
      total: 0,
      
      // 统计数据
      incomeTotal: 0,
      expenseTotal: 0
    }
  },
  created() {
    this.userId = this.$route.params.userId
    this.fetchUserInfo()
    this.fetchTransactions()
  },
  methods: {
    async fetchUserInfo() {
      if (!this.userId) return
      
      try {
        const { data } = await getWalletDetail(this.userId)
        this.userInfo = data.user
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },
    async fetchTransactions() {
      if (!this.userId) return
      
      this.loading = true
      try {
        const { data } = await getTransactions(this.userId, this.queryParams)
        this.transactionList = data.list || []
        // 修复分页组件total属性类型错误
        this.total = (data.pagination && data.pagination.total) || data.total || 0

        // 计算收入和支出总额
        this.calculateTotals()
      } catch (error) {
        console.error('获取交易记录失败:', error)
        this.transactionList = []
        this.total = 0
        this.$message.error('获取交易记录失败')
      } finally {
        this.loading = false
      }
    },
    calculateTotals() {
      this.incomeTotal = this.transactionList
        .filter(item => item.type === 'income')
        .reduce((sum, item) => sum + parseFloat(item.amount || 0), 0)
      
      this.expenseTotal = this.transactionList
        .filter(item => item.type === 'expense')
        .reduce((sum, item) => sum + parseFloat(item.amount || 0), 0)
    },
    handleDateChange(val) {
      if (val) {
        this.queryParams.start_date = val[0]
        this.queryParams.end_date = val[1]
      } else {
        this.queryParams.start_date = ''
        this.queryParams.end_date = ''
      }
    },
    handleQuery() {
      this.queryParams.page = 1
      this.fetchTransactions()
    },
    resetQuery() {
      this.dateRange = []
      this.queryParams = {
        page: 1,
        limit: 20,
        type: '',
        business_type: '',
        start_date: '',
        end_date: ''
      }
      this.fetchTransactions()
    },
    handleSizeChange(val) {
      this.queryParams.limit = val
      this.fetchTransactions()
    },
    handleCurrentChange(val) {
      this.queryParams.page = val
      this.fetchTransactions()
    },
    goBack() {
      this.$router.push('/wallet/list')
    },
    viewWalletDetail() {
      this.$router.push(`/wallet/detail/${this.userId}`)
    },
    formatNumber(num) {
      return parseFloat(num || 0).toFixed(2)
    }
  }
}
</script>

<style scoped>
.detail-header {
  margin-bottom: 20px;
}

.income-amount {
  color: #67C23A;
  font-weight: bold;
}

.expense-amount {
  color: #F56C6C;
  font-weight: bold;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-value.income {
  color: #67C23A;
}

.stat-value.expense {
  color: #F56C6C;
}
</style>
