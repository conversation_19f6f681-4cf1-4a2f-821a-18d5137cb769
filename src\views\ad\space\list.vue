<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="广告位名称">
          <el-input v-model="queryParams.name" placeholder="广告位名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="状态" clearable>
            <el-option label="启用" :value="1"></el-option>
            <el-option label="禁用" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="primary" @click="handleCreate">添加广告位</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="loading"
      :data="spaceList"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="广告位名称" width="150" />
      <el-table-column prop="code" label="广告位编码" width="150" />
      <el-table-column label="尺寸" width="120">
        <template slot-scope="scope">
          {{ scope.row.width }} x {{ scope.row.height }}
        </template>
      </el-table-column>
      <el-table-column prop="price" label="价格/天" width="120">
        <template slot-scope="scope">
          ¥ {{ scope.row.price.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180" />
      <el-table-column label="操作" width="220" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleUpdate(scope.row)"
          >编辑</el-button>
          <el-button
            size="mini"
            :type="scope.row.status === 1 ? 'warning' : 'success'"
            @click="handleChangeStatus(scope.row)"
          >{{ scope.row.status === 1 ? '禁用' : '启用' }}</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { getAdSpaceList, deleteAdSpace, updateAdSpaceStatus } from '@/api/advertisement'

export default {
  name: 'AdSpaceList',
  data () {
    return {
      loading: false,
      total: 0,
      queryParams: {
        page: 1,
        limit: 10,
        name: undefined,
        status: undefined
      },
      spaceList: []
    }
  },
  created () {
    console.log('广告位列表页面已创建，准备获取数据...')
    this.getList()
  },
  methods: {
    getList () {
      this.loading = true
      getAdSpaceList(this.queryParams)
        .then(response => {
          console.log('广告位列表响应:', response)
          if (response.status === 'success') {
            // 处理数据，确保价格字段是数字类型
            this.spaceList = (response.data.list || []).map(item => ({
              ...item,
              price: parseFloat(item.price) || 0
            }))
            // 修复分页组件total属性类型错误
            this.total = (response.data.pagination && response.data.pagination.total) || response.data.total || 0
          } else {
            this.$message.error(response.message || '获取广告位列表失败')
          }
        })
        .catch(error => {
          console.error('获取广告位列表错误:', error)
          this.$message.error('获取广告位列表失败')
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleQuery () {
      this.queryParams.page = 1
      this.getList()
    },
    resetQuery () {
      this.queryParams = {
        page: 1,
        limit: 10,
        name: undefined,
        status: undefined
      }
      this.getList()
    },
    handleSizeChange (val) {
      this.queryParams.limit = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.queryParams.page = val
      this.getList()
    },
    handleCreate () {
      this.$router.push('/ad/space/create')
    },
    handleUpdate (row) {
      this.$router.push(`/ad/space/edit/${row.id}`)
    },
    handleChangeStatus (row) {
      const newStatus = row.status === 1 ? 0 : 1
      const statusText = newStatus === 1 ? '启用' : '禁用'

      this.$confirm(`确认要${statusText}该广告位吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateAdSpaceStatus(row.id, newStatus)
          .then(() => {
        this.$message.success(`${statusText}成功`)
            this.getList()
          })
          .catch(() => {
            this.$message.error(`${statusText}失败`)
          })
      }).catch(() => {})
    },
    handleDelete (row) {
      this.$confirm('确认要删除该广告位吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAdSpace(row.id)
          .then(() => {
          this.$message.success('删除成功')
            this.getList()
          })
          .catch(() => {
            this.$message.error('删除失败')
          })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
}
.pagination-container {
  margin-top: 15px;
  text-align: right;
}
</style>
