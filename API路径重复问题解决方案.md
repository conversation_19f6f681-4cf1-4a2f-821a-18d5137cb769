# API路径重复问题解决方案

## 问题描述

在宝塔面板中访问WiFi共享管理后台时，出现API路径重复的错误：
```
POST /api/api/v1/admin/auth/admin-login
```

正确的路径应该是：
```
POST /api/v1/admin/auth/admin-login
```

## 问题原因

1. **生产环境配置问题**：`.env.production`文件中的`VUE_APP_API_BASE_URL`包含了完整的服务器地址
2. **axios配置问题**：在生产环境中，baseURL被设置为完整URL，但请求URL本身也包含`/api`前缀
3. **代理配置差异**：开发环境使用代理，生产环境直接请求，配置不一致

## 解决方案

### 1. 修复环境配置

已将`.env.production`中的API地址修改为：
```
VUE_APP_API_BASE_URL=http://101.37.255.139:4000
```

### 2. 修复axios配置

在`src/utils/request.js`中添加了路径修复逻辑：
- 检测生产环境中的URL重复问题
- 自动修正API路径
- 增强调试日志输出

### 3. 部署步骤

#### 步骤1：重新构建项目
```bash
npm run build
```

#### 步骤2：重启服务
```bash
pm2 restart wifi-share-admin
```

#### 步骤3：验证修复
访问：http://101.37.255.139:8081

### 4. 验证方法

1. **检查浏览器网络面板**：
   - 打开开发者工具 → Network
   - 尝试登录
   - 查看请求URL是否正确

2. **检查服务器日志**：
   ```bash
   pm2 logs wifi-share-admin
   ```

3. **检查后端API**：
   ```bash
   curl -X POST http://101.37.255.139:4000/api/v1/admin/auth/admin-login \
     -H "Content-Type: application/json" \
     -d '{"username":"mrx0927","password":"hh20250701"}'
   ```

## 常见问题排查

### 问题1：仍然出现路径重复
**解决方案**：
1. 清除浏览器缓存
2. 重新构建项目：`npm run build`
3. 重启服务：`pm2 restart wifi-share-admin`

### 问题2：无法连接到后端
**检查项目**：
1. 后端服务是否运行：`pm2 status`
2. 端口是否正确：4000
3. 防火墙设置是否允许4000端口

### 问题3：登录失败
**检查项目**：
1. 用户名密码是否正确：mrx0927 / hh20250701
2. 数据库连接是否正常
3. 后端日志是否有错误信息

## 技术细节

### 修复前的请求流程
```
前端请求: /api/v1/admin/auth/admin-login
axios baseURL: http://101.37.255.139:4000
最终URL: http://101.37.255.139:4000/api/v1/admin/auth/admin-login ✓

但在某些情况下会变成:
最终URL: http://101.37.255.139:4000/api/api/v1/admin/auth/admin-login ✗
```

### 修复后的请求流程
```
前端请求: /api/v1/admin/auth/admin-login
axios baseURL: http://101.37.255.139:4000
请求拦截器处理: 检测并修正路径重复
最终URL: http://101.37.255.139:4000/api/v1/admin/auth/admin-login ✓
```

## 监控和维护

### 日志监控
```bash
# 查看前端服务日志
pm2 logs wifi-share-admin

# 查看详细日志文件
tail -f logs/wifi-share-admin.log
```

### 性能监控
```bash
# 查看服务状态
pm2 status

# 查看服务详情
pm2 show wifi-share-admin
```

## 联系信息

如果问题仍然存在，请提供以下信息：
1. 浏览器控制台错误信息
2. 网络请求详情
3. 服务器日志内容
