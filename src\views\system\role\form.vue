<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? '编辑角色' : '新增角色' }}</span>
      </div>
      <el-form
        ref="roleForm"
        :model="roleForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            placeholder="请输入角色描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="roleForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item label="权限分配" prop="permissions">
          <el-tree
            ref="permissionTree"
            :data="permissionList"
            :props="{
              label: 'name',
              children: 'children'
            }"
            show-checkbox
            node-key="id"
            default-expand-all
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="goBack">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { createRole, updateRole } from '@/api/system'

export default {
  name: 'RoleForm',
  data () {
    return {
      isEdit: false,
      roleId: undefined,
      roleForm: {
        name: '',
        description: '',
        status: 1,
        permissions: []
      },
      rules: {
        name: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        description: [
          { max: 255, message: '长度不能超过 255 个字符', trigger: 'blur' }
        ]
      },
      permissionList: [
        {
          id: 1,
          name: 'WiFi码管理',
          children: [
            { id: 11, name: 'WiFi码查看' },
            { id: 12, name: 'WiFi码创建' },
            { id: 13, name: 'WiFi码编辑' },
            { id: 14, name: 'WiFi码删除' }
          ]
        },
        {
          id: 2,
          name: '用户管理',
          children: [
            { id: 21, name: '用户查看' },
            { id: 22, name: '用户编辑' },
            { id: 23, name: '用户标签管理' }
          ]
        },
        {
          id: 3,
          name: '商城管理',
          children: [
            {
              id: 31,
              name: '商品管理',
              children: [
                { id: 311, name: '商品查看' },
                { id: 312, name: '商品创建' },
                { id: 313, name: '商品编辑' },
                { id: 314, name: '商品删除' }
              ]
            },
            {
              id: 32,
              name: '订单管理',
              children: [
                { id: 321, name: '订单查看' },
                { id: 322, name: '订单处理' }
              ]
            }
          ]
        },
        {
          id: 4,
          name: '分润管理',
          children: [
            { id: 41, name: '分润规则设置' },
            { id: 42, name: '分润账单查看' },
            { id: 43, name: '提现管理' }
          ]
        },
        {
          id: 5,
          name: '广告管理',
          children: [
            { id: 51, name: '广告位管理' },
            { id: 52, name: '广告内容管理' }
          ]
        },
        {
          id: 6,
          name: '系统设置',
          children: [
            { id: 61, name: '基础设置' },
            { id: 62, name: '角色管理' },
            { id: 63, name: '账号管理' },
            { id: 64, name: '日志管理' }
          ]
        }
      ]
    }
  },
  created () {
    const id = this.$route.params && this.$route.params.id
    if (id) {
      this.isEdit = true
      this.roleId = id
      this.getRoleDetail(id)
    }
  },
  methods: {
    getRoleDetail (id) {
      // 这里应该通过API获取角色详情
      // 但由于没有提供获取单个角色的API，这里模拟一个
      this.roleForm = {
        id: id,
        name: '角色' + id,
        description: '角色描述',
        status: 1,
        permissions: [11, 21, 31]
      }

      // 根据已有权限设置树选择
      this.$nextTick(() => {
        const permissionIds = this.roleForm.permissions || []
        this.$refs.permissionTree && this.$refs.permissionTree.setCheckedKeys(permissionIds)
      })
    },
    submitForm () {
      this.$refs.roleForm.validate(valid => {
        if (valid) {
          // 获取选中的权限ID
          const checkedKeys = this.$refs.permissionTree.getCheckedKeys()
          const halfCheckedKeys = this.$refs.permissionTree.getHalfCheckedKeys()
          this.roleForm.permissions = [...checkedKeys, ...halfCheckedKeys]

          if (this.isEdit) {
            // 更新
            updateRole(this.roleId, this.roleForm).then(() => {
              this.$message.success('更新成功')
              
              // 检查是否是从账号创建页面进入的
              const referrer = this.$route.query.referrer
              if (referrer && referrer === 'account-create') {
                this.$router.push('/system/account/create')
              } else {
                this.goBack()
              }
            })
          } else {
            // 创建
            createRole(this.roleForm).then(() => {
              this.$message.success('创建成功')
              
              // 检查是否是从账号创建页面进入的
              const referrer = this.$route.query.referrer
              if (referrer && referrer === 'account-create') {
                this.$router.push('/system/account/create')
              } else {
                this.goBack()
              }
            })
          }
        }
      })
    },
    goBack () {
      this.$router.push('/system/role')
    }
  }
}
</script>

<style lang="scss" scoped>
.box-card {
  margin-bottom: 20px;
}
</style>
