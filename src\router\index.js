import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/components/layout'

Vue.use(VueRouter)

// 公共路由
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: { title: '仪表盘', icon: 'dashboard', affix: true }
      },
      {
        path: 'platform-stats',
        component: () => import('@/views/dashboard/platform-stats.vue'),
        name: 'PlatformStats',
        meta: { title: '平台数据统计', icon: 'chart', affix: false }
      }
    ]
  },
  // WiFi码管理
  {
    path: '/wifi',
    component: Layout,
    redirect: '/wifi/list',
    name: 'WiFiManage',
    meta: { title: 'WiFi码管理', icon: 'wifi' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/wifi/list'),
        name: 'WiFiList',
        meta: { title: 'WiFi码列表' }
      },
      {
        path: 'create',
        component: () => import('@/views/wifi/form'),
        name: 'WiFiCreate',
        meta: { title: '创建WiFi码' }
      },
      {
        path: 'edit/:id',
        component: () => import('@/views/wifi/form'),
        name: 'WiFiEdit',
        meta: { title: '编辑WiFi码', activeMenu: '/wifi/list' },
        hidden: true
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/wifi/detail'),
        name: 'WiFiDetail',
        meta: { title: 'WiFi码详情', activeMenu: '/wifi/list' },
        hidden: true
      },
      {
        path: 'stats',
        component: () => import('@/views/wifi/stats'),
        name: 'WiFiStats',
        meta: { title: 'WiFi码统计' }
      }
    ]
  },
  // 用户管理
  {
    path: '/user',
    component: Layout,
    redirect: '/user/list',
    name: 'UserManage',
    meta: { title: '用户管理', icon: 'user' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/user/list'),
        name: 'UserList',
        meta: { title: '用户列表' }
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/user/detail'),
        name: 'UserDetail',
        meta: { title: '用户详情', activeMenu: '/user/list' },
        hidden: true
      },
      {
        path: 'tag',
        component: () => import('@/views/user/tag'),
        name: 'UserTag',
        meta: { title: '用户标签' }
  },
  {
        path: 'alliance',
        component: () => import('@/views/alliance/list'),
        name: 'AllianceList',
        meta: { title: '联盟申请' }
      },
      {
        path: 'alliance/detail/:id',
        component: () => import('@/views/alliance/detail'),
        name: 'AllianceDetail',
        meta: { title: '申请详情', activeMenu: '/user/alliance' },
        hidden: true
      }
    ]
  },
  // 团队管理
  {
    path: '/team',
    component: Layout,
    redirect: '/team/list',
    name: 'TeamManage',
    meta: { title: '团队管理', icon: 'team' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/team/list'),
        name: 'TeamList',
        meta: { title: '团队列表' }
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/team/detail'),
        name: 'TeamDetail',
        meta: { title: '团队详情', activeMenu: '/team/list' },
        hidden: true
      },
      {
        path: 'create',
        component: () => import('@/views/team/form'),
        name: 'TeamCreate',
        meta: { title: '创建团队' }
      },
      {
        path: 'edit/:id',
        component: () => import('@/views/team/form'),
        name: 'TeamEdit',
        meta: { title: '编辑团队', activeMenu: '/team/list' },
        hidden: true
      },
      {
        path: 'members/:id',
        component: () => import('@/views/team/members'),
        name: 'TeamMembers',
        meta: { title: '成员管理', activeMenu: '/team/list' },
        hidden: true
      }
    ]
  },
  // 商城管理
  {
    path: '/mall',
    component: Layout,
    redirect: '/mall/goods',
    name: 'MallManage',
    meta: { title: '商城管理', icon: 'shopping' },
    children: [
      {
        path: 'goods',
        component: () => import('@/views/mall/goods/list'),
        name: 'GoodsList',
        meta: { title: '商品列表' }
      },
      {
        path: 'goods/create',
        component: () => import('@/views/mall/goods/form'),
        name: 'GoodsCreate',
        meta: { title: '创建商品', activeMenu: '/mall/goods' },
        hidden: true
      },
      {
        path: 'goods/edit/:id',
        component: () => import('@/views/mall/goods/form'),
        name: 'GoodsEdit',
        meta: { title: '编辑商品', activeMenu: '/mall/goods' },
        hidden: true
      },
      {
        path: 'goods/detail/:id',
        component: () => import('@/views/mall/goods/detail'),
        name: 'GoodsDetail',
        meta: { title: '商品详情', activeMenu: '/mall/goods' },
        hidden: true
      },
      {
        path: 'order',
        component: () => import('@/views/mall/order/list'),
        name: 'OrderList',
        meta: { title: '订单列表' }
      },
      {
        path: 'order/detail/:id',
        component: () => import('@/views/mall/order/detail'),
        name: 'OrderDetail',
        meta: { title: '订单详情', activeMenu: '/mall/order' },
        hidden: true
      }
    ]
  },
  // 分润管理
  {
    path: '/profit',
    component: Layout,
    redirect: '/profit/rules',
    name: 'ProfitManage',
    meta: { title: '分润管理', icon: 'money' },
    children: [
      {
        path: 'rules',
        component: () => import('@/views/profit/rules'),
        name: 'ProfitRules',
        meta: { title: '分润规则' }
      },
      {
        path: 'bill',
        component: () => import('@/views/profit/bill/list'),
        name: 'BillList',
        meta: { title: '分润账单' }
      },
      {
        path: 'bill/detail/:id',
        component: () => import('@/views/profit/bill/detail'),
        name: 'BillDetail',
        meta: { title: '账单详情', activeMenu: '/profit/bill' },
        hidden: true
      },
      {
        path: 'withdraw',
        component: () => import('@/views/profit/withdraw/list'),
        name: 'WithdrawList',
        meta: { title: '提现管理' }
      },
      {
        path: 'withdraw/detail/:id',
        component: () => import('@/views/profit/withdraw/detail'),
        name: 'WithdrawDetail',
        meta: { title: '提现详情', activeMenu: '/profit/withdraw' },
        hidden: true
      }
    ]
  },
  // 广告管理
  {
    path: '/ad',
    component: Layout,
    redirect: '/ad/space',
    name: 'AdManage',
    meta: { title: '广告管理', icon: 'ad' },
    children: [
      {
        path: 'space',
        component: () => import('@/views/ad/space/list'),
        name: 'AdSpaceList',
        meta: { title: '广告位管理' }
      },
      {
        path: 'space/create',
        component: () => import('@/views/ad/space/form'),
        name: 'AdSpaceCreate',
        meta: { title: '创建广告位', activeMenu: '/ad/space' },
        hidden: true
      },
      {
        path: 'space/edit/:id',
        component: () => import('@/views/ad/space/form'),
        name: 'AdSpaceEdit',
        meta: { title: '编辑广告位', activeMenu: '/ad/space' },
        hidden: true
      },
      {
        path: 'content',
        component: () => import('@/views/ad/content/list'),
        name: 'AdContentList',
        meta: { title: '广告内容管理' }
      },
      {
        path: 'content/create',
        component: () => import('@/views/ad/content/form'),
        name: 'AdContentCreate',
        meta: { title: '创建广告内容', activeMenu: '/ad/content' },
        hidden: true
      },
      {
        path: 'content/edit/:id',
        component: () => import('@/views/ad/content/form'),
        name: 'AdContentEdit',
        meta: { title: '编辑广告内容', activeMenu: '/ad/content' },
        hidden: true
      },
      {
        path: 'stats',
        component: () => import('@/views/ad/stats/index.vue'),
        name: 'AdStats',
        meta: { title: '广告统计' }
      }
    ]
  },
  // 地区管理
  {
    path: '/region',
    component: Layout,
    redirect: '/region/list',
    name: 'RegionManage',
    meta: { title: '地区管理', icon: 'location' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/region/list.vue'),
        name: 'RegionList',
        meta: { title: '地区列表' }
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/region/detail.vue'),
        name: 'RegionDetail',
        meta: { title: '地区详情', activeMenu: '/region/list' },
        hidden: true
      }
    ]
  },
  // 钱包管理
  {
    path: '/wallet',
    component: Layout,
    redirect: '/wallet/list',
    name: 'WalletManage',
    meta: { title: '钱包管理', icon: 'wallet' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/wallet/list.vue'),
        name: 'WalletList',
        meta: { title: '钱包列表' }
      },
      {
        path: 'detail/:userId',
        component: () => import('@/views/wallet/detail.vue'),
        name: 'WalletDetail',
        meta: { title: '钱包详情', activeMenu: '/wallet/list' },
        hidden: true
      },
      {
        path: 'transactions/:userId',
        component: () => import('@/views/wallet/transactions.vue'),
        name: 'WalletTransactions',
        meta: { title: '交易记录', activeMenu: '/wallet/list' },
        hidden: true
      }
    ]
  },
  // 平台管理
  {
    path: '/platform',
    component: Layout,
    redirect: '/platform/revenue',
    name: 'PlatformManage',
    meta: { title: '平台管理', icon: 'platform' },
    children: [
      {
        path: 'revenue',
        component: () => import('@/views/platform/revenue.vue'),
        name: 'PlatformRevenue',
        meta: { title: '收益管理' }
      }
    ]
  },
  // 系统设置
  {
    path: '/system',
    component: Layout,
    redirect: '/system/config',
    name: 'SystemManage',
    meta: { title: '系统设置', icon: 'setting' },
    children: [
      {
        path: 'config',
        component: () => import('@/views/system/config'),
        name: 'SystemConfig',
        meta: { title: '基础设置' }
      },
      {
        path: 'role',
        component: () => import('@/views/system/role/list'),
        name: 'RoleList',
        meta: { title: '角色管理' }
      },
      {
        path: 'role/create',
        component: () => import('@/views/system/role/form'),
        name: 'RoleCreate',
        meta: { title: '创建角色', activeMenu: '/system/role' },
        hidden: true
      },
      {
        path: 'role/edit/:id',
        component: () => import('@/views/system/role/form'),
        name: 'RoleEdit',
        meta: { title: '编辑角色', activeMenu: '/system/role' },
        hidden: true
      },
      {
        path: 'account',
        component: () => import('@/views/system/account/list'),
        name: 'AccountList',
        meta: { title: '账号管理' }
      },
      {
        path: 'account/create',
        component: () => import('@/views/system/account/form'),
        name: 'AccountCreate',
        meta: { title: '创建账号', activeMenu: '/system/account' },
        hidden: true
      },
      {
        path: 'account/edit/:id',
        component: () => import('@/views/system/account/form'),
        name: 'AccountEdit',
        meta: { title: '编辑账号', activeMenu: '/system/account' },
        hidden: true
      },
      {
        path: 'log',
        component: () => import('@/views/system/log'),
        name: 'LogManage',
        meta: { title: '日志管理' }
      }
    ]
  },
  // 404页面必须放在最后
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new VueRouter({
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// 重置路由
export function resetRouter () {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher
}

export default router
