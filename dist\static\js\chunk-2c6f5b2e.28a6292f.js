(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2c6f5b2e"],{2698:function(t,a,e){},"4df3":function(t,a,e){"use strict";e("2698")},"87b5":function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t._self._c;return a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"stats-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("广告统计概览")]),a("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.refreshData}},[a("i",{staticClass:"el-icon-refresh"}),t._v(" 刷新 ")])],1),a("el-row",{staticClass:"overview-cards",attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"overview-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-icon ad-icon"},[a("i",{staticClass:"el-icon-picture-outline"})]),a("div",{staticClass:"card-content"},[a("div",{staticClass:"card-title"},[t._v("广告位总数")]),a("div",{staticClass:"card-value"},[t._v(t._s(t.overview.total_spaces||0))]),a("div",{staticClass:"card-footer"},[a("span",[t._v("启用: ")]),a("span",{staticClass:"highlight"},[t._v(t._s(t.overview.active_spaces||0))])])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"overview-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-icon content-icon"},[a("i",{staticClass:"el-icon-document"})]),a("div",{staticClass:"card-content"},[a("div",{staticClass:"card-title"},[t._v("广告内容总数")]),a("div",{staticClass:"card-value"},[t._v(t._s(t.overview.total_contents||0))]),a("div",{staticClass:"card-footer"},[a("span",[t._v("投放中: ")]),a("span",{staticClass:"highlight"},[t._v(t._s(t.overview.active_contents||0))])])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"overview-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-icon click-icon"},[a("i",{staticClass:"el-icon-mouse"})]),a("div",{staticClass:"card-content"},[a("div",{staticClass:"card-title"},[t._v("总点击次数")]),a("div",{staticClass:"card-value"},[t._v(t._s(t.formatNumber(t.overview.total_clicks||0)))]),a("div",{staticClass:"card-footer"},[a("span",[t._v("今日点击: ")]),a("span",{staticClass:"highlight"},[t._v(t._s(t.formatNumber(t.overview.today_clicks||0)))])])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"overview-card",attrs:{shadow:"hover"}},[a("div",{staticClass:"card-icon revenue-icon"},[a("i",{staticClass:"el-icon-coin"})]),a("div",{staticClass:"card-content"},[a("div",{staticClass:"card-title"},[t._v("广告收益")]),a("div",{staticClass:"card-value"},[t._v("¥"+t._s(t.formatMoney(t.overview.total_revenue||0)))]),a("div",{staticClass:"card-footer"},[a("span",[t._v("今日收益: ")]),a("span",{staticClass:"highlight"},[t._v("¥"+t._s(t.formatMoney(t.overview.today_revenue||0)))])])])])],1)],1),a("el-row",{staticStyle:{margin:"20px 0"}},[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{inline:!0,size:"small"}},[a("el-form-item",{attrs:{label:"统计周期"}},[a("el-radio-group",{on:{change:t.handlePeriodChange},model:{value:t.timePeriod,callback:function(a){t.timePeriod=a},expression:"timePeriod"}},[a("el-radio-button",{attrs:{label:"7d"}},[t._v("7天")]),a("el-radio-button",{attrs:{label:"30d"}},[t._v("30天")]),a("el-radio-button",{attrs:{label:"90d"}},[t._v("90天")])],1)],1),a("el-form-item",{attrs:{label:"自定义时间"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},on:{change:t.handleCustomDateChange},model:{value:t.customDateRange,callback:function(a){t.customDateRange=a},expression:"customDateRange"}})],1)],1)],1)],1),a("el-card",{staticClass:"chart-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("点击趋势")])]),a("div",{ref:"clickTrendChart",staticClass:"chart-container"})]),a("el-card",{staticClass:"chart-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("收益趋势")])]),a("div",{ref:"revenueTrendChart",staticClass:"chart-container"})]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-card",{staticClass:"chart-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("广告位点击排行")])]),a("div",{ref:"spaceRankChart",staticClass:"chart-container"})])],1),a("el-col",{attrs:{span:12}},[a("el-card",{staticClass:"chart-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("广告内容效果排行")])]),a("div",{ref:"contentRankChart",staticClass:"chart-container"})])],1)],1),a("el-card",{staticClass:"chart-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("广告位详细数据")])]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],staticStyle:{width:"100%"},attrs:{data:t.spaceStats,border:""}},[a("el-table-column",{attrs:{prop:"space_name",label:"广告位名称","min-width":"150"}}),a("el-table-column",{attrs:{prop:"space_code",label:"广告位编码",width:"120"}}),a("el-table-column",{attrs:{prop:"total_clicks",label:"总点击数",width:"100"}}),a("el-table-column",{attrs:{prop:"today_clicks",label:"今日点击",width:"100"}}),a("el-table-column",{attrs:{prop:"total_revenue",label:"总收益",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(" ¥"+t._s(t.formatMoney(a.row.total_revenue))+" ")]}}])}),a("el-table-column",{attrs:{prop:"today_revenue",label:"今日收益",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(" ¥"+t._s(t.formatMoney(a.row.today_revenue))+" ")]}}])}),a("el-table-column",{attrs:{prop:"click_rate",label:"点击率",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(" "+t._s((100*a.row.click_rate).toFixed(2))+"% ")]}}])}),a("el-table-column",{attrs:{prop:"avg_revenue",label:"平均收益",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(" ¥"+t._s(t.formatMoney(a.row.avg_revenue))+" ")]}}])}),a("el-table-column",{attrs:{prop:"status",label:"状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[1===e.row.status?a("el-tag",{attrs:{type:"success"}},[t._v("启用")]):a("el-tag",{attrs:{type:"danger"}},[t._v("禁用")])]}}])}),a("el-table-column",{attrs:{label:"操作",width:"120",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[1===e.row.status?a("el-button",{attrs:{size:"mini",type:"warning",loading:e.row.loading},on:{click:function(a){return t.toggleSpaceStatus(e.row,0)}}},[t._v(" 禁用 ")]):a("el-button",{attrs:{size:"mini",type:"success",loading:e.row.loading},on:{click:function(a){return t.toggleSpaceStatus(e.row,1)}}},[t._v(" 启用 ")])]}}])})],1)],1)],1)],1)},r=[],i=(e("e9f5"),e("ab43"),e("313e")),n=e("b775");function c(){return Object(n["a"])({url:"/api/v1/admin/advertisement/stats",method:"get"})}function o(t){return Object(n["a"])({url:"/api/v1/admin/advertisement/trend",method:"get",params:t})}function l(t){return Object(n["a"])({url:"/api/v1/admin/advertisement/space-rank",method:"get",params:t})}function d(t){return Object(n["a"])({url:"/api/v1/admin/advertisement/content-rank",method:"get",params:t})}function h(){return Object(n["a"])({url:"/api/v1/admin/advertisement/space-stats",method:"get"})}var u={name:"AdStats",data(){return{loading:!1,tableLoading:!1,timePeriod:"7d",customDateRange:[],overview:{},spaceStats:[],clickTrendChart:null,revenueTrendChart:null,spaceRankChart:null,contentRankChart:null,trendData:[],spaceRankData:[],contentRankData:[]}},mounted(){this.initData()},beforeDestroy(){this.clickTrendChart&&this.clickTrendChart.dispose(),this.revenueTrendChart&&this.revenueTrendChart.dispose(),this.spaceRankChart&&this.spaceRankChart.dispose(),this.contentRankChart&&this.contentRankChart.dispose()},methods:{initData(){this.fetchOverview(),this.fetchTrendData(),this.fetchRankData(),this.fetchSpaceStats()},refreshData(){this.initData(),this.$message.success("数据已刷新")},handlePeriodChange(){this.customDateRange=[],this.fetchTrendData(),this.fetchRankData()},handleCustomDateChange(t){t&&2===t.length&&(this.timePeriod="custom",this.fetchTrendData(),this.fetchRankData())},async fetchOverview(){try{this.loading=!0;const{data:t}=await c();this.overview=t||{}}catch(t){console.error("获取广告统计概览失败:",t),this.$message.error("获取广告统计概览失败"),this.overview={}}finally{this.loading=!1}},async fetchTrendData(){try{const t={period:this.timePeriod};this.customDateRange&&2===this.customDateRange.length&&(t.start_date=this.customDateRange[0],t.end_date=this.customDateRange[1]);const{data:a}=await o(t);this.trendData=Array.isArray(a)?a:[],this.$nextTick(()=>{this.renderTrendCharts()})}catch(t){console.error("获取广告趋势数据失败:",t),this.$message.error("获取广告趋势数据失败"),this.trendData=[]}},async fetchRankData(){try{const t={period:this.timePeriod};this.customDateRange&&2===this.customDateRange.length&&(t.start_date=this.customDateRange[0],t.end_date=this.customDateRange[1]);const[a,e]=await Promise.all([l(t),d(t)]);this.spaceRankData=Array.isArray(a.data)?a.data:[],this.contentRankData=Array.isArray(e.data)?e.data:[],this.$nextTick(()=>{this.renderRankCharts()})}catch(t){console.error("获取广告排行数据失败:",t),this.$message.error("获取广告排行数据失败"),this.spaceRankData=[],this.contentRankData=[]}},async fetchSpaceStats(){try{this.tableLoading=!0;const{data:t}=await h();this.spaceStats=Array.isArray(t)?t:[]}catch(t){console.error("获取广告位统计失败:",t),this.$message.error("获取广告位统计失败"),this.spaceStats=[]}finally{this.tableLoading=!1}},renderTrendCharts(){this.renderClickTrendChart(),this.renderRevenueTrendChart()},renderClickTrendChart(){if(!this.$refs.clickTrendChart)return;if(this.clickTrendChart||(this.clickTrendChart=i["a"](this.$refs.clickTrendChart)),!Array.isArray(this.trendData)||0===this.trendData.length)return;const t=this.trendData.map(t=>t.date||""),a=this.trendData.map(t=>t.clicks||0),e={tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:t},yAxis:{type:"value"},series:[{name:"点击次数",type:"line",data:a,smooth:!0,itemStyle:{color:"#409EFF"}}]};this.clickTrendChart.setOption(e)},renderRevenueTrendChart(){if(!this.$refs.revenueTrendChart)return;if(this.revenueTrendChart||(this.revenueTrendChart=i["a"](this.$refs.revenueTrendChart)),!Array.isArray(this.trendData)||0===this.trendData.length)return;const t=this.trendData.map(t=>t.date||""),a=this.trendData.map(t=>t.revenue||0),e={tooltip:{trigger:"axis",formatter:function(t){return`${t[0].name}<br/>${t[0].seriesName}: ¥${t[0].value.toFixed(2)}`}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:t},yAxis:{type:"value"},series:[{name:"广告收益",type:"bar",data:a,itemStyle:{color:"#67C23A"}}]};this.revenueTrendChart.setOption(e)},renderRankCharts(){this.renderSpaceRankChart(),this.renderContentRankChart()},renderSpaceRankChart(){if(!this.$refs.spaceRankChart)return;if(this.spaceRankChart||(this.spaceRankChart=i["a"](this.$refs.spaceRankChart)),!Array.isArray(this.spaceRankData)||0===this.spaceRankData.length)return;const t=this.spaceRankData.map(t=>t.space_name||""),a=this.spaceRankData.map(t=>t.clicks||0),e={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value"},yAxis:{type:"category",data:t},series:[{name:"点击次数",type:"bar",data:a,itemStyle:{color:"#E6A23C"}}]};this.spaceRankChart.setOption(e)},renderContentRankChart(){if(!this.$refs.contentRankChart)return;if(this.contentRankChart||(this.contentRankChart=i["a"](this.$refs.contentRankChart)),!Array.isArray(this.contentRankData)||0===this.contentRankData.length)return;const t=this.contentRankData.map(t=>({value:t.clicks||0,name:t.title||t.content_title||"未知内容"})),a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:10,data:this.contentRankData.map(t=>t.title||t.content_title||"未知内容")},series:[{name:"广告内容点击",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:t}]};this.contentRankChart.setOption(a)},formatNumber(t){return parseInt(t).toLocaleString()},formatMoney(t){return parseFloat(t||0).toFixed(2)},async toggleSpaceStatus(t,a){try{this.$set(t,"loading",!0),console.log("准备更新广告位状态:",{space_id:t.space_id,current_status:t.status,target_status:a});const e=await Object(n["a"])({url:"/api/v1/admin/advertisement/spaces/"+t.space_id,method:"put",data:{status:a}});console.log("API响应数据:",e),e&&"success"===e.status?(t.status=a,this.$message.success(1===a?"广告位已启用":"广告位已禁用")):(console.error("API响应格式异常:",e),this.$message.error((null===e||void 0===e?void 0:e.message)||"操作失败"))}catch(e){console.error("切换广告位状态失败:",e),this.$message.error("操作失败，请稍后重试")}finally{this.$set(t,"loading",!1)}}}},v=u,p=(e("4df3"),e("2877")),m=Object(p["a"])(v,s,r,!1,null,"cfe6796e",null);a["default"]=m.exports},ab43:function(t,a,e){"use strict";var s=e("23e7"),r=e("c65b"),i=e("59ed"),n=e("825a"),c=e("46c4"),o=e("c5cc"),l=e("9bdd"),d=e("2a62"),h=e("2baa"),u=e("f99f"),v=e("c430"),p=!v&&!h("map",(function(){})),m=!v&&!p&&u("map",TypeError),f=v||p||m,C=o((function(){var t=this.iterator,a=n(r(this.next,t)),e=this.done=!!a.done;if(!e)return l(t,this.mapper,[a.value,this.counter++],!0)}));s({target:"Iterator",proto:!0,real:!0,forced:f},{map:function(t){n(this);try{i(t)}catch(a){d(this,"throw",a)}return m?r(m,this,t):new C(c(this),{mapper:t})}})}}]);