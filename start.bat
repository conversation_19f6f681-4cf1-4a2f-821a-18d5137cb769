@echo off
chcp 65001 >nul
echo WiFi共享管理后台启动脚本

:: 检查Node.js是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Node.js 未安装
    pause
    exit /b 1
)

:: 检查PM2是否安装
pm2 --version >nul 2>&1
if errorlevel 1 (
    echo 安装PM2...
    npm install -g pm2
)

:: 检查依赖是否安装
if not exist "node_modules" (
    echo 安装依赖...
    npm install
)

:: 检查dist目录是否存在
if not exist "dist" (
    echo 构建前端项目...
    npm run build
)

:: 创建日志目录
if not exist "logs" mkdir logs

:: 启动应用
echo 启动WiFi共享管理后台...
pm2 start ecosystem.config.js --env production

:: 显示状态
pm2 status

echo.
echo WiFi共享管理后台启动完成!
echo 访问地址: http://localhost:8081
echo 日志文件: ./logs/wifi-share-admin.log
echo.
echo 常用命令:
echo   查看状态: pm2 status
echo   查看日志: pm2 logs wifi-share-admin
echo   重启应用: pm2 restart wifi-share-admin
echo   停止应用: pm2 stop wifi-share-admin
echo.
pause
