const db = require('../../config/database');
const logger = require('../utils/logger');
const { success, error } = require('../utils/response');

/**
 * 联盟管理控制器
 */

/**
 * 获取联盟申请列表
 * GET /api/v1/admin/alliance/list
 */
const getAllianceList = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, name, phone } = req.query;
    
    let whereConditions = [];
    const params = [];
    
    // 状态筛选
    if (status !== undefined && status !== '') {
      whereConditions.push('ta.status = ?');
      params.push(parseInt(status));
    }
    
    // 姓名筛选
    if (name) {
      whereConditions.push('ta.name LIKE ?');
      params.push(`%${name}%`);
    }
    
    // 手机号筛选
    if (phone) {
      whereConditions.push('ta.phone LIKE ?');
      params.push(`%${phone}%`);
    }
    
    const whereClause = whereConditions.length > 0 ? 
      `WHERE ${whereConditions.join(' AND ')}` : '';
    
    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM team_apply ta 
      LEFT JOIN user u ON ta.user_id = u.id 
      ${whereClause}
    `;
    const countResult = await db.query(countSql, params);
    const total = countResult[0].total;
    
    // 获取列表（包含关联的团队信息）
    const listSql = `
      SELECT
        ta.id,
        ta.user_id,
        ta.name,
        ta.contact,
        ta.phone,
        ta.email,
        ta.area,
        ta.description,
        ta.status,
        ta.created_at,
        ta.updated_at,
        ta.remark,
        u.nickname as user_nickname,
        u.avatar as user_avatar,
        u.is_leader,
        u.team_id,
        t.id as team_id,
        t.name as team_name,
        t.member_count as team_member_count,
        t.created_at as team_created_at,
        CASE
          WHEN ta.status = 0 THEN '待审核'
          WHEN ta.status = 1 THEN '已通过'
          WHEN ta.status = 2 THEN '已拒绝'
          ELSE '未知'
        END as status_text,
        CASE
          WHEN ta.status = 1 AND t.id IS NOT NULL THEN '已创建团队'
          WHEN ta.status = 1 AND t.id IS NULL THEN '审核通过但团队创建失败'
          WHEN ta.status = 0 THEN '等待审核'
          WHEN ta.status = 2 THEN '申请被拒绝'
          ELSE '状态异常'
        END as process_status
      FROM team_apply ta
      LEFT JOIN user u ON ta.user_id = u.id
      LEFT JOIN team t ON t.leader_id = ta.user_id AND ta.status = 1
      ${whereClause}
      ORDER BY ta.created_at DESC
      LIMIT ${offset}, ${parseInt(limit)}
    `;

    const list = await db.query(listSql, params);
    
    return success(res, {
      list,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    }, '获取联盟申请列表成功');
  } catch (err) {
    logger.error(`获取联盟申请列表失败: ${err.message}`);
    return error(res, '获取联盟申请列表失败', 500);
  }
};

/**
 * 获取联盟申请详情
 * GET /api/v1/admin/alliance/detail/:id
 */
const getAllianceDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    const sql = `
      SELECT
        ta.*,
        u.nickname as user_nickname,
        u.avatar as user_avatar,
        u.phone as user_phone,
        u.created_at as user_register_time,
        u.is_leader,
        u.team_id,
        t.id as team_id,
        t.name as team_name,
        t.member_count as team_member_count,
        t.wifi_count as team_wifi_count,
        t.total_profit as team_total_profit,
        t.created_at as team_created_at,
        t.invite_code as team_invite_code,
        CASE
          WHEN ta.status = 0 THEN '待审核'
          WHEN ta.status = 1 THEN '已通过'
          WHEN ta.status = 2 THEN '已拒绝'
          ELSE '未知'
        END as status_text,
        CASE
          WHEN ta.status = 1 AND t.id IS NOT NULL THEN '已创建团队'
          WHEN ta.status = 1 AND t.id IS NULL THEN '审核通过但团队创建失败'
          WHEN ta.status = 0 THEN '等待审核'
          WHEN ta.status = 2 THEN '申请被拒绝'
          ELSE '状态异常'
        END as process_status
      FROM team_apply ta
      LEFT JOIN user u ON ta.user_id = u.id
      LEFT JOIN team t ON t.leader_id = ta.user_id AND ta.status = 1
      WHERE ta.id = ?
    `;
    
    const result = await db.query(sql, [id]);
    
    if (result.length === 0) {
      return error(res, '联盟申请不存在', 404);
    }
    
    return success(res, result[0], '获取联盟申请详情成功');
  } catch (err) {
    logger.error(`获取联盟申请详情失败: ${err.message}`);
    return error(res, '获取联盟申请详情失败', 500);
  }
};

/**
 * 审核联盟申请
 * POST /api/v1/admin/alliance/audit/:id
 */
const auditAlliance = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, remark } = req.body;
    
    // 验证状态值
    if (![1, 2].includes(parseInt(status))) {
      return error(res, '无效的审核状态', 400);
    }
    
    // 检查申请是否存在
    const checkSql = 'SELECT * FROM team_apply WHERE id = ?';
    const checkResult = await db.query(checkSql, [id]);
    
    if (checkResult.length === 0) {
      return error(res, '联盟申请不存在', 404);
    }
    
    const application = checkResult[0];
    
    // 检查是否已经审核过
    if (application.status !== 0) {
      return error(res, '该申请已经审核过', 400);
    }
    
    await db.transaction(async (connection) => {
      // 更新申请状态
      await connection.query(
        'UPDATE team_apply SET status = ?, remark = ?, updated_at = NOW() WHERE id = ?',
        [status, remark || null, id]
      );
      
      // 如果审核通过，创建团队并设置团长
      if (parseInt(status) === 1) {
        // 创建团队
        const teamResult = await connection.query(
          `INSERT INTO team (name, leader_id, status, created_at, updated_at)
           VALUES (?, ?, 1, NOW(), NOW())`,
          [
            application.name || `${application.contact}的团队`,
            application.user_id
          ]
        );

        const teamId = teamResult[0]?.insertId || teamResult.insertId;

        if (!teamId) {
          throw new Error('创建团队失败，无法获取团队ID');
        }
        
        // 更新用户为团长
        await connection.query(
          'UPDATE user SET team_id = ?, is_leader = ?, updated_at = NOW() WHERE id = ?',
          [teamId, 1, application.user_id]
        );
        
        // 添加团队成员记录
        await connection.query(
          'INSERT INTO team_member (team_id, user_id, role, joined_at) VALUES (?, ?, ?, NOW())',
          [teamId, application.user_id, 'leader']
        );
        
        // 更新团队成员数量
        await connection.query(
          'UPDATE team SET member_count = 1 WHERE id = ?',
          [teamId]
        );
        
        logger.info(`联盟申请审核通过，已创建团队${teamId}，用户${application.user_id}成为团长`);
      }
    });
    
    const statusText = parseInt(status) === 1 ? '通过' : '拒绝';
    return success(res, null, `联盟申请审核${statusText}成功`);
  } catch (err) {
    logger.error(`审核联盟申请失败: ${err.message}`);
    return error(res, '审核联盟申请失败', 500);
  }
};

/**
 * 删除联盟申请
 * DELETE /api/v1/admin/alliance/delete/:id
 */
const deleteAlliance = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查申请是否存在
    const checkResult = await db.query('SELECT * FROM team_apply WHERE id = ?', [id]);
    
    if (checkResult.length === 0) {
      return error(res, '联盟申请不存在', 404);
    }
    
    // 删除申请
    await db.query('DELETE FROM team_apply WHERE id = ?', [id]);
    
    return success(res, null, '删除联盟申请成功');
  } catch (err) {
    logger.error(`删除联盟申请失败: ${err.message}`);
    return error(res, '删除联盟申请失败', 500);
  }
};

/**
 * 获取联盟统计信息
 * GET /api/v1/admin/alliance/stats
 */
const getAllianceStats = async (req, res) => {
  try {
    // 获取申请统计
    const statsSql = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as rejected
      FROM team_apply
    `;
    
    const statsResult = await db.query(statsSql);
    const stats = statsResult[0];
    
    // 获取今日申请数
    const todayStatsSql = `
      SELECT COUNT(*) as today_applications
      FROM team_apply 
      WHERE DATE(created_at) = CURDATE()
    `;
    
    const todayStatsResult = await db.query(todayStatsSql);
    const todayStats = todayStatsResult[0];
    
    // 获取团队统计
    const teamStatsSql = `
      SELECT 
        COUNT(*) as total_teams,
        AVG(member_count) as avg_team_size
      FROM team
    `;
    
    const teamStatsResult = await db.query(teamStatsSql);
    const teamStats = teamStatsResult[0];
    
    return success(res, {
      applications: {
        total: stats.total,
        pending: stats.pending,
        approved: stats.approved,
        rejected: stats.rejected,
        todayCount: todayStats.today_applications
      },
      teams: {
        total: teamStats.total_teams,
        avgSize: parseFloat(teamStats.avg_team_size || 0).toFixed(1)
      }
    }, '获取联盟统计信息成功');
  } catch (err) {
    logger.error(`获取联盟统计信息失败: ${err.message}`);
    return error(res, '获取联盟统计信息失败', 500);
  }
};

/**
 * 获取联盟申请关联的团队信息
 * GET /api/v1/admin/alliance/team-info/:id
 */
const getAllianceTeamInfo = async (req, res) => {
  try {
    const { id } = req.params;

    const sql = `
      SELECT
        ta.*,
        t.id as team_id,
        t.name as team_name,
        t.member_count,
        t.wifi_count,
        t.total_profit,
        t.created_at as team_created_at,
        t.invite_code,
        u.nickname as user_nickname,
        u.avatar as user_avatar,
        u.is_leader,
        CASE
          WHEN ta.status = 1 AND t.id IS NOT NULL THEN '已创建团队'
          WHEN ta.status = 1 AND t.id IS NULL THEN '审核通过但团队创建失败'
          WHEN ta.status = 0 THEN '等待审核'
          WHEN ta.status = 2 THEN '申请被拒绝'
          ELSE '状态异常'
        END as process_status
      FROM team_apply ta
      LEFT JOIN user u ON ta.user_id = u.id
      LEFT JOIN team t ON t.leader_id = ta.user_id AND ta.status = 1
      WHERE ta.id = ?
    `;

    const result = await db.query(sql, [id]);

    if (result.length === 0) {
      return error(res, '联盟申请不存在', 404);
    }

    return success(res, result[0], '获取联盟申请团队信息成功');
  } catch (err) {
    logger.error(`获取联盟申请团队信息失败: ${err.message}`);
    return error(res, '获取联盟申请团队信息失败', 500);
  }
};

module.exports = {
  getAllianceList,
  getAllianceDetail,
  auditAlliance,
  deleteAlliance,
  getAllianceStats,
  getAllianceTeamInfo
};
