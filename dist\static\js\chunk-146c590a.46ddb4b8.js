(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-146c590a"],{"29b5":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"team-list-container"},[e("el-card",{staticClass:"search-card"},[e("el-form",{staticClass:"search-form",attrs:{inline:!0,model:t.searchForm}},[e("el-form-item",{attrs:{label:"团队名称"}},[e("el-input",{attrs:{placeholder:"请输入团队名称",clearable:""},model:{value:t.searchForm.name,callback:function(e){t.$set(t.searchForm,"name",e)},expression:"searchForm.name"}})],1),e("el-form-item",{attrs:{label:"团长"}},[e("el-input",{attrs:{placeholder:"请输入团长姓名",clearable:""},model:{value:t.searchForm.leader,callback:function(e){t.$set(t.searchForm,"leader",e)},expression:"searchForm.leader"}})],1),e("el-form-item",{attrs:{label:"状态"}},[e("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:t.searchForm.status,callback:function(e){t.$set(t.searchForm,"status",e)},expression:"searchForm.status"}},[e("el-option",{attrs:{label:"全部",value:""}}),e("el-option",{attrs:{label:"正常",value:1}}),e("el-option",{attrs:{label:"禁用",value:0}})],1)],1),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")]),e("el-button",{on:{click:t.handleReset}},[t._v("重置")])],1)],1)],1),e("el-card",{staticClass:"table-card"},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("团队列表")]),e("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.handleCreate}},[t._v("创建团队")])],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData,border:""}},[e("el-table-column",{attrs:{prop:"id",label:"ID",width:"80",align:"center"}}),e("el-table-column",{attrs:{prop:"name",label:"团队名称","min-width":"150"}}),e("el-table-column",{attrs:{prop:"leader_name",label:"团长","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("div",{staticClass:"user-info"},[e("el-avatar",{attrs:{size:30,src:a.row.leader_avatar||"/img/default-avatar.png"}}),e("span",{staticClass:"user-name"},[t._v(t._s(a.row.leader_name||"未知"))])],1)]}}])}),e("el-table-column",{attrs:{prop:"member_count",label:"成员数",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:"info"}},[t._v(t._s(a.row.member_count||0))])]}}])}),e("el-table-column",{attrs:{prop:"total_income",label:"总收益",width:"120",align:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticClass:"revenue"},[t._v("¥"+t._s((a.row.total_income||0).toFixed(2)))])]}}])}),e("el-table-column",{attrs:{prop:"month_income",label:"本月收益",width:"120",align:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{staticStyle:{color:"#e6a23c"}},[t._v("¥"+t._s((a.row.month_income||0).toFixed(2)))])]}}])}),e("el-table-column",{attrs:{prop:"status",label:"状态",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:1===a.row.status?"success":"danger"}},[t._v(" "+t._s(1===a.row.status?"正常":"禁用")+" ")])]}}])}),e("el-table-column",{attrs:{prop:"created_at",label:"创建时间",width:"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.created_at))+" ")]}}])}),e("el-table-column",{attrs:{label:"操作",width:"200",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.handleView(a.row)}}},[t._v("查看")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.handleEdit(a.row)}}},[t._v("编辑")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.handleMembers(a.row)}}},[t._v("成员管理")]),e("el-button",{staticStyle:{color:"#F56C6C"},attrs:{type:"text",size:"small"},on:{click:function(e){return t.handleDelete(a.row)}}},[t._v("删除")])]}}])})],1),e("el-pagination",{attrs:{"current-page":t.pagination.page,"page-sizes":[10,20,50,100],"page-size":t.pagination.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.pagination.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1)},r=[],i=(a("14d9"),a("ac98")),l=a("c466"),s={name:"TeamList",data(){return{loading:!1,searchForm:{name:"",leader:"",status:""},tableData:[],pagination:{page:1,limit:10,total:0}}},created(){this.fetchData()},methods:{async fetchData(){this.loading=!0;try{const t={page:this.pagination.page,limit:this.pagination.limit,...this.searchForm},e=await Object(i["e"])(t);0===e.code&&(this.tableData=e.data.list||[],this.pagination.total=e.data.pagination&&e.data.pagination.total||e.data.total||0)}catch(t){console.error("获取团队列表失败:",t),this.tableData=[],this.pagination.total=0,this.$message.error("获取团队列表失败")}finally{this.loading=!1}},handleSearch(){this.pagination.page=1,this.fetchData()},handleReset(){this.searchForm={name:"",leader:"",status:""},this.pagination.page=1,this.fetchData()},handleCreate(){this.$router.push("/team/create")},handleView(t){this.$router.push("/team/detail/"+t.id)},handleEdit(t){this.$router.push("/team/edit/"+t.id)},handleMembers(t){this.$router.push("/team/members/"+t.id)},handleDelete(t){this.$confirm("确定要删除该团队吗？删除后不可恢复！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await Object(i["c"])(t.id);0===e.code&&(this.$message.success("删除成功"),this.fetchData())}catch(e){this.$message.error("删除失败")}}).catch(()=>{})},handleSizeChange(t){this.pagination.limit=t,this.fetchData()},handleCurrentChange(t){this.pagination.page=t,this.fetchData()},formatDate:l["a"]}},o=s,c=(a("68c1"),a("2877")),u=Object(c["a"])(o,n,r,!1,null,"3ddd98dd",null);e["default"]=u.exports},"446b":function(t,e,a){},"68c1":function(t,e,a){"use strict";a("446b")},ac98:function(t,e,a){"use strict";a.d(e,"e",(function(){return r})),a.d(e,"d",(function(){return i})),a.d(e,"b",(function(){return l})),a.d(e,"j",(function(){return s})),a.d(e,"c",(function(){return o})),a.d(e,"g",(function(){return c})),a.d(e,"f",(function(){return u})),a.d(e,"a",(function(){return d})),a.d(e,"i",(function(){return m})),a.d(e,"h",(function(){return h}));var n=a("b775");function r(t){return Object(n["a"])({url:"/api/v1/admin/team/list",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/api/v1/admin/team/detail/"+t,method:"get"})}function l(t){return Object(n["a"])({url:"/api/v1/admin/team/create",method:"post",data:t})}function s(t,e){return Object(n["a"])({url:"/api/v1/admin/team/update/"+t,method:"put",data:e})}function o(t){return Object(n["a"])({url:"/api/v1/admin/team/delete/"+t,method:"delete"})}function c(t){return Object(n["a"])({url:"/api/v1/admin/team/stats/"+t,method:"get"})}function u(t,e){return Object(n["a"])({url:`/api/v1/admin/team/${t}/members`,method:"get",params:e})}function d(t,e){return Object(n["a"])({url:`/api/v1/admin/team/${t}/members`,method:"post",data:{user_id:e}})}function m(t,e){return Object(n["a"])({url:`/api/v1/admin/team/${t}/members/${e}`,method:"delete"})}function h(t,e){return m(t,e)}},c466:function(t,e,a){"use strict";function n(t,e="YYYY-MM-DD HH:mm:ss"){if(!t)return"";const a=new Date(t);if(isNaN(a.getTime()))return"";const n=a.getFullYear(),r=String(a.getMonth()+1).padStart(2,"0"),i=String(a.getDate()).padStart(2,"0"),l=String(a.getHours()).padStart(2,"0"),s=String(a.getMinutes()).padStart(2,"0"),o=String(a.getSeconds()).padStart(2,"0");return e.replace("YYYY",n).replace("MM",r).replace("DD",i).replace("HH",l).replace("mm",s).replace("ss",o)}a.d(e,"a",(function(){return n}))}}]);