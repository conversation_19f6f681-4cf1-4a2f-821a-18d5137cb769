// pages/user/wallet/wallet.js
const app = getApp()
const API = require('../../../config/api')
const { request } = require('../../../utils/request')
const { showToast, showModal } = require('../../../utils/util')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    balance: '0.00',
    incomeStats: {
      wifi: '0.00',
      team: '0.00',
      ads: '0.00',
      mall: '0.00'
    },
    transactions: [],
    loading: true,
    isLoggedIn: false,
    page: 1,
    limit: 10,
    hasMore: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.checkLoginStatus()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    if (this.data.isLoggedIn) {
      this.fetchWalletData()
      this.fetchTransactions()
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function () {
    const isLoggedIn = app.globalData.isLoggedIn
    this.setData({ isLoggedIn })
    
    if (!isLoggedIn) {
      showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.navigateBack()
        }
      })
    }
  },

  /**
   * 获取钱包数据
   */
  fetchWalletData: function () {
    this.setData({ loading: true })

    // 检查API配置是否存在
    if (!API || !API.income || !API.income.stats) {
      console.error('❌ API配置错误: API.income.stats 不存在')
      showToast('API配置错误')
      this.setData({ loading: false })
      return
    }

    request({
      url: API.income.stats,
      method: 'GET'
    }).then(res => {
      console.log('💰 钱包数据响应:', res)
      // 兼容不同的响应格式：code: 0 或 code: 200 或 success: true
      if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
        // 适配后端返回的数据结构
        const data = res.data
        this.setData({
          balance: data.total?.balance || '0.00',
          incomeStats: {
            wifi: data.total?.wifi_income || '0.00',
            team: data.this_month?.income || '0.00',
            ads: data.total?.ad_income || '0.00',
            mall: data.total?.goods_income || '0.00'
          },
          loading: false
        })
        console.log('✅ 钱包数据更新成功')
      } else {
        console.error('❌ 钱包数据响应格式错误:', res)
        showToast('获取钱包数据失败')
        this.setData({ loading: false })
      }
    }).catch(err => {
      console.error('❌ 获取钱包数据失败:', err)
      showToast('获取钱包数据失败')
      this.setData({ loading: false })
    })
  },

  /**
   * 获取交易记录
   */
  fetchTransactions: function () {
    if (!this.data.hasMore) return
    
    request({
      url: API.income.details,
      method: 'GET',
      data: {
        page: this.data.page,
        limit: this.data.limit
      }
    }).then(res => {
      console.log('💰 收入明细响应:', res)
      // 兼容不同的响应格式：code: 0 或 code: 200 或 success: true
      if ((res.code === 0 || res.code === 200 || res.success === true) && res.data) {
        const newTransactions = res.data.list || []
        const transactions = this.data.page === 1
          ? newTransactions
          : [...this.data.transactions, ...newTransactions]

        this.setData({
          transactions,
          hasMore: newTransactions.length === this.data.limit,
          page: this.data.page + 1
        })
        console.log('✅ 收入明细更新成功，共', newTransactions.length, '条记录')
      } else {
        console.error('❌ 收入明细响应格式错误:', res)
        showToast('获取交易记录失败')
      }
    }).catch(err => {
      console.error('获取交易记录失败', err)
      showToast('获取交易记录失败')
    })
  },

  /**
   * 充值操作
   */
  onRecharge: function () {
    showToast('充值功能开发中')
  },

  /**
   * 提现操作
   */
  onWithdraw: function () {
    if (parseFloat(this.data.balance) <= 0) {
      showToast('余额不足，无法提现')
      return
    }
    
    wx.navigateTo({
      url: '/pages/user/wallet/withdraw'
    })
  },

  /**
   * 查看明细
   */
  onViewDetails: function () {
    // 已在当前页面显示
  },

  /**
   * 查看更多交易记录
   */
  onViewMore: function () {
    if (this.data.hasMore) {
      this.fetchTransactions()
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    if (this.data.isLoggedIn) {
      this.setData({ page: 1, hasMore: true })
      Promise.all([
        this.fetchWalletData(),
        this.fetchTransactions()
      ]).finally(() => {
        wx.stopPullDownRefresh()
      })
    } else {
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.isLoggedIn && this.data.hasMore) {
      this.fetchTransactions()
    }
  },

  /**
   * 广告加载成功
   */
  onAdLoad: function () {
    console.log('广告加载成功')
  },

  /**
   * 广告加载失败
   */
  onAdError: function (err) {
    console.error('广告加载失败', err)
  }
}) 