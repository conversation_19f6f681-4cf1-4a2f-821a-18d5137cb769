(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-df93d3ac"],{"888a":function(t,e,a){"use strict";a("f45f")},c4a2:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("el-card",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("申请详情")]),e("el-button-group",{staticStyle:{float:"right"}},[0===t.detail.status?e("el-button",{attrs:{type:"success",size:"mini"},on:{click:t.handleApprove}},[t._v("通过申请")]):t._e(),0===t.detail.status?e("el-button",{attrs:{type:"danger",size:"mini"},on:{click:t.handleReject}},[t._v("拒绝申请")]):t._e(),e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.goBack}},[t._v("返回列表")])],1)],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("h4",{staticClass:"detail-title"},[t._v("申请信息")]),e("el-descriptions",{staticClass:"margin-top",attrs:{column:1,border:""}},[e("el-descriptions-item",{attrs:{label:"申请ID"}},[t._v(t._s(t.detail.id))]),e("el-descriptions-item",{attrs:{label:"团队名称"}},[t._v(t._s(t.detail.name))]),e("el-descriptions-item",{attrs:{label:"联系电话"}},[t._v(t._s(t.detail.phone))]),e("el-descriptions-item",{attrs:{label:"区域"}},[t._v(t._s(t.detail.area))]),e("el-descriptions-item",{attrs:{label:"申请时间"}},[t._v(" "+t._s(t.formatDate(t.detail.created_at))+" ")]),e("el-descriptions-item",{attrs:{label:"申请状态"}},[e("el-tag",{attrs:{type:t._f("statusFilter")(t.detail.status)}},[t._v(t._s(t._f("statusTextFilter")(t.detail.status)))])],1),e("el-descriptions-item",{attrs:{label:"备注"}},[t._v(t._s(t.detail.remark||"无"))]),e("el-descriptions-item",{attrs:{label:"简介"}},[e("div",{staticClass:"description-content"},[t._v(t._s(t.detail.description||"无"))])])],1)],1),e("el-col",{attrs:{span:12}},[e("h4",{staticClass:"detail-title"},[t._v("用户信息")]),e("el-descriptions",{staticClass:"margin-top",attrs:{column:1,border:""}},[e("el-descriptions-item",{attrs:{label:"用户ID"}},[t._v(t._s(t.detail.user_id))]),e("el-descriptions-item",{attrs:{label:"用户昵称"}},[t._v(t._s(t.detail.nickname||"未设置昵称"))]),e("el-descriptions-item",{attrs:{label:"用户头像"}},[e("el-avatar",{attrs:{size:60,src:t.detail.avatar||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"}})],1),e("el-descriptions-item",{attrs:{label:"手机号码"}},[t._v(t._s(t.detail.user_phone||"未绑定手机"))]),e("el-descriptions-item",{attrs:{label:"性别"}},[t._v(" "+t._s(1===t.detail.gender?"男":2===t.detail.gender?"女":"未知")+" ")]),e("el-descriptions-item",{attrs:{label:"注册时间"}},[t._v(" "+t._s(t.formatDate(t.detail.user_created_at))+" ")])],1)],1)],1)],1),e("el-dialog",{attrs:{title:t.auditDialogTitle,visible:t.auditDialogVisible,width:"30%"},on:{"update:visible":function(e){t.auditDialogVisible=e}}},[e("el-form",{attrs:{model:t.auditForm,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"备注"}},[e("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入审核备注"},model:{value:t.auditForm.remark,callback:function(e){t.$set(t.auditForm,"remark",e)},expression:"auditForm.remark"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.auditDialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.submitAudit}},[t._v("确 定")])],1)],1)],1)},s=[],l=(a("14d9"),a("e61d")),r={name:"AllianceDetail",filters:{statusFilter(t){const e={0:"info",1:"success",2:"danger"};return e[t]},statusTextFilter(t){const e={0:"待审核",1:"已通过",2:"已拒绝"};return e[t]}},data(){return{loading:!1,detail:{id:null,user_id:null,name:"",phone:"",area:"",description:"",status:0,remark:"",created_at:"",updated_at:"",nickname:"",avatar:"",user_phone:"",gender:0,user_created_at:""},auditDialogVisible:!1,auditDialogTitle:"审核",auditForm:{id:null,status:null,remark:""}}},created(){this.fetchData()},methods:{fetchData(){this.loading=!0;const t=this.$route.params.id;Object(l["c"])(t).then(t=>{this.detail=t.data,this.loading=!1}).catch(()=>{this.loading=!1})},goBack(){this.$router.push({path:"/alliance/list"})},handleApprove(){this.auditDialogTitle="通过申请",this.auditForm={id:this.detail.id,status:1,remark:""},this.auditDialogVisible=!0},handleReject(){this.auditDialogTitle="拒绝申请",this.auditForm={id:this.detail.id,status:2,remark:""},this.auditDialogVisible=!0},submitAudit(){Object(l["a"])(this.auditForm.id,{status:this.auditForm.status,remark:this.auditForm.remark}).then(t=>{this.$message({type:"success",message:"审核成功!"}),this.auditDialogVisible=!1,this.fetchData()})},formatDate(t){if(!t)return"";const e=new Date(t),a=e.getFullYear(),i=String(e.getMonth()+1).padStart(2,"0"),s=String(e.getDate()).padStart(2,"0"),l=String(e.getHours()).padStart(2,"0"),r=String(e.getMinutes()).padStart(2,"0");return`${a}-${i}-${s} ${l}:${r}`}}},n=r,d=(a("888a"),a("2877")),o=Object(d["a"])(n,i,s,!1,null,"5c5ae5b0",null);e["default"]=o.exports},e61d:function(t,e,a){"use strict";a.d(e,"d",(function(){return s})),a.d(e,"c",(function(){return l})),a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return n}));var i=a("b775");function s(t){return Object(i["a"])({url:"/api/v1/admin/alliance/list",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/api/v1/admin/alliance/detail/"+t,method:"get"})}function r(t,e){return Object(i["a"])({url:"/api/v1/admin/alliance/audit/"+t,method:"post",data:e})}function n(t){return Object(i["a"])({url:"/api/v1/admin/alliance/delete/"+t,method:"delete"}).catch(e=>{if(e.response&&404===e.response.status)return Object(i["a"])({url:"/api/v1/admin/alliance/delete/"+t,method:"post"});throw e})}},f45f:function(t,e,a){}}]);