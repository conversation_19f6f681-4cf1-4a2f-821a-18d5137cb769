/* pages/user/profile/profile.wxss */
.profile-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 30rpx;
}

/* 广告区域 */
.ad-banner {
  position: relative;
  width: 100%;
  height: 200rpx;
}

.ad-image {
  width: 100%;
  height: 100%;
}

.ad-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
}

.ad-text {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

/* 腾讯广告容器样式 */
.ad-container {
  width: 100%;
  margin: 20rpx 0;
  padding: 0;
  display: flex;
  justify-content: center;
  background-color: #fff;
}

/* 用户信息区域 */
.user-section {
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
}

/* 头像选择按钮样式 */
.avatar-button {
  padding: 0;
  background: none;
  border: none;
  margin: 0;
  line-height: normal;
  position: relative;
}

.avatar-button::after {
  border: none;
}

.avatar-edit-icon {
  position: absolute;
  bottom: 0;
  right: 20rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
}

.user-detail {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-nickname {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

/* 昵称输入框样式 */
.nickname-input {
  font-size: 32rpx;
  border-bottom: 1px solid #eee;
  padding: 8rpx 0;
  width: 100%;
  margin-bottom: 10rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
}

.user-demote {
  font-size: 22rpx;
  color: #ff6b6b;
  margin-top: 6rpx;
  text-decoration: underline;
}

.user-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.logout-btn {
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  background-color: #f8f8f8;
  color: #333;
  border-radius: 30rpx;
  border: 1px solid #ddd;
  margin: 0;
  line-height: 1.5;
}

.save-btn {
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  background-color: #ff6b6b;
  color: white;
  border-radius: 30rpx;
  margin: 10rpx 0 0 0;
  line-height: 1.5;
}

/* 订单区域 */
.order-section {
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
}

.section-action {
  display: flex;
  align-items: center;
}

.action-text {
  font-size: 28rpx;
  color: #999;
  margin-right: 10rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: #999;
}

.order-status-list {
  display: flex;
  justify-content: space-between;
}

.order-status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.status-icon-wrapper {
  position: relative;
  margin-bottom: 10rpx;
}

.status-icon {
  font-size: 48rpx;
}

.status-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background-color: #ff6b6b;
  color: white;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
}

.status-text {
  font-size: 24rpx;
  color: #333;
}

/* 菜单区域 */
.menu-section {
  background-color: white;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.menu-row {
  display: flex;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item {
  flex: 1;
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.menu-divider {
  width: 1rpx;
  background-color: #f5f5f5;
}

.menu-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
}

.menu-badge {
  background-color: #ff6b6b;
  color: white;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
  margin-right: 10rpx;
}

.menu-arrow {
  font-size: 24rpx;
  color: #999;
}

.menu-single-row {
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item-full {
  padding: 30rpx;
  display: flex;
  align-items: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  padding: 30rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 30rpx;
  background-color: white;
  border-radius: 10rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.login-btn {
  font-size: 28rpx;
  padding: 15rpx 40rpx;
  background-color: #ff6b6b;
  color: white;
  border-radius: 30rpx;
}

/* 客服按钮样式 */
.contact-button {
  background: none;
  padding: 0;
  margin: 0;
  line-height: 1;
  border: none;
  display: flex;
  align-items: center;
  width: 100%;
  text-align: left;
  font-weight: normal;
  font-size: inherit;
}

.contact-button::after {
  border: none;
} 