const express = require('express');
const router = express.Router();
const { verifyToken, adminAuth } = require('../middlewares/auth');
const logger = require('../utils/logger');
const { success, error } = require('../utils/response');

// 模拟数据 - 提现申请列表
const withdrawList = [
  {
    id: 1,
    withdraw_no: 'W2025070800001',
    user_id: 1001,
    user_name: '张三',
    user_phone: '138****1001',
    amount: 200.00,
    status: 0,  // 0待审核，1审核通过待打款，2审核拒绝，3已打款
    type: 1,  // 1支付宝，2银行卡
    account_type: '支付宝',
    account_name: '张三',
    account_no: 'zhang<PERSON>@example.com',
    bank_name: null,
    created_at: '2025-07-08 10:12:33',
    audit_time: null,
    audit_user: null,
    audit_remark: null,
    pay_time: null,
    pay_remark: null
  },
  {
    id: 2,
    withdraw_no: 'W2025070800002',
    user_id: 1002,
    user_name: '李四',
    user_phone: '138****1002',
    amount: 500.00,
    status: 1,
    type: 2,
    account_type: '银行卡',
    account_name: '李四',
    account_no: '6222xxxxxxx',
    bank_name: '工商银行',
    created_at: '2025-07-08 09:45:21',
    audit_time: '2025-07-08 11:23:45',
    audit_user: 'admin',
    audit_remark: '审核通过',
    pay_time: null,
    pay_remark: null
  },
  {
    id: 3,
    withdraw_no: 'W2025070800003',
    user_id: 1003,
    user_name: '王五',
    user_phone: '138****1003',
    amount: 100.00,
    status: 2,
    type: 1,
    account_type: '支付宝',
    account_name: '王五',
    account_no: '<EMAIL>',
    bank_name: null,
    created_at: '2025-07-07 16:32:18',
    audit_time: '2025-07-08 09:15:30',
    audit_user: 'admin',
    audit_remark: '金额不足，请重新提交',
    pay_time: null,
    pay_remark: null
  },
  {
    id: 4,
    withdraw_no: 'W2025070800004',
    user_id: 1004,
    user_name: '赵六',
    user_phone: '138****1004',
    amount: 300.00,
    status: 3,
    type: 2,
    account_type: '银行卡',
    account_name: '赵六',
    account_no: '6217xxxxxxx',
    bank_name: '招商银行',
    created_at: '2025-07-07 14:56:42',
    audit_time: '2025-07-07 16:28:35',
    audit_user: 'admin',
    audit_remark: '审核通过',
    pay_time: '2025-07-08 10:35:12',
    pay_remark: '打款成功'
  }
];

// 获取提现申请列表
router.get('/list', verifyToken, adminAuth, (req, res) => {
  try {
    const { page = 1, limit = 10, keyword, status, start_date, end_date } = req.query;
    
    // 过滤提现申请
    let filteredWithdraws = [...withdrawList];
    
    // 按关键词搜索
    if (keyword) {
      filteredWithdraws = filteredWithdraws.filter(withdraw => 
        withdraw.user_name.includes(keyword) ||
        withdraw.user_phone.includes(keyword) ||
        withdraw.withdraw_no.includes(keyword)
      );
    }
    
    // 按状态筛选
    if (status !== undefined && status !== '') {
      filteredWithdraws = filteredWithdraws.filter(withdraw => withdraw.status === parseInt(status));
    }
    
    // 按日期筛选
    if (start_date && end_date) {
      filteredWithdraws = filteredWithdraws.filter(withdraw => {
        const withdrawDate = new Date(withdraw.created_at);
        const startDate = new Date(start_date);
        const endDate = new Date(end_date);
        endDate.setHours(23, 59, 59, 999);
        return withdrawDate >= startDate && withdrawDate <= endDate;
      });
    }
    
    // 计算分页
    const total = filteredWithdraws.length;
    const pageNum = parseInt(page);
    const pageSize = parseInt(limit);
    const start = (pageNum - 1) * pageSize;
    const end = start + pageSize;
    const list = filteredWithdraws.slice(start, end);
    
    logger.info('获取提现申请列表成功');
    return success(res, {
      list,
      total,
      page: pageNum,
      limit: pageSize
    }, '获取提现申请列表成功');
  } catch (err) {
    logger.error('获取提现申请列表失败:', err);
    return error(res, '获取提现申请列表失败');
  }
});

// 获取提现申请详情
router.get('/detail/:id', verifyToken, adminAuth, (req, res) => {
  try {
    const { id } = req.params;
    const withdraw = withdrawList.find(w => w.id === parseInt(id));
    
    if (!withdraw) {
      return error(res, '提现申请不存在', 404);
    }
    
    // 构造返回数据
    const detailData = {
      detail: {
        ...withdraw,
        card_holder: withdraw.account_name,
        bank_name: withdraw.bank_name || '支付宝',
        card_number: withdraw.account_no,
        bank_branch: withdraw.bank_name === '工商银行' ? '北京市朝阳区支行' : null,
        transfer_time: withdraw.pay_time,
        transaction_id: withdraw.status === 3 ? 'TRX202507080001' : null,
        remark: withdraw.audit_remark || withdraw.pay_remark || null
      },
      user_info: {
        id: withdraw.user_id,
        nickname: withdraw.user_name,
        phone: withdraw.user_phone,
        avatar: '/img/default-avatar.png',
        balance: (Math.random() * 1000 + 500).toFixed(2),
        is_leader: withdraw.user_id % 2 === 0 ? 0 : 1
      }
    };
    
    logger.info('获取提现申请详情成功');
    return success(res, detailData, '获取提现申请详情成功');
  } catch (err) {
    logger.error('获取提现申请详情失败:', err);
    return error(res, '获取提现申请详情失败');
  }
});

// 审核提现申请
router.post('/audit/:id', verifyToken, adminAuth, (req, res) => {
  try {
    const { id } = req.params;
    const { status, remark } = req.body;
    
    const withdrawIndex = withdrawList.findIndex(w => w.id === parseInt(id));
    
    if (withdrawIndex === -1) {
      return error(res, '提现申请不存在', 404);
    }
    
    // 检查提现申请状态
    if (withdrawList[withdrawIndex].status !== 0) {
      return error(res, '只有待审核的提现申请可以进行审核操作', 400);
    }
    
    // 检查状态参数
    if (status !== 1 && status !== 2) {
      return error(res, '状态参数错误，只能是1(通过)或2(拒绝)', 400);
    }
    
    // 更新提现申请状态
    withdrawList[withdrawIndex].status = status;
    withdrawList[withdrawIndex].audit_time = new Date().toISOString().replace('T', ' ').slice(0, 19);
    withdrawList[withdrawIndex].audit_user = 'admin';
    withdrawList[withdrawIndex].audit_remark = remark || (status === 1 ? '审核通过' : '审核拒绝');
    
    logger.info('审核提现申请成功');
    return success(res, {}, '审核成功');
  } catch (err) {
    logger.error('审核提现申请失败:', err);
    return error(res, '审核提现申请失败');
  }
});

// 确认提现打款
router.post('/confirm/:id', verifyToken, adminAuth, (req, res) => {
  try {
    const { id } = req.params;
    const { transaction_id, remark } = req.body;
    
    const withdrawIndex = withdrawList.findIndex(w => w.id === parseInt(id));
    
    if (withdrawIndex === -1) {
      return error(res, '提现申请不存在', 404);
    }
    
    // 检查提现申请状态
    if (withdrawList[withdrawIndex].status !== 1) {
      return error(res, '只有审核通过的提现申请可以进行打款操作', 400);
    }
    
    // 更新提现申请状态
    withdrawList[withdrawIndex].status = 3;
    withdrawList[withdrawIndex].pay_time = new Date().toISOString().replace('T', ' ').slice(0, 19);
    withdrawList[withdrawIndex].pay_remark = remark || '已打款';
    withdrawList[withdrawIndex].transaction_id = transaction_id || `TRX${Date.now().toString().substr(-8)}`;
    
    logger.info('确认提现打款成功');
    return success(res, {}, '打款成功');
  } catch (err) {
    logger.error('确认提现打款失败:', err);
    return error(res, '确认提现打款失败');
  }
});

module.exports = router; 