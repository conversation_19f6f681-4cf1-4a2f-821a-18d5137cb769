import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login']

router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()

  console.log('路由守卫: 从', from.path, '到', to.path)

  // 设置页面标题
  document.title = `${to.meta.title || ''} - ${process.env.VUE_APP_TITLE}`

  // 确定用户是否已登录
  const hasToken = getToken()
  console.log('是否有token:', !!hasToken, 'token值:', hasToken)

  if (hasToken) {
    if (to.path === '/login') {
      // 如果已登录，则重定向到主页
      console.log('已登录，重定向到首页')
      next({ path: '/' })
      NProgress.done()
    } else {
      // 确定用户是否已通过getInfo获取其权限角色
      const hasRoles = store.getters.roles && store.getters.roles.length > 0
      console.log('是否有角色信息:', hasRoles, '角色:', store.getters.roles)

      if (hasRoles) {
        next()
      } else {
        try {
          console.log('开始获取用户信息...')
          // 获取用户信息
          await store.dispatch('user/getInfo')
          console.log('获取用户信息成功，继续导航')
          next()
        } catch (error) {
          console.error('获取用户信息失败:', error)
          // 移除token并跳转到登录页重新登录
          await store.dispatch('user/resetToken')
          Message.error(error.message || 'Has Error')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    /* 没有token */
    console.log('没有token，检查是否在白名单中')
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单中，直接进入
      next()
    } else {
      // 没有访问权限的其他页面将重定向到登录页面
      console.log('重定向到登录页')
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // 完成进度条
  NProgress.done()
})
