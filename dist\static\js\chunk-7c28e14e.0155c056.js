(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7c28e14e"],{"274d":function(t,e,a){"use strict";a.d(e,"c",(function(){return l})),a.d(e,"b",(function(){return i})),a.d(e,"a",(function(){return n}));var s=a("b775");function l(t){return Object(s["a"])({url:"/api/v1/admin/region/list",method:"get",params:t})}function i(t){return Object(s["a"])({url:"/api/v1/admin/region/detail/"+t,method:"get"})}function n(t){return Object(s["a"])({url:"/api/v1/admin/region/delete/"+t,method:"delete"})}},"3aa3":function(t,e,a){},"6efd":function(t,e,a){"use strict";a("3aa3")},c29d:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"region-detail"},[e("div",{staticClass:"detail-header"},[e("el-button",{attrs:{icon:"el-icon-arrow-left"},on:{click:t.goBack}},[t._v("返回")]),e("h2",[t._v("地区详情")])],1),e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("基本信息")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.handleEdit}},[t._v("编辑")])],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("label",[t._v("地区ID：")]),e("span",[t._v(t._s(t.regionInfo.id))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("label",[t._v("地区名称：")]),e("span",[t._v(t._s(t.regionInfo.name))])])])],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("label",[t._v("地区代码：")]),e("span",[t._v(t._s(t.regionInfo.code))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("label",[t._v("父级地区：")]),e("span",[t._v(t._s(t.regionInfo.parent_name||"无"))])])])],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("label",[t._v("级别：")]),e("el-tag",{attrs:{type:1===t.regionInfo.level?"primary":2===t.regionInfo.level?"success":"info"}},[t._v(" "+t._s(t.getLevelText(t.regionInfo.level))+" ")])],1)]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("label",[t._v("状态：")]),e("el-tag",{attrs:{type:1===t.regionInfo.status?"success":"danger"}},[t._v(" "+t._s(1===t.regionInfo.status?"启用":"禁用")+" ")])],1)])],1),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("label",[t._v("创建时间：")]),e("span",[t._v(t._s(t.regionInfo.created_at))])])]),e("el-col",{attrs:{span:12}},[e("div",{staticClass:"detail-item"},[e("label",[t._v("更新时间：")]),e("span",[t._v(t._s(t.regionInfo.updated_at))])])])],1),t.regionInfo.description?e("el-row",[e("el-col",{attrs:{span:24}},[e("div",{staticClass:"detail-item"},[e("label",[t._v("描述：")]),e("p",[t._v(t._s(t.regionInfo.description))])])])],1):t._e()],1),t.subRegions.length>0?e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("子地区列表")])]),e("el-table",{staticStyle:{width:"100%"},attrs:{data:t.subRegions,border:""}},[e("el-table-column",{attrs:{prop:"id",label:"ID",width:"80"}}),e("el-table-column",{attrs:{prop:"name",label:"地区名称"}}),e("el-table-column",{attrs:{prop:"code",label:"地区代码"}}),e("el-table-column",{attrs:{label:"级别",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:1===a.row.level?"primary":2===a.row.level?"success":"info"}},[t._v(" "+t._s(t.getLevelText(a.row.level))+" ")])]}}],null,!1,2988787840)}),e("el-table-column",{attrs:{label:"状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:1===a.row.status?"success":"danger"}},[t._v(" "+t._s(1===a.row.status?"启用":"禁用")+" ")])]}}],null,!1,593812414)}),e("el-table-column",{attrs:{label:"操作",width:"150"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.viewSubRegion(a.row)}}},[t._v("查看")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.editSubRegion(a.row)}}},[t._v("编辑")])]}}],null,!1,404044684)})],1)],1):t._e()],1)},l=[],i=(a("14d9"),a("274d")),n={name:"RegionDetail",data(){return{regionInfo:{},subRegions:[]}},created(){this.fetchData()},methods:{fetchData(){const t=this.$route.params.id;Object(i["b"])(t).then(t=>{this.regionInfo=t.data.region,this.subRegions=t.data.subRegions||[]}).catch(t=>{console.error("获取地区详情失败:",t),this.$message.error("获取地区详情失败")})},goBack(){this.$router.go(-1)},handleEdit(){this.$router.push("/region/edit/"+this.regionInfo.id)},viewSubRegion(t){this.$router.push("/region/detail/"+t.id)},editSubRegion(t){this.$router.push("/region/edit/"+t.id)},getLevelText(t){const e={1:"省级",2:"市级",3:"区县级"};return e[t]||"未知"}}},r=n,o=(a("6efd"),a("2877")),c=Object(o["a"])(r,s,l,!1,null,"2c12853c",null);e["default"]=c.exports}}]);