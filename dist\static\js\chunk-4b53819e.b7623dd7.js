(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4b53819e"],{"330e":function(t,e,a){"use strict";a("34b0")},"333d":function(t,e,a){"use strict";var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[e("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},n=[],i={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get(){return this.page},set(t){this.$emit("update:page",t)}},pageSize:{get(){return this.limit},set(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange(t){this.$emit("pagination",{page:this.currentPage,limit:t})},handleCurrentChange(t){this.$emit("pagination",{page:t,limit:this.pageSize})}}},s=i,o=(a("330e"),a("2877")),d=Object(o["a"])(s,r,n,!1,null,"11252b03",null);e["a"]=d.exports},"34b0":function(t,e,a){},"46fa":function(t,e,a){},"92c2":function(t,e,a){"use strict";a.d(e,"g",(function(){return o})),a.d(e,"f",(function(){return d})),a.d(e,"k",(function(){return u})),a.d(e,"l",(function(){return c})),a.d(e,"c",(function(){return l})),a.d(e,"m",(function(){return m})),a.d(e,"d",(function(){return p})),a.d(e,"i",(function(){return f})),a.d(e,"h",(function(){return b})),a.d(e,"a",(function(){return _})),a.d(e,"b",(function(){return v})),a.d(e,"e",(function(){return w})),a.d(e,"j",(function(){return y}));a("14d9"),a("e9f5"),a("ab43");var r=a("b775");const n=!1,i="wifi_admin_user_tags",s=[{id:1,name:"VIP用户",description:"重要VIP客户",created_at:"2023-06-10 10:30:45"},{id:2,name:"新用户",description:"注册不满30天的用户",created_at:"2023-06-08 14:20:30"},{id:3,name:"商家",description:"拥有商铺的用户",created_at:"2023-06-05 09:15:00"},{id:4,name:"活跃用户",description:"近30天有登录的用户",created_at:"2023-06-01 16:40:20"}];function o(t){return n?new Promise(e=>{setTimeout(()=>{const a=[],r=10*t.pageSize||100;for(let e=1;e<=Math.min(t.pageSize||10,r);e++){const r=(t.pageNum-1)*(t.pageSize||10),n=r+e;a.push({id:n,openid:"oMKLx5M2xxxxxxxx"+n,nickname:["张三","李四","王五","赵六","刘七"][e%5]||"用户"+n,avatar:"/img/default-avatar.png",gender:e%3,phone:"1380013800"+e,balance:parseFloat((500*Math.random()).toFixed(2)),team_id:e%3===0?1:e%3===1?2:null,is_leader:e%7===0?1:0,level:e%5,status:e%10===0?0:1,created_at:`2023-05-${String(e%30+1).padStart(2,"0")} 10:20:30`,updated_at:`2023-06-${String(e%28+1).padStart(2,"0")} 15:30:45`})}e({code:200,data:{list:a,total:r},message:"获取用户列表成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/list",method:"get",params:t})}function d(t){return n?new Promise(e=>{setTimeout(()=>{const a=parseInt(t),r={id:a,openid:"oMKLx5M2xxxxxxxx"+t,nickname:["","张三","李四","王五","赵六"][a]||"用户"+t,avatar:"/img/default-avatar.png",gender:a%2===0?2:1,phone:"1380013800"+t,balance:parseFloat((500*Math.random()).toFixed(2)),team_id:a<=2?1:3===a?2:null,parent_id:2===a?1:null,is_leader:[1,3].includes(a)?1:0,level:[1,3].includes(a)?2:2===a?1:0,status:4===a?0:1,created_at:`2023-05-0${a>4?"1":a} 10:20:30`,updated_at:`2023-05-0${a>4?"1":a} 15:30:45`};let n=null;r.team_id&&(n={id:r.team_id,name:"团队"+r.team_id,member_count:Math.floor(20*Math.random())+5,wifi_count:Math.floor(10*Math.random())+2,total_profit:parseFloat((5e3*Math.random()).toFixed(2))});const i=[{id:10*a+1,title:r.nickname+"的WiFi-1",name:`Wifi_${a}_1`,use_count:Math.floor(100*Math.random())+10,created_at:"2023-06-01 09:30:00"},{id:10*a+2,title:r.nickname+"的WiFi-2",name:`Wifi_${a}_2`,use_count:Math.floor(100*Math.random())+10,created_at:"2023-06-02 14:20:00"}],s=[{id:100*a+1,order_no:`WF${Date.now()}${a}01`,total_amount:parseFloat((200*Math.random()+50).toFixed(2)),status:Math.floor(4*Math.random())+1,created_at:"2023-06-01 16:45:30"},{id:100*a+2,order_no:`WF${Date.now()}${a}02`,total_amount:parseFloat((200*Math.random()+50).toFixed(2)),status:Math.floor(4*Math.random())+1,created_at:"2023-06-02 10:15:20"}];e({code:200,data:{user_info:r,team_info:n,wifi_list:i,order_list:s},message:"获取用户详情成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/detail/"+t,method:"get"})}function u(t,e){return n?new Promise(t=>{setTimeout(()=>{t({code:200,data:{},message:"更新用户成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/update/"+t,method:"put",data:e})}function c(t,e){return n?new Promise(t=>{setTimeout(()=>{t({code:200,data:{},message:"更新用户状态成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/status/"+t,method:"put",data:e})}function l(t){return n?new Promise(e=>{setTimeout(()=>{const a=h(),r=a.length>0?Math.max(...a.map(t=>t.id))+1:1,n={id:r,name:t.name,description:t.description||"",created_at:(new Date).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")};a.push(n),g(a),e({code:200,data:{id:r},message:"创建用户标签成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/tag/create",method:"post",data:t})}function m(t,e){return n?new Promise(a=>{setTimeout(()=>{const r=h(),n=r.findIndex(e=>e.id===parseInt(t));-1!==n?(r[n].name=e.name,r[n].description=e.description||r[n].description,g(r),a({code:200,message:"更新用户标签成功"})):a({code:404,message:"标签不存在"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/tag/update/"+t,method:"put",data:e})}function p(t){return n?new Promise(e=>{setTimeout(()=>{const a=h(),r=a.findIndex(e=>e.id===parseInt(t));-1!==r?(a.splice(r,1),g(a),e({code:200,message:"删除用户标签成功"})):e({code:404,message:"标签不存在"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/tag/delete/"+t,method:"delete"})}function h(){try{const t=localStorage.getItem(i);return t?JSON.parse(t):s}catch(t){return console.warn("读取用户标签数据失败，使用默认数据:",t),s}}function g(t){try{localStorage.setItem(i,JSON.stringify(t))}catch(e){console.error("保存用户标签数据失败:",e)}}function f(){return n?new Promise(t=>{setTimeout(()=>{const e=h();t({code:200,data:[...e],message:"获取用户标签列表成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/tag/list",method:"get"})}function b(){return n?new Promise(t=>{setTimeout(()=>{t({code:200,data:{total_users:1256,active_users:1180,leader_users:45,today_users:23,month_users:187,total_balance:125680.5,avg_balance:106.51},message:"获取用户统计成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/stats",method:"get"})}function _(t,e){return n?new Promise(t=>{setTimeout(()=>{t({code:200,data:{before_balance:100,after_balance:"add"===e.type?100+parseFloat(e.amount):100-parseFloat(e.amount)},message:"余额调整成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/balance/"+t,method:"post",data:e})}function v(t){return n?new Promise(t=>{setTimeout(()=>{t({code:200,data:{},message:"批量操作成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/batch",method:"post",data:t})}function w(t){return n?new Promise(t=>{setTimeout(()=>{t({code:200,data:{total_orders:Math.floor(50*Math.random())+10,total_amount:parseFloat((5e3*Math.random()+1e3).toFixed(2)),avg_order_amount:parseFloat((200*Math.random()+50).toFixed(2)),last_order_time:"2023-06-15 14:30:20",favorite_category:"数码产品",monthly_stats:[{month:"2023-01",orders:3,amount:450},{month:"2023-02",orders:5,amount:680.5},{month:"2023-03",orders:2,amount:320},{month:"2023-04",orders:4,amount:590.3},{month:"2023-05",orders:6,amount:780.2},{month:"2023-06",orders:3,amount:420}]},message:"获取消费统计成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/consumption-stats/"+t,method:"get"})}function y(t){return n?new Promise(t=>{setTimeout(()=>{t({code:200,data:{total_wifi:Math.floor(20*Math.random())+5,total_scans:Math.floor(1e3*Math.random())+100,avg_scans_per_wifi:Math.floor(50*Math.random())+10,most_popular_wifi:{id:1,title:"星巴克WiFi",scan_count:156},recent_activity:[{date:"2023-06-15",scans:23},{date:"2023-06-14",scans:18},{date:"2023-06-13",scans:31},{date:"2023-06-12",scans:25},{date:"2023-06-11",scans:19}]},message:"获取WiFi统计成功"})},300)}):Object(r["a"])({url:"/api/v1/admin/user/wifi-stats/"+t,method:"get"})}},ab43:function(t,e,a){"use strict";var r=a("23e7"),n=a("c65b"),i=a("59ed"),s=a("825a"),o=a("46c4"),d=a("c5cc"),u=a("9bdd"),c=a("2a62"),l=a("2baa"),m=a("f99f"),p=a("c430"),h=!p&&!l("map",(function(){})),g=!p&&!h&&m("map",TypeError),f=p||h||g,b=d((function(){var t=this.iterator,e=s(n(this.next,t)),a=this.done=!!e.done;if(!a)return u(t,this.mapper,[e.value,this.counter++],!0)}));r({target:"Iterator",proto:!0,real:!0,forced:f},{map:function(t){s(this);try{i(t)}catch(e){c(this,"throw",e)}return g?n(g,this,t):new b(o(this),{mapper:t})}})},ac98:function(t,e,a){"use strict";a.d(e,"e",(function(){return n})),a.d(e,"d",(function(){return i})),a.d(e,"b",(function(){return s})),a.d(e,"j",(function(){return o})),a.d(e,"c",(function(){return d})),a.d(e,"g",(function(){return u})),a.d(e,"f",(function(){return c})),a.d(e,"a",(function(){return l})),a.d(e,"i",(function(){return m})),a.d(e,"h",(function(){return p}));var r=a("b775");function n(t){return Object(r["a"])({url:"/api/v1/admin/team/list",method:"get",params:t})}function i(t){return Object(r["a"])({url:"/api/v1/admin/team/detail/"+t,method:"get"})}function s(t){return Object(r["a"])({url:"/api/v1/admin/team/create",method:"post",data:t})}function o(t,e){return Object(r["a"])({url:"/api/v1/admin/team/update/"+t,method:"put",data:e})}function d(t){return Object(r["a"])({url:"/api/v1/admin/team/delete/"+t,method:"delete"})}function u(t){return Object(r["a"])({url:"/api/v1/admin/team/stats/"+t,method:"get"})}function c(t,e){return Object(r["a"])({url:`/api/v1/admin/team/${t}/members`,method:"get",params:e})}function l(t,e){return Object(r["a"])({url:`/api/v1/admin/team/${t}/members`,method:"post",data:{user_id:e}})}function m(t,e){return Object(r["a"])({url:`/api/v1/admin/team/${t}/members/${e}`,method:"delete"})}function p(t,e){return m(t,e)}},bd29:function(t,e,a){"use strict";a("46fa")},c466:function(t,e,a){"use strict";function r(t,e="YYYY-MM-DD HH:mm:ss"){if(!t)return"";const a=new Date(t);if(isNaN(a.getTime()))return"";const r=a.getFullYear(),n=String(a.getMonth()+1).padStart(2,"0"),i=String(a.getDate()).padStart(2,"0"),s=String(a.getHours()).padStart(2,"0"),o=String(a.getMinutes()).padStart(2,"0"),d=String(a.getSeconds()).padStart(2,"0");return e.replace("YYYY",r).replace("MM",n).replace("DD",i).replace("HH",s).replace("mm",o).replace("ss",d)}a.d(e,"a",(function(){return r}))},fe24:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"team-members"},[e("el-breadcrumb",{staticClass:"breadcrumb",attrs:{"separator-class":"el-icon-arrow-right"}},[e("el-breadcrumb-item",{attrs:{to:{path:"/dashboard"}}},[t._v("首页")]),e("el-breadcrumb-item",{attrs:{to:{path:"/team/list"}}},[t._v("团队管理")]),e("el-breadcrumb-item",[t._v("团队成员")])],1),e("el-card",{staticClass:"team-info-card"},[e("div",{staticClass:"team-info"},[e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("团队名称：")]),e("span",{staticClass:"value"},[t._v(t._s(t.teamInfo.name))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("团长：")]),e("span",{staticClass:"value"},[t._v(t._s(t.teamInfo.leader_name))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("成员数量：")]),e("span",{staticClass:"value"},[t._v(t._s(t.teamInfo.member_count))])]),e("div",{staticClass:"info-item"},[e("span",{staticClass:"label"},[t._v("创建时间：")]),e("span",{staticClass:"value"},[t._v(t._s(t.formatDate(t.teamInfo.created_at)))])])])]),e("el-card",{staticClass:"member-list-card"},[e("div",{staticClass:"card-header",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("成员列表")]),e("el-button",{attrs:{type:"primary",size:"small",icon:"el-icon-plus"},on:{click:t.showAddMemberDialog}},[t._v(" 添加成员 ")])],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.memberList}},[e("el-table-column",{attrs:{prop:"user_id",label:"用户ID",width:"80"}}),e("el-table-column",{attrs:{label:"头像",width:"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[e("el-avatar",{attrs:{size:40,src:t.row.avatar||"/img/default-avatar.png"}})]}}])}),e("el-table-column",{attrs:{prop:"nickname",label:"昵称","min-width":"120"}}),e("el-table-column",{attrs:{prop:"phone",label:"手机号",width:"120"}}),e("el-table-column",{attrs:{label:"角色",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-tag",{attrs:{type:"leader"===a.row.role?"danger":"info",size:"small"}},[t._v(" "+t._s("leader"===a.row.role?"团长":"成员")+" ")])]}}])}),e("el-table-column",{attrs:{label:"加入时间",width:"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.formatDate(e.row.joined_at))+" ")]}}])}),e("el-table-column",{attrs:{label:"操作",width:"100",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(a){return["leader"!==a.row.role?e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.removeMember(a.row)}}},[t._v(" 移除 ")]):e("span",{staticClass:"no-action"},[t._v("-")])]}}])})],1),e("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.page,limit:t.queryParams.limit},on:{"update:page":function(e){return t.$set(t.queryParams,"page",e)},"update:limit":function(e){return t.$set(t.queryParams,"limit",e)},pagination:t.getMembers}})],1),e("el-dialog",{attrs:{title:"添加团队成员",visible:t.addMemberDialog,width:"500px"},on:{"update:visible":function(e){t.addMemberDialog=e}}},[e("el-form",{ref:"addForm",attrs:{model:t.addForm,rules:t.addRules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"选择用户",prop:"user_id"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择要添加的用户",filterable:"",remote:"","remote-method":t.searchUsers,loading:t.searchLoading},model:{value:t.addForm.user_id,callback:function(e){t.$set(t.addForm,"user_id",e)},expression:"addForm.user_id"}},t._l(t.availableUsers,(function(a){return e("el-option",{key:a.id,attrs:{label:`${a.nickname} (${a.phone})`,value:a.id}},[e("div",{staticClass:"user-option"},[e("el-avatar",{staticStyle:{"margin-right":"10px"},attrs:{size:30,src:a.avatar||"/img/default-avatar.png"}}),e("div",[e("div",[t._v(t._s(a.nickname))]),e("div",{staticStyle:{"font-size":"12px",color:"#999"}},[t._v(t._s(a.phone))])])],1)])})),1)],1)],1),e("div",{attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.addMemberDialog=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.addMember}},[t._v("确定")])],1)],1)],1)},n=[],i=(a("e9f5"),a("910d"),a("ab43"),a("ac98")),s=a("92c2"),o=a("c466"),d=a("333d"),u={name:"TeamMembers",components:{Pagination:d["a"]},data(){return{teamId:null,teamInfo:{},loading:!1,memberList:[],total:0,queryParams:{page:1,limit:10},addMemberDialog:!1,addForm:{user_id:null},addRules:{user_id:[{required:!0,message:"请选择用户",trigger:"change"}]},availableUsers:[],searchLoading:!1}},created(){this.teamId=this.$route.params.id,this.getTeamInfo(),this.getMembers()},methods:{formatDate:o["a"],async getTeamInfo(){try{const t=await Object(i["d"])(this.teamId);this.teamInfo=t.data}catch(t){this.$message.error("获取团队信息失败")}},async getMembers(){this.loading=!0;try{const t=await Object(i["f"])(this.teamId,this.queryParams);this.memberList=t.data.list||[],this.total=t.data.pagination&&t.data.pagination.total||t.data.total||0}catch(t){console.error("获取成员列表失败:",t),this.memberList=[],this.total=0,this.$message.error("获取成员列表失败")}finally{this.loading=!1}},showAddMemberDialog(){this.addMemberDialog=!0,this.addForm.user_id=null,this.searchUsers("")},async searchUsers(t){this.searchLoading=!0;try{const e=await Object(s["g"])({page:1,limit:20,keyword:t}),a=this.memberList.map(t=>t.user_id);this.availableUsers=e.data.list.filter(t=>!a.includes(t.id))}catch(e){this.$message.error("搜索用户失败")}finally{this.searchLoading=!1}},async addMember(){this.$refs.addForm.validate(async t=>{if(t)try{await Object(i["a"])(this.teamId,this.addForm.user_id),this.$message.success("添加成员成功"),this.addMemberDialog=!1,this.getMembers(),this.getTeamInfo()}catch(e){this.$message.error(e.message||"添加成员失败")}})},removeMember(t){this.$confirm(`确定要将 ${t.nickname} 移出团队吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await Object(i["i"])(this.teamId,t.user_id),this.$message.success("移除成员成功"),this.getMembers(),this.getTeamInfo()}catch(e){this.$message.error(e.message||"移除成员失败")}}).catch(()=>{})}}},c=u,l=(a("bd29"),a("2877")),m=Object(l["a"])(c,r,n,!1,null,"75bc04f2",null);e["default"]=m.exports}}]);