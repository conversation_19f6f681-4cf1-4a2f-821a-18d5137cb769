<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.keyword" placeholder="用户昵称/手机号" clearable class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.source_type" placeholder="分润来源" clearable class="filter-item" style="width: 130px">
        <el-option v-for="item in sourceTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        class="filter-item"
        style="width: 240px;"
        @change="handleDateRangeChange"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
    </div>

    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="全部" name="all"></el-tab-pane>
      <el-tab-pane label="WiFi分润" name="wifi"></el-tab-pane>
      <el-tab-pane label="商品分润" name="goods"></el-tab-pane>
      <el-tab-pane label="广告分润" name="ad"></el-tab-pane>
    </el-tabs>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="正在加载..."
      border
      fit
      highlight-current-row
    >
      <el-table-column label="ID" prop="id" align="center" width="80" />
      <el-table-column label="用户信息" align="center" min-width="180">
        <template slot-scope="{row}">
          <div class="user-info">
            <el-avatar :size="32" :src="row.user_avatar">
              <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"/>
            </el-avatar>
            <div class="user-detail">
              <div class="nickname">{{ row.user_nickname }}</div>
              <div class="phone">{{ row.user_phone }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="分润金额" align="center" width="120">
        <template slot-scope="{row}">
          <span class="profit-amount">+{{ row.amount }} 元</span>
        </template>
      </el-table-column>
      <el-table-column label="分润类型" align="center" width="120">
        <template slot-scope="{row}">
          <el-tag :type="getSourceTypeTag(row.source_type)">{{ getSourceTypeLabel(row.source_type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="来源ID" prop="source_id" align="center" width="100" />
      <el-table-column label="备注" prop="remark" align="center" min-width="180" />
      <el-table-column label="时间" prop="created_at" align="center" width="160" />
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleView(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 数据统计卡片 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>分润统计</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">今日分润</div>
            <div class="stat-value">¥ {{ stats.today || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">本周分润</div>
            <div class="stat-value">¥ {{ stats.week || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">本月分润</div>
            <div class="stat-value">¥ {{ stats.month || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-title">累计分润</div>
            <div class="stat-value">¥ {{ stats.total || 0 }}</div>
          </div>
        </el-col>
      </el-row>

      <el-row style="margin-top: 20px;">
        <el-col :span="24">
          <div id="profit-chart" style="width: 100%; height: 300px;"></div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getProfitBillList } from '@/api/profit'
import Pagination from '@/components/Pagination'
// 引入echarts
import * as echarts from 'echarts/lib/echarts'
import 'echarts/lib/chart/line'
import 'echarts/lib/chart/pie'
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/title'
import 'echarts/lib/component/legend'
import 'echarts/lib/component/grid'

export default {
  name: 'ProfitBillList',
  components: { Pagination },
  data () {
    return {
      activeTab: 'all',
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        keyword: undefined,
        source_type: undefined,
        start_date: undefined,
        end_date: undefined
      },
      dateRange: null,
      sourceTypeOptions: [
        { label: 'WiFi分润', value: 1 },
        { label: '商品分润', value: 2 },
        { label: '广告分润', value: 3 }
      ],
      stats: {
        today: 0,
        week: 0,
        month: 0,
        total: 0
      },
      chart: null
    }
  },
  created () {
    this.getList()
  },
  mounted () {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy () {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    getList () {
      this.listLoading = true
      getProfitBillList(this.listQuery).then(response => {
        const { list, total, stats } = response.data
        this.list = list || []
        // 修复分页组件total属性类型错误
        this.total = (response.data.pagination && response.data.pagination.total) || response.data.total || 0
        this.stats = stats || {}
        this.listLoading = false

        // 更新图表
        if (this.chart) {
          this.updateChart(stats.chart_data)
        }
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleFilter () {
      this.listQuery.page = 1
      this.getList()
    },
    handleTabClick () {
      // 根据激活的标签页更新筛选条件
      switch (this.activeTab) {
        case 'wifi':
          this.listQuery.source_type = 1
          break
        case 'goods':
          this.listQuery.source_type = 2
          break
        case 'ad':
          this.listQuery.source_type = 3
          break
        default:
          this.listQuery.source_type = undefined
      }
      this.handleFilter()
    },
    handleDateRangeChange (val) {
      if (val) {
        this.listQuery.start_date = val[0]
        this.listQuery.end_date = val[1]
      } else {
        this.listQuery.start_date = undefined
        this.listQuery.end_date = undefined
      }
    },
    handleView (row) {
      this.$router.push(`/profit/bill/detail/${row.id}`)
    },
    getSourceTypeLabel (type) {
      const map = {
        1: 'WiFi分润',
        2: '商品分润',
        3: '广告分润'
      }
      return map[type] || '未知'
    },
    getSourceTypeTag (type) {
      const map = {
        1: 'primary',
        2: 'success',
        3: 'warning'
      }
      return map[type] || 'info'
    },
    initChart () {
      this.chart = echarts.init(document.getElementById('profit-chart'))

      const option = {
        title: {
          text: '分润趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}: {c} 元'
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          name: '金额(元)'
        },
        series: [
          {
            name: '分润金额',
            type: 'line',
            smooth: true,
            data: [],
            areaStyle: {
              opacity: 0.3
            },
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }

      this.chart.setOption(option)

      window.addEventListener('resize', () => {
        this.chart && this.chart.resize()
      })
    },
    updateChart (chartData) {
      if (!chartData || !this.chart) return

      const dates = chartData.map(item => item.date)
      const values = chartData.map(item => item.amount)

      this.chart.setOption({
        xAxis: {
          data: dates
        },
        series: [
          {
            data: values
          }
        ]
      })
    }
  }
}
</script>

<style scoped>
.user-info {
  display: flex;
  align-items: center;
}
.user-detail {
  margin-left: 10px;
  text-align: left;
}
.nickname {
  font-size: 14px;
}
.phone {
  font-size: 12px;
  color: #909399;
}
.profit-amount {
  color: #67c23a;
  font-weight: bold;
}
.stat-card {
  text-align: center;
  padding: 20px 0;
}
.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}
.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}
</style>
