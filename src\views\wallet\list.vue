<template>
  <div class="wallet-list">
    <div class="filter-container">
      <el-input
        v-model="listQuery.username"
        placeholder="用户名"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.phone"
        placeholder="手机号"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.status" placeholder="状态" clearable style="width: 120px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="用户ID" prop="user_id" sortable="custom" align="center" width="80">
        <template slot-scope="{row}">
          <span>{{ row.user_id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户名" width="150px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.username }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手机号" width="150px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.phone }}</span>
        </template>
      </el-table-column>
      <el-table-column label="钱包余额" width="150px" align="center">
        <template slot-scope="{row}">
          <span class="balance">{{ formatCurrency(row.balance) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="冻结金额" width="150px" align="center">
        <template slot-scope="{row}">
          <span class="frozen">{{ formatCurrency(row.frozen_amount) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="累计收入" width="150px" align="center">
        <template slot-scope="{row}">
          <span class="income">{{ formatCurrency(row.total_income) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="累计支出" width="150px" align="center">
        <template slot-scope="{row}">
          <span class="expense">{{ formatCurrency(row.total_expense) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100px" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '正常' : '冻结' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" width="180px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.updated_at }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="230" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleDetail(row)">
            详情
          </el-button>
          <el-button size="mini" type="success" @click="handleAdjust(row)">
            调整余额
          </el-button>
          <el-button size="mini" type="info" @click="handleTransactions(row)">
            交易记录
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <!-- 调整余额对话框 -->
    <el-dialog title="调整钱包余额" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="100px" style="width: 400px; margin-left:50px;">
        <el-form-item label="用户名">
          <span>{{ temp.username }}</span>
        </el-form-item>
        <el-form-item label="当前余额">
          <span>{{ formatCurrency(temp.balance) }}</span>
        </el-form-item>
        <el-form-item label="调整类型" prop="adjust_type">
          <el-select v-model="temp.adjust_type" class="filter-item" placeholder="请选择调整类型">
            <el-option v-for="item in adjustTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="调整金额" prop="amount">
          <el-input v-model.number="temp.amount" type="number" placeholder="请输入调整金额" />
        </el-form-item>
        <el-form-item label="调整原因" prop="reason">
          <el-input v-model="temp.reason" type="textarea" :rows="3" placeholder="请输入调整原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="adjustBalance">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWalletList, adjustWalletBalance } from '@/api/wallet'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'

export default {
  name: 'WalletList',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        username: undefined,
        phone: undefined,
        status: undefined
      },
      statusOptions: [
        { label: '正常', value: 1 },
        { label: '冻结', value: 0 }
      ],
      adjustTypeOptions: [
        { label: '增加余额', value: 'increase' },
        { label: '减少余额', value: 'decrease' },
        { label: '冻结金额', value: 'freeze' },
        { label: '解冻金额', value: 'unfreeze' }
      ],
      temp: {
        user_id: undefined,
        username: '',
        balance: 0,
        adjust_type: '',
        amount: 0,
        reason: ''
      },
      dialogFormVisible: false,
      rules: {
        adjust_type: [{ required: true, message: '请选择调整类型', trigger: 'change' }],
        amount: [
          { required: true, message: '请输入调整金额', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
        ],
        reason: [{ required: true, message: '请输入调整原因', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getWalletList(this.listQuery).then(response => {
        this.list = response.data.items || []
        // 修复分页组件total属性类型错误
        this.total = (response.data.pagination && response.data.pagination.total) || response.data.total || 0
        this.listLoading = false
      }).catch(error => {
        console.error('获取钱包列表失败:', error)
        this.list = []
        this.total = 0
        this.listLoading = false
        this.$message.error('获取钱包列表失败')
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleDetail(row) {
      this.$router.push(`/wallet/detail/${row.user_id}`)
    },
    handleTransactions(row) {
      this.$router.push(`/wallet/transactions/${row.user_id}`)
    },
    handleAdjust(row) {
      this.temp = {
        user_id: row.user_id,
        username: row.username,
        balance: row.balance,
        adjust_type: '',
        amount: 0,
        reason: ''
      }
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    adjustBalance() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          adjustWalletBalance(this.temp).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '钱包余额调整成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    formatCurrency(value) {
      return '¥ ' + parseFloat(value).toFixed(2)
    }
  }
}
</script>

<style scoped>
.wallet-list {
  padding: 20px;
}

.filter-container {
  padding-bottom: 10px;
}

.filter-item {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 10px;
  margin-right: 10px;
}

.balance {
  font-weight: bold;
  color: #409EFF;
}

.frozen {
  color: #E6A23C;
}

.income {
  color: #67C23A;
}

.expense {
  color: #F56C6C;
}
</style>
