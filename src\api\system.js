import request from '@/utils/request'

// 本地模拟数据，用于开发测试
const useMock = false  // 禁用模拟数据

// 默认的系统配置数据
const defaultSystemConfig = {
  basic: {
    siteName: '华红WIFI共享商业系统',
    siteLogo: 'data:image/svg+xml,%3Csvg width="200" height="60" xmlns="http://www.w3.org/2000/svg"%3E%3Crect width="200" height="60" fill="%234A90E2"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="white" font-size="24" font-weight="bold"%3EWIFI共享%3C/text%3E%3C/svg%3E',
    copyright: '© 2024 华红科技有限公司',
    icp: '京ICP备**********号'
  },
  payment: {
    enableWechat: 1,
    wechatMchId: '**********',
    wechatAppId: 'wx**********abcdef',
    wechatApiKey: '',
    enableAlipay: 1,
    alipayAppId: '202100**********',
    alipayPublicKey: '',
    alipayPrivateKey: ''
  },
  logistics: {
    apiProvider: 'kdniao',
    appId: 'test123456',
    appKey: '',
    feeType: 1,
    defaultFee: 10
  },
  sms: {
    provider: 'aliyun',
    accessKeyId: 'LTAI4xxxxxxxxxxxx',
    accessKeySecret: '',
    signName: '华红科技',
    templates: {
      verifyCode: 'SMS_123456789',
      orderNotify: 'SMS_123456790',
      deliveryNotify: 'SMS_123456791'
    }
  },
  updated_at: '2025-7-7 10:00:00'
}

// 默认的角色数据
const defaultRoles = [
  {
    id: 1,
    name: '超级管理员',
    code: 'super_admin',
    description: '拥有系统所有权限',
    permissions: ['*'],
    status: 1,
    created_at: '2024-01-01 00:00:00',
    updated_at: '2024-01-01 00:00:00'
  },
  {
    id: 2,
    name: '运营管理员',
    code: 'operation_admin',
    description: '负责日常运营管理',
    permissions: ['wifi:*', 'user:*', 'order:*', 'ad:*'],
    status: 1,
    created_at: '2024-01-15 10:00:00',
    updated_at: '2024-12-20 15:00:00'
  }
]

// 模拟账号数据
const defaultMockAccounts = [
  {
    id: 1,
    username: 'admin',
    nickname: '超级管理员',
    email: '<EMAIL>',
    phone: '***********',
    avatar: 'data:image/svg+xml,%3Csvg width="100" height="100" xmlns="http://www.w3.org/2000/svg"%3E%3Ccircle cx="50" cy="50" r="50" fill="%234A90E2"/%3E%3Ctext x="50%25" y="50%25" text-anchor="middle" dy=".3em" fill="white" font-size="40"%3EA%3C/text%3E%3C/svg%3E',
    role_id: 1,
    role_name: '超级管理员',
    status: 1,
    last_login_time: '2025-7-7 14:00:00',
    last_login_ip: '127.0.0.1',
    created_at: '2024-01-01 00:00:00',
    updated_at: '2025-7-7 14:00:00'
  },
  {
    id: 2,
    username: 'operation',
    nickname: '运营小王',
    email: '<EMAIL>',
    phone: '***********',
    avatar: '',
    role_id: 2,
    role_name: '运营管理员',
    status: 1,
    last_login_time: '2025-7-7 09:00:00',
    last_login_ip: '*************',
    created_at: '2024-01-15 10:00:00',
    updated_at: '2025-7-7 09:00:00'
  }
]

// 初始化mockAccounts
let mockAccounts = []
try {
  const stored = localStorage.getItem('wifi_admin_accounts')
  mockAccounts = stored ? JSON.parse(stored) : defaultMockAccounts
  if (!stored) {
    // 如果localStorage中没有数据，则保存默认数据
    localStorage.setItem('wifi_admin_accounts', JSON.stringify(defaultMockAccounts))
  }
} catch (e) {
  console.error('初始化mockAccounts失败:', e)
  mockAccounts = defaultMockAccounts
}

// 模拟操作日志数据
const mockOperationLogs = [
  {
    id: 1,
    user_id: 1,
    username: 'admin',
    module: '系统管理',
    action: '更新系统配置',
    method: 'PUT',
    url: '/api/v1/admin/system/config/update',
    ip: '127.0.0.1',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    params: '{"site_name":"华红WIFI共享商业系统"}',
    result: 'success',
    created_at: '2025-7-7 15:30:00'
  },
  {
    id: 2,
    user_id: 2,
    username: 'operation',
    module: 'WiFi管理',
    action: '创建WiFi热点',
    method: 'POST',
    url: '/api/v1/admin/wifi/create',
    ip: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    params: '{"name":"星巴克-朝阳店","password":"starbucks2024"}',
    result: 'success',
    created_at: '2025-7-7 14:20:00'
  },
  {
    id: 3,
    user_id: 3,
    username: 'finance',
    module: '提现管理',
    action: '审核提现申请',
    method: 'PUT',
    url: '/api/v1/admin/withdraw/audit/1',
    ip: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    params: '{"status":1,"remark":"审核通过"}',
    result: 'success',
    created_at: '2025-7-7 11:00:00'
  }
]

// 保存账号数据
function saveAccounts(accounts) {
  console.log('保存账号数据到localStorage:', accounts)
  localStorage.setItem('wifi_admin_accounts', JSON.stringify(accounts))
  // 清除缓存
  localStorage.removeItem('wifi_admin_accounts_processed')
  localStorage.removeItem('wifi_admin_accounts_cache')
}

// 获取存储的账号数据
function getStoredAccounts() {
  try {
    // 清除可能的缓存
    localStorage.removeItem('wifi_admin_accounts_processed')
    localStorage.removeItem('wifi_admin_accounts_cache')
    
    const stored = localStorage.getItem('wifi_admin_accounts')
    console.log('getStoredAccounts - 从localStorage获取的原始数据:', stored)
    if (stored) {
      const accounts = JSON.parse(stored)
      console.log('getStoredAccounts - 解析后的账号数据:', accounts)
      return accounts
    }
  } catch (e) {
    console.error('getStoredAccounts - 从localStorage获取账号数据失败:', e)
  }
  console.log('getStoredAccounts - 返回默认账号数据')
  return defaultMockAccounts
}

// 获取系统配置
export function getSystemConfig () {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const config = getStoredSystemConfig()
        resolve({
          code: 200,
          data: config,
          message: '获取成功'
        })
      }, 200)
    })
  }

  return request({
    url: '/api/v1/admin/system/config',
    method: 'get'
  })
}

// 更新系统配置
export function updateSystemConfig (data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const config = getStoredSystemConfig()
        
        // 合并更新配置
        if (data.basic) {
          Object.assign(config.basic, data.basic)
        }
        if (data.payment) {
          Object.assign(config.payment, data.payment)
        }
        if (data.logistics) {
          Object.assign(config.logistics, data.logistics)
        }
        if (data.sms) {
          Object.assign(config.sms, data.sms)
          // 确保templates对象存在
          if (data.sms.templates) {
            Object.assign(config.sms.templates, data.sms.templates)
          }
        }
        config.updated_at = new Date().toISOString().replace('T', ' ').slice(0, 19)
        
        // 保存到localStorage
        saveSystemConfig(config)
        
        resolve({
          code: 200,
          message: '更新成功'
        })
      }, 500)
    })
  }

  return request({
    url: '/api/v1/admin/system/config/update',
    method: 'put',
    data
  })
}

// 获取存储的角色数据
function getStoredRoles() {
  const stored = localStorage.getItem('wifi_admin_roles')
  return stored ? JSON.parse(stored) : defaultRoles
}

// 保存角色数据
function saveRoles(roles) {
  localStorage.setItem('wifi_admin_roles', JSON.stringify(roles))
}

// 初始化mockRoles
let mockRoles = getStoredRoles()

// 获取角色列表
export function getRoleList () {
  return new Promise((resolve) => {
    setTimeout(() => {
      const roles = getStoredRoles()
      // 更新全局mockRoles
      mockRoles = roles
      resolve({
        code: 200,
        data: {
          list: roles,
          total: roles.length
        },
        message: '获取成功'
      })
    }, 200)
  })
}

// 创建角色
export function createRole (data) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const roles = getStoredRoles();
      
      // 检查角色名称是否重复
      if (roles.some(role => role.name === data.name)) {
        resolve({
          code: 400,
          message: '角色名称已存在'
        });
        return;
      }
      
      // 检查角色编码是否重复
      if (data.code && roles.some(role => role.code === data.code)) {
        resolve({
          code: 400,
          message: '角色编码已存在'
        });
        return;
      }
      
      const newRole = {
        ...data,
        id: roles.length > 0 ? Math.max(...roles.map(item => item.id)) + 1 : 1,
        created_at: new Date().toISOString().replace('T', ' ').slice(0, 19),
        updated_at: new Date().toISOString().replace('T', ' ').slice(0, 19)
      };
      
      roles.push(newRole);
      saveRoles(roles);
      // 更新全局mockRoles
      mockRoles = roles;
      
      resolve({
        code: 200,
        data: newRole,
        message: '创建成功'
      });
    }, 500);
  });
}

// 更新角色
export function updateRole (id, data) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const roles = getStoredRoles()
      const index = roles.findIndex(item => item.id === parseInt(id))
      if (index > -1) {
        // 不允许修改超级管理员角色的关键信息
        if (id === 1 && (data.code !== roles[index].code || data.status === 0)) {
          reject(new Error('不能修改超级管理员的关键信息'))
          return
        }
        
        roles[index] = {
          ...roles[index],
          ...data,
          updated_at: new Date().toISOString().replace('T', ' ').slice(0, 19)
        }
        saveRoles(roles)
        // 更新全局mockRoles
        mockRoles = roles
        
        resolve({
          code: 200,
          data: roles[index],
          message: '更新成功'
        })
      } else {
        reject(new Error('角色不存在'))
      }
    }, 500)
  })
}

// 删除角色
export function deleteRole (id) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 不允许删除超级管理员角色
      if (parseInt(id) === 1) {
        reject(new Error('超级管理员角色不能删除'))
        return
      }
      
      const roles = getStoredRoles()
      const index = roles.findIndex(item => item.id === parseInt(id))
      if (index > -1) {
        roles.splice(index, 1)
        saveRoles(roles)
        // 更新全局mockRoles
        mockRoles = roles
        resolve({
          code: 200,
          message: '删除成功'
        })
      } else {
        reject(new Error('角色不存在'))
      }
    }, 300)
  })
}

// 获取账号列表
export function getAccountList (query) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取最新的账号数据
        let list = getStoredAccounts()
        console.log('getAccountList获取到的原始账号数据:', list)
        
        // 获取最新的角色数据（直接从localStorage获取，确保最新）
        const roles = getStoredRoles()
        console.log('获取到的角色数据:', roles)
        
        // 更新角色名称
        list = list.map(account => {
          const role = roles.find(r => r.id === account.role_id)
          return {
            ...account,
            role_name: role ? role.name : '未知角色'
          }
        })

        // 根据查询条件过滤
        if (query) {
          if (query.username) {
            list = list.filter(item => item.username.includes(query.username))
          }
          if (query.nickname) {
            list = list.filter(item => item.nickname && item.nickname.includes(query.nickname))
          }
          if (query.role_id) {
            list = list.filter(item => item.role_id === parseInt(query.role_id))
          }
          if (query.status !== undefined && query.status !== '') {
            list = list.filter(item => item.status === parseInt(query.status))
          }
        }

        // 保存处理后的完整列表到localStorage
        localStorage.setItem('wifi_admin_accounts_processed', JSON.stringify(list))
        console.log('getAccountList处理后的账号数据:', list)

        resolve({
          code: 200,
          data: {
            total: list.length,
            list: list
          },
          message: '获取成功'
        })
      }, 200)
    })
  }

  return request({
    url: '/api/v1/admin/system/account/list',
    method: 'get',
    params: query
  })
}

// 创建账号
export function createAccount (data) {
  if (useMock) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 获取最新的账号数据
        const accounts = getStoredAccounts()
        
        // 检查用户名是否已存在
        if (accounts.some(item => item.username === data.username)) {
          reject({
            code: 400,
            message: '用户名已存在'
          })
          return
        }

        // 获取最新的角色数据（直接从localStorage获取，确保最新）
        const roles = getStoredRoles()
        console.log('创建账号时获取的角色数据:', roles)
        
        // 获取角色信息
        const role = roles.find(r => r.id === parseInt(data.role_id))
        if (!role) {
          reject({
            code: 400,
            message: '角色不存在'
          })
          return
        }

        // 创建新账号
        const newAccount = {
          id: accounts.length > 0 ? Math.max(...accounts.map(item => item.id)) + 1 : 1,
          username: data.username,
          nickname: data.real_name || data.username,
          real_name: data.real_name || '',
          email: data.email || '',
          phone: data.phone || '',
          avatar: data.avatar || '',
          role_id: parseInt(data.role_id),
          role_name: role.name,
          status: parseInt(data.status) || 1,
          last_login_time: '',
          last_login_ip: '',
          created_at: new Date().toISOString().replace('T', ' ').slice(0, 19),
          updated_at: new Date().toISOString().replace('T', ' ').slice(0, 19)
        }

        accounts.push(newAccount)
        
        // 保存到localStorage
        saveAccounts(accounts)
        
        // 清除可能的缓存
        localStorage.removeItem('wifi_admin_accounts_processed')
        localStorage.removeItem('wifi_admin_accounts_cache')
        
        resolve({
          code: 200,
          data: newAccount,
          message: '创建成功'
        })
      }, 500)
    })
  }

  return request({
    url: '/api/v1/admin/system/account/create',
    method: 'post',
    data
  })
}

// 更新账号
export function updateAccount (id, data) {
  if (useMock) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 获取最新的账号数据
        const accounts = getStoredAccounts()
        console.log('更新账号 - 原始账号列表:', accounts)
        console.log('更新账号 - ID:', id, '类型:', typeof id)
        console.log('更新账号 - 数据:', data)
        
        // 确保ID是数字类型
        const parsedId = parseInt(id)
        
        // 打印所有账号ID及其类型以便调试
        accounts.forEach(acc => {
          console.log('账号ID:', acc.id, '类型:', typeof acc.id)
        })
        
        const index = accounts.findIndex(item => parseInt(item.id) === parsedId)
        console.log('找到的账号索引:', index)
        
        if (index === -1) {
          console.error('账号不存在, ID:', id)
          reject({
            code: 404,
            message: '账号不存在'
          })
          return
        }

        // 如果修改了用户名，检查是否与其他账号冲突
        if (data.username && data.username !== accounts[index].username) {
          if (accounts.some(item => parseInt(item.id) !== parsedId && item.username === data.username)) {
            reject({
              code: 400,
              message: '用户名已存在'
            })
            return
          }
        }

        // 获取最新的角色数据
        const roles = getStoredRoles()
        
        // 获取角色信息
        const role = roles.find(r => r.id === parseInt(data.role_id))
        if (!role) {
          reject({
            code: 400,
            message: '角色不存在'
          })
          return
        }

        // 更新账号信息
        const updatedAccount = {
          ...accounts[index],
          ...data,
          role_id: parseInt(data.role_id),
          role_name: role.name,
          updated_at: new Date().toISOString().replace('T', ' ').slice(0, 19)
        }
        
        accounts[index] = updatedAccount
        console.log('更新后的账号:', updatedAccount)
        console.log('更新后的账号列表:', accounts)

        // 保存到localStorage
        saveAccounts(accounts)

        resolve({
          code: 200,
          data: updatedAccount,
          message: '更新成功'
        })
      }, 500)
    })
  }

  return request({
    url: `/api/v1/admin/system/account/update/${id}`,
    method: 'put',
    data
  })
}

// 删除账号
export function deleteAccount (id) {
  if (useMock) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        console.log('删除账号 - ID:', id, '类型:', typeof id)
        
        // 确保ID是数字类型
        const parsedId = parseInt(id)
        
        if (parsedId === 1) {
          console.error('尝试删除超级管理员账号')
          reject(new Error('超级管理员账号不能删除'))
          return
        }
        
        // 获取最新的账号数据
        const accounts = getStoredAccounts()
        console.log('删除账号 - 原始账号列表:', accounts)
        
        // 打印所有账号ID及其类型以便调试
        accounts.forEach(acc => {
          console.log('账号ID:', acc.id, '类型:', typeof acc.id)
        })
        
        const index = accounts.findIndex(item => parseInt(item.id) === parsedId)
        console.log('找到的账号索引:', index)
        
        if (index > -1) {
          const deletedAccount = accounts[index]
          console.log('要删除的账号:', deletedAccount)
          
          accounts.splice(index, 1)
          console.log('删除后的账号列表:', accounts)
          
          // 保存到localStorage
          saveAccounts(accounts)
          resolve({
            code: 200,
            message: '删除成功'
          })
        } else {
          console.error('账号不存在, ID:', id)
          reject(new Error('账号不存在'))
        }
      }, 300)
    })
  }

  return request({
    url: `/api/v1/admin/system/account/delete/${id}`,
    method: 'delete'
  })
}

// 获取操作日志
export function getOperationLog (query) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const page = parseInt(query.page) || 1
        const limit = parseInt(query.limit) || 10
        
        // 生成更多模拟日志
        const logs = [...mockOperationLogs]
        for (let i = 4; i <= 20; i++) {
          logs.push({
            id: i,
            user_id: Math.floor(Math.random() * 4) + 1,
            username: ['admin', 'operation', 'finance', 'service01'][Math.floor(Math.random() * 4)],
            module: ['系统管理', 'WiFi管理', '用户管理', '订单管理'][Math.floor(Math.random() * 4)],
            action: ['查看列表', '创建记录', '更新记录', '删除记录'][Math.floor(Math.random() * 4)],
            method: ['GET', 'POST', 'PUT', 'DELETE'][Math.floor(Math.random() * 4)],
            url: '/api/v1/admin/xxx',
            ip: `192.168.1.${100 + Math.floor(Math.random() * 10)}`,
            user_agent: 'Mozilla/5.0',
            params: '{}',
            result: Math.random() > 0.1 ? 'success' : 'error',
            created_at: new Date(Date.now() - i * 3600000).toISOString().replace('T', ' ').slice(0, 19)
          })
        }
        
        // 按时间倒序
        logs.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        
        // 分页
        const total = logs.length
        const start = (page - 1) * limit
        const end = start + limit
        const list = logs.slice(start, end)
        
        resolve({
          code: 200,
          data: {
            list,
            total,
            page,
            limit
          },
          message: '获取成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/log/operation',
    method: 'get',
    params: query
  })
}

// 获取登录日志
export function getLoginLog (query) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const page = parseInt(query.page) || 1
        const limit = parseInt(query.limit) || 10
        
        // 生成模拟登录日志
        const logs = []
        for (let i = 1; i <= 30; i++) {
          logs.push({
            id: i,
            user_id: Math.floor(Math.random() * 4) + 1,
            username: ['admin', 'operation', 'finance', 'service01'][Math.floor(Math.random() * 4)],
            ip: `192.168.1.${100 + Math.floor(Math.random() * 20)}`,
            location: ['北京市', '上海市', '广州市', '深圳市'][Math.floor(Math.random() * 4)],
            browser: ['Chrome', 'Firefox', 'Safari', 'Edge'][Math.floor(Math.random() * 4)],
            os: ['Windows 10', 'macOS', 'Ubuntu', 'Windows 11'][Math.floor(Math.random() * 4)],
            status: Math.random() > 0.2 ? 1 : 0,
            message: Math.random() > 0.2 ? '登录成功' : '密码错误',
            created_at: new Date(Date.now() - i * 7200000).toISOString().replace('T', ' ').slice(0, 19)
          })
        }
        
        // 分页
        const total = logs.length
        const start = (page - 1) * limit
        const end = start + limit
        const list = logs.slice(start, end)
        
        resolve({
          code: 200,
          data: {
            list,
            total,
            page,
            limit
          },
          message: '获取成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/log/login',
    method: 'get',
    params: query
  })
}

// 获取错误日志
export function getErrorLog (query) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const page = parseInt(query.page) || 1
        const limit = parseInt(query.limit) || 10
        
        // 生成模拟错误日志
        const logs = []
        for (let i = 1; i <= 15; i++) {
          const hasStack = Math.random() > 0.3 // 70%的概率有堆栈信息
          const errorMessages = [
            'Connection timeout',
            'Invalid parameter',
            'File not found',
            'Permission denied',
            'Database connection failed',
            'Redis connection refused',
            'API rate limit exceeded',
            'Memory allocation failed'
          ]
          const selectedMessage = errorMessages[Math.floor(Math.random() * errorMessages.length)]
          
          // 生成模拟堆栈信息
          const stackTrace = hasStack ? `Error: ${selectedMessage}
    at Object.<anonymous> (/app/src/api/wifi.js:45:15)
    at Module._compile (internal/modules/cjs/loader.js:1063:30)
    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1092:10)
    at Module.load (internal/modules/cjs/loader.js:928:32)
    at Function.Module._load (internal/modules/cjs/loader.js:769:14)
    at Module.require (internal/modules/cjs/loader.js:952:19)
    at require (internal/modules/cjs/helpers.js:88:18)
    at Object.<anonymous> (/app/src/router/index.js:12:18)
    at Module._compile (internal/modules/cjs/loader.js:1063:30)
    at Object.Module._extensions..js (internal/modules/cjs/loader.js:1092:10)` : null
          
          logs.push({
            id: i,
            level: ['error', 'warning'][Math.floor(Math.random() * 2)],
            module: ['API', 'Database', 'Redis', 'File'][Math.floor(Math.random() * 4)],
            message: selectedMessage,
            stack: stackTrace,
            user_id: Math.floor(Math.random() * 4) + 1,
            username: ['admin', 'operation', 'finance', 'service01'][Math.floor(Math.random() * 4)],
            ip: `192.168.1.${100 + Math.floor(Math.random() * 20)}`,
            url: '/api/v1/admin/xxx',
            created_at: new Date(Date.now() - i * 10800000).toISOString().replace('T', ' ').slice(0, 19)
          })
        }
        
        // 分页
        const total = logs.length
        const start = (page - 1) * limit
        const end = start + limit
        const list = logs.slice(start, end)
        
        resolve({
          code: 200,
          data: {
            list,
            total,
            page,
            limit
          },
          message: '获取成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/log/error',
    method: 'get',
    params: query
  })
}
