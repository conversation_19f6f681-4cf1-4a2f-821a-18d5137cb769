<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="WiFi标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入WiFi标题" />
      </el-form-item>
      <el-form-item label="WiFi名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入WiFi名称" />
      </el-form-item>
      <el-form-item label="WiFi密码" prop="password">
        <el-input v-model="form.password" placeholder="请输入WiFi密码" />
      </el-form-item>
      <el-form-item label="商户名称" prop="merchant_name">
        <el-input v-model="form.merchant_name" placeholder="请输入商户名称" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">{{ isEdit ? '更新' : '创建' }}</el-button>
        <el-button @click="cancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getWifiDetail, createWifi, updateWifi } from '@/api/wifi'

export default {
  name: 'WiFiForm',
  data () {
    return {
      form: {
        title: '',
        name: '',
        password: '',
        merchant_name: '',
        status: 1
      },
      rules: {
        title: [
          { required: true, message: '请输入WiFi标题', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入WiFi名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入WiFi密码', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        merchant_name: [
          { required: true, message: '请输入商户名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ]
      },
      isEdit: false,
      wifiId: null,
      loading: false
    }
  },
  created () {
    if (this.$route.params.id) {
      this.isEdit = true
      const id = this.$route.params.id
      if (!id || isNaN(id)) {
        this.$message.error('无效的WiFi ID')
        this.goBack()
        return
      }
      this.wifiId = parseInt(id)
      this.getDetail()
    }
  },
  methods: {
    getDetail () {
      if (!this.wifiId || isNaN(this.wifiId)) {
        this.$message.error('无效的WiFi ID')
        this.goBack()
        return
      }
      
      this.loading = true
      getWifiDetail(this.wifiId).then(response => {
        this.form = response.data
      }).catch(error => {
        console.error('获取WiFi码详情失败:', error)
        this.$message.error(`获取WiFi码详情失败: ${error.message || '未知错误'}`)
      }).finally(() => {
        this.loading = false
      })
    },
    submitForm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          const submitData = {
            ...this.form,
            status: parseInt(this.form.status)
          }

          console.log('提交数据:', submitData)

          if (this.isEdit) {
            updateWifi(this.wifiId, submitData).then(response => {
              console.log('更新响应:', response)
              this.$message.success('更新成功')
              this.goBack()
            }).catch(error => {
              console.error('更新失败:', error)
              this.$message.error(`更新失败: ${error.message || '未知错误'}`)
            }).finally(() => {
              this.loading = false
            })
          } else {
            createWifi(submitData).then(response => {
              console.log('创建响应:', response)
              this.$message.success('创建成功')
              this.goBack()
            }).catch(error => {
              console.error('创建失败:', error)
              this.$message.error(`创建失败: ${error.message || '未知错误'}`)
            }).finally(() => {
              this.loading = false
            })
          }
        } else {
          return false
        }
      })
    },
    cancel () {
      this.goBack()
    },
    goBack () {
      this.$router.push('/wifi/list')
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
