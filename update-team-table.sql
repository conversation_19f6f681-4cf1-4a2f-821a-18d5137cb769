-- 检查团队表结构
DESCRIBE team;

-- 添加邀请码字段（如果不存在）
ALTER TABLE team ADD COLUMN IF NOT EXISTS invite_code VARCHAR(20) UNIQUE COMMENT '团队邀请码';

-- 添加团队等级字段（如果不存在）
ALTER TABLE team ADD COLUMN IF NOT EXISTS level INT DEFAULT 1 COMMENT '团队等级';

-- 查看现有团队数据
SELECT id, name, invite_code, level FROM team;

-- 为没有邀请码的团队生成邀请码
UPDATE team 
SET invite_code = CONCAT('TEAM', LPAD(id, 6, '0')) 
WHERE invite_code IS NULL OR invite_code = '';

-- 设置默认等级
UPDATE team 
SET level = 1 
WHERE level IS NULL OR level = 0;

-- 查看更新后的数据
SELECT id, name, invite_code, level, created_at FROM team;

-- 验证邀请码唯一性
SELECT invite_code, COUNT(*) as count 
FROM team 
WHERE invite_code IS NOT NULL 
GROUP BY invite_code 
HAVING COUNT(*) > 1;
