<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>基本信息</span>
        </div>
        <el-form-item label="商品标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入商品标题" />
        </el-form-item>
        <el-form-item label="商品分类" prop="category_id">
          <el-select v-model="form.category_id" placeholder="请选择分类" style="width: 100%">
            <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="商品封面" prop="cover">
          <el-upload
            class="avatar-uploader"
            action="/api/v1/admin/upload"
            :show-file-list="false"
            :http-request="customUploadCover"
            :before-upload="beforeUpload">
            <img v-if="form.cover" :src="form.cover" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="商品图片" prop="images">
          <el-upload
            action="/api/v1/admin/upload"
            list-type="picture-card"
            :file-list="fileList"
            :http-request="customUploadImage"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :before-upload="beforeUpload">
            <i class="el-icon-plus"></i>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
        </el-form-item>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>价格与库存</span>
        </div>
        <el-form-item label="售价" prop="price">
          <el-input-number v-model="form.price" :precision="2" :step="0.1" :min="0" />
        </el-form-item>
        <el-form-item label="原价" prop="original_price">
          <el-input-number v-model="form.original_price" :precision="2" :step="0.1" :min="0" />
        </el-form-item>
        <el-form-item label="库存" prop="stock">
          <el-input-number v-model="form.stock" :min="0" />
        </el-form-item>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>商品属性</span>
        </div>
        <el-form-item label="推荐商品" prop="is_recommend">
          <el-switch v-model="form.is_recommend" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="热门商品" prop="is_hot">
          <el-switch v-model="form.is_hot" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="新品" prop="is_new">
          <el-switch v-model="form.is_new" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="上架状态" prop="status">
          <el-switch v-model="form.status" :active-value="1" :inactive-value="0" />
        </el-form-item>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>商品详情</span>
        </div>
        <el-form-item label="商品描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入商品简短描述" />
        </el-form-item>
        <el-form-item label="详情内容" prop="details">
          <div class="editor-container">
            <el-input v-model="form.details" type="textarea" :rows="10" placeholder="请输入商品详细描述" />
          </div>
        </el-form-item>
      </el-card>

      <el-form-item style="margin-top: 20px;">
        <el-button type="primary" @click="submitForm">{{ isEdit ? '更新' : '创建' }}</el-button>
        <el-button @click="cancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getGoodsDetail, createGoods, updateGoods } from '@/api/goods'
import { uploadFile } from '@/api/upload'

export default {
  name: 'GoodsForm',
  data () {
    return {
      isEdit: false,
      goodsId: null,
      form: {
        title: '',
        category_id: undefined,
        cover: '',
        images: [],
        price: 0,
        original_price: 0,
        stock: 0,
        description: '',
        details: '',
        is_recommend: 0,
        is_hot: 0,
        is_new: 0,
        status: 1
      },
      rules: {
        title: [
          { required: true, message: '请输入商品标题', trigger: 'blur' },
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ],
        category_id: [
          { required: true, message: '请选择商品分类', trigger: 'change' }
        ],
        cover: [
          { required: true, message: '请上传商品封面', trigger: 'change' }
        ],
        price: [
          { required: true, message: '请输入售价', trigger: 'blur' }
        ],
        stock: [
          { required: true, message: '请输入库存', trigger: 'blur' }
        ]
      },
      categoryOptions: [
        { label: '数码产品', value: 1 },
        { label: '家居用品', value: 2 },
        { label: '美妆护肤', value: 3 },
        { label: '食品饮料', value: 4 }
      ],
      fileList: [],
      dialogVisible: false,
      dialogImageUrl: ''
    }
  },
  created () {
    if (this.$route.params.id) {
      this.isEdit = true
      this.goodsId = parseInt(this.$route.params.id)
      this.getDetail()
    }
  },
  methods: {
    getDetail () {
      getGoodsDetail(this.goodsId).then(response => {
        this.form = response.data

        // 处理商品图片列表
        if (this.form.images) {
          try {
            const images = typeof this.form.images === 'string' ? JSON.parse(this.form.images) : this.form.images
            this.fileList = images.map((url, index) => {
              return { name: `商品图片${index + 1}`, url }
            })
          } catch (e) {
            console.error('解析商品图片失败', e)
            this.fileList = []
          }
        }
      }).catch(error => {
        console.error('获取商品详情失败:', error)
      })
    },
    submitForm () {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 处理商品图片，将文件列表转为URL数组
          const images = this.fileList.map(file => file.url)
          
          // 创建一个新的表单数据对象，避免直接修改this.form
          const formData = {
            ...this.form,
            name: this.form.title, // 后端使用name字段
            category_id: this.form.category_id, // 确保分类ID正确传递
            images: images, // 直接使用数组，不再转为JSON字符串
            is_recommend: this.form.is_recommend ? 1 : 0, // 确保布尔值转为0/1
            is_hot: this.form.is_hot ? 1 : 0,
            is_new: this.form.is_new ? 1 : 0,
            status: this.form.status ? 1 : 0
          }
          
          // 删除多余的字段，减小请求体积
          delete formData.title // 使用name替代
          
          console.log('提交的表单数据:', formData)
          
          if (this.isEdit) {
            updateGoods(this.goodsId, formData).then(response => {
              this.$message.success('更新成功')
              this.goBack()
            }).catch((error) => {
              console.error('更新失败:', error)
              this.$message.error('更新失败: ' + (error.message || '未知错误'))
            })
          } else {
            createGoods(formData).then(response => {
              this.$message.success('创建成功')
              this.goBack()
            }).catch((error) => {
              console.error('创建失败:', error)
              this.$message.error('创建失败: ' + (error.message || '未知错误'))
            })
          }
        } else {
          return false
        }
      })
    },
    cancel () {
      this.goBack()
    },
    goBack () {
      this.$router.push('/mall/goods')
    },
    beforeUpload (file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt5M = file.size / 1024 / 1024 < 5 // 增加到5MB

      if (!isImage) {
        this.$message.error('上传图片只能是图片格式!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
        return false
      }
      return isImage && isLt5M
    },
    customUploadCover (options) {
      const { file } = options
      uploadFile(file).then(response => {
        // 确保使用相对路径，避免跨域问题
        const url = response.data.url
        // 从URL中提取相对路径部分
        this.form.cover = url.includes('/uploads/') ? url.substring(url.indexOf('/uploads/')) : url
        console.log('封面上传成功，URL:', this.form.cover)
        this.$message.success('封面上传成功')
      }).catch(error => {
        console.error('上传失败:', error)
        this.$message.error('封面上传失败')
      })
    },
    customUploadImage (options) {
      const { file } = options
      uploadFile(file).then(response => {
        // 确保使用相对路径，避免跨域问题
        const url = response.data.url
        // 从URL中提取相对路径部分
        const imageUrl = url.includes('/uploads/') ? url.substring(url.indexOf('/uploads/')) : url
        const fileObj = {
          name: file.name,
          url: imageUrl
        }
        console.log('图片上传成功，URL:', fileObj.url)
        this.fileList.push(fileObj)
        this.$message.success('图片上传成功')
      }).catch(error => {
        console.error('上传失败:', error)
        this.$message.error('图片上传失败')
      })
    },
    handleRemove (file, fileList) {
      this.fileList = fileList
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
}
</script>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.editor-container {
  margin-bottom: 20px;
}
</style>
