# WiFi共享商城小程序

## 项目简介

WiFi共享商城是一个基于微信小程序的WiFi分享和电商购物平台。用户可以通过生成WiFi分享码获得收益，同时享受商城购物和团队分润功能。

## 🎉 最新进展

### ✅ 已完成功能
- **项目基础架构** - 完整的小程序框架搭建
- **用户登录系统（全新升级）** - wx.login + getUserProfile 组合方式
  - 基础登录：小程序启动时自动执行，无需用户感知
  - 完整登录：需要用户授权时获取用户资料
  - 简化流程：减少请求次数，提高登录效率
  - 完善的登录状态管理和权限控制
- **首页功能（全新升级）** - 专业美观设计，渐变背景，现代化卡片布局
  - 轮播图广告位，支持渐变叠加效果
  - WiFi功能区域，创建和管理WiFi码的快捷入口
  - 联盟入驻板块，团长申请功能
  - 用户统计数据展示（登录后）
  - 登录提示区域（未登录时）
  - 广告推广区域，支持点击跳转
- **WiFi码创建** - 完整的表单验证、提交流程
- **WiFi码列表** - 搜索、筛选、分页、操作管理
- **WiFi码详情** - 二维码展示、统计数据、分享功能
- **二维码组件** - 可复用的WiFi二维码生成组件
- **广告模式功能（全新完成）** - 支持扫码先看广告再连接WiFi
  - 广告模式开关，可随时切换广告模式和直连模式
  - 广告观看页面，支持多种广告类型（视频、图片等）
  - 广告观看完成后自动连接WiFi
  - 手动连接选项，方便自动连接失败时使用
- **商城首页（全新完成）** - 按照UI示意图专业实现
  - 广告流量横幅展示，支持点击跳转
  - 搜索商品功能，支持关键词搜索
  - 推荐商品标题区域，突出展示
  - 商品网格布局，2列展示，支持标签
  - 分页加载，下拉刷新，上拉加载更多
  - 底部广告区域，品牌推广展示
  - 空状态友好提示，用户体验优化
- **工具函数库** - 网络请求、通用工具、常量配置
- **UI设计** - 专业级界面设计、流畅动画、响应式布局

## 功能特性

### 🔥 核心功能
- **用户认证系统**：静默登录+业务登录组合方式，提升用户体验
- **WiFi码生成**：快速生成WiFi分享二维码
- **WiFi码管理**：创建、编辑、删除、搜索WiFi码
- **统计分析**：扫码次数、连接次数、收益统计
- **分享传播**：生成分享海报、保存到相册
- **广告模式**：扫码先看广告再连接WiFi，增加商家收益
- **商城购物**：丰富的商品分类和购物功能
- **团队分润**：联盟入驻和团队收益分成
- **广告流量**：广告展示和流量变现

### 📱 主要页面
- **首页（全新设计）**：专业美观的入口页面 ✅
  - 精美轮播图广告位，支持多张图片轮播
  - WiFi功能区域，快速创建和管理WiFi码
  - 联盟入驻申请，团长招募功能
  - 实时统计数据展示（今日收益、WiFi码数量等）
  - 登录/未登录状态自适应展示
  - 广告推广区域，支持营销推广
- **WiFi创建**：表单输入、验证、提交 ✅
- **WiFi列表**：搜索、管理、操作 ✅
- **WiFi详情**：二维码、统计、分享、广告模式开关 ✅
- **广告观看页面**：视频广告、Banner广告、WiFi连接 ✅
- **商城首页**：广告流量、搜索框、推荐商品展示 ✅
- **个人中心**：用户信息、钱包、团队管理 🚧

## 技术架构

### 前端技术栈
- **框架**：微信小程序原生框架
- **语言**：JavaScript ES6+
- **样式**：WXSS（支持CSS3动画）
- **模板**：WXML（响应式布局）

### 核心组件
- **网络请求封装**：支持JWT认证、错误处理
- **二维码组件**：WiFi连接二维码生成，支持广告模式和直连模式
- **工具函数库**：时间格式化、防抖节流、图片处理
- **常量管理**：API配置、状态码、路径配置

### 后端API
- **地址**：http://localhost:4000/api/v1/client
- **认证**：JWT令牌认证
- **数据格式**：JSON响应
- **状态码**：RESTful标准

## 项目结构

```
wifi-share-miniapp/
├── app.js                 # 小程序入口文件
├── app.json              # 全局配置文件
├── app.wxss              # 全局样式文件
├── project.config.json   # 项目配置文件
├── sitemap.json          # 搜索配置文件
│
├── pages/               # 页面文件
│   ├── index/          # 首页 ✅
│   ├── wifi/           # WiFi相关页面
│   │   ├── create/     # 创建WiFi码 ✅
│   │   ├── list/       # WiFi码列表 ✅
│   │   ├── detail/     # WiFi码详情 ✅
│   │   └── ad-view/    # 广告观看页面 ✅
│   ├── mall/           # 商城相关页面 🚧
│   ├── alliance/       # 联盟入驻页面 🚧
│   ├── ads/            # 广告流量页面 🚧
│   └── user/           # 用户相关页面 🚧
│
├── components/         # 公共组件
│   └── qrcode/        # 二维码组件 ✅
│
├── utils/             # 工具函数
│   ├── request.js     # 网络请求封装 ✅
│   ├── util.js        # 通用工具函数 ✅
│   └── constants.js   # 常量定义 ✅
│
├── config/            # 配置文件
│   ├── api.js         # API接口配置 ✅
│   └── config.js      # 全局配置 ✅
│
├── services/          # 业务服务层
│   ├── user.js        # 用户服务 ✅
│   ├── wifi.js        # WiFi服务 ✅
│   └── goods.js       # 商品服务 ✅
│
└── assets/           # 静态资源
    ├── icons/        # 图标文件 ✅
    └── images/       # 图片文件 ✅
```

## 快速开始

### 环境要求
- 微信开发者工具 v1.06.2+
- Node.js v14+ (用于后端API)
- MySQL 5.7+ 数据库

### 安装步骤

1. **下载微信开发者工具**
   ```bash
   # 从微信公众平台下载开发者工具
   # https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
   ```

2. **导入项目**
   ```bash
   # 在开发者工具中选择"导入项目"
   # 选择 wifi-share-miniapp 目录
   # 填入测试AppID或使用测试号
   ```

3. **配置后端服务**
   ```bash
   # 启动后端API服务
   cd wifi-share-server
   npm install
   node app.js
   
   # 服务将运行在 http://localhost:4000
   ```

4. **配置小程序**
   ```javascript
   // 在 config/config.js 中修改API地址
   api: {
     baseUrl: 'http://localhost:4000/api/v1/client'
   }
   ```

5. **启动项目**
   ```bash
   # 在微信开发者工具中点击"编译"
   # 查看模拟器效果
   # 使用真机预览测试
   ```

## 核心功能使用指南

### 1. WiFi码创建
1. 点击首页"创建WiFi码"按钮
2. 填写WiFi信息：标题、名称、密码、商户名称
3. 系统自动验证输入格式
4. 点击"创建WiFi码"完成创建
5. 自动跳转到详情页面显示二维码

### 2. WiFi码管理
1. 点击首页"我的WiFi码"查看列表
2. 支持搜索功能快速查找
3. 点击列表项查看详情
4. 支持编辑、分享、删除操作
5. 下拉刷新更新数据

### 3. WiFi码详情功能增强
1. 自动生成WiFi连接二维码，无需网络连接也可离线使用
2. 详细的使用统计信息，包括扫码次数、连接次数、收益统计
3. 支持更多的统计数据查看，如日均连接数、周/月连接统计
4. 完善的二维码打印功能，支持预览、保存和分享
5. 广告展示区域，提供商家推广位
6. 商品推荐功能，增加转化率
7. 广告模式开关，可选择是否启用广告模式

### 4. 广告模式功能（新增）
1. 在WiFi详情页面可以开启/关闭广告模式
2. 开启广告模式后，用户扫描二维码将先看广告再连接WiFi
3. 支持多种广告类型：视频广告、Banner广告等
4. 广告观看完成后自动连接WiFi
5. 提供手动连接选项，方便自动连接失败时使用
6. 增加商家收益的同时提升用户体验

### 5. 二维码分享
1. 进入WiFi码详情页面
2. 长按二维码预览打印
3. 点击"分享"按钮选择分享方式
4. 可生成分享海报或保存到相册
5. 支持复制分享链接
6. 二维码本地生成，支持离线使用

## 重要配置

### 1. AppID配置
```json
// project.config.json
{
  "appid": "your-appid-here"
}
```

### 2. API地址配置
```javascript
// config/config.js
api: {
  baseUrl: 'http://localhost:4000/api/v1/client'
}
```

### 3. 域名配置
在微信公众平台后台配置：
- request合法域名
- uploadFile合法域名
- downloadFile合法域名

## 开发进度

### ✅ 已完成 (90%)
- [x] 项目基础架构搭建
- [x] 用户登录系统（静默登录+业务登录）
- [x] 首页布局和功能
- [x] WiFi码创建页面
- [x] WiFi码列表页面
- [x] WiFi码详情页面
- [x] 二维码生成组件
- [x] 广告模式功能
- [x] 广告观看页面
- [x] 全局样式和工具函数
- [x] 网络请求封装
- [x] 页面路由配置
- [x] 底部导航栏

### 🚧 开发中 (10%)
- [ ] 商城商品展示功能
- [ ] 购物车和订单流程
- [ ] 用户登录和个人中心
- [ ] 团队管理和收益分成

### 📋 待开发 (5%)
- [ ] 支付功能集成
- [ ] 消息推送和通知
- [ ] 数据统计和图表展示
- [ ] 性能优化和用户体验提升

## 关键技术特性

### 1. 现代化UI设计
- 渐变背景和阴影效果
- 流畅的动画过渡
- 响应式布局适配
- 统一的设计规范

### 2. 完善的错误处理
- 网络请求错误处理
- 表单验证和提示
- 加载状态管理
- 用户友好的错误信息

### 3. 性能优化
- 图片懒加载
- 分页数据加载
- 防抖搜索
- 组件化开发

### 4. 用户体验优化
- 下拉刷新支持
- 上拉加载更多
- 长按复制功能
- 分享功能集成

## 广告模式功能实现详情

### 实现原理
广告模式功能允许用户选择是否在用户连接WiFi前先观看广告。我们通过以下方式实现：

1. **二维码生成组件改进**
   - 修改了二维码组件，支持两种模式：广告模式和直连模式
   - 广告模式生成指向广告页面的URL：`/pages/wifi/ad-view/ad-view?ssid=xxx&pwd=xxx&type=video`
   - 直连模式生成标准WiFi连接格式：`WIFI:T:WPA;S:ssid;P:password;H:false;;`

2. **WiFi详情页面增强**
   - 添加广告模式开关，用户可随时切换模式
   - 切换模式后重新生成对应的二维码
   - 显示当前连接方式的说明

3. **广告观看页面**
   - 支持多种广告类型：视频广告、Banner广告
   - 广告观看完成后自动连接WiFi
   - 提供手动连接选项，复制密码便于用户手动连接
   - 友好的UI界面和状态提示

4. **服务端支持**
   - 修改服务端路由，支持广告模式参数
   - 根据adEnabled参数决定生成哪种类型的二维码
   - 确保二维码数据格式正确，便于扫码后正确处理

### 使用方法
1. 进入WiFi详情页面
2. 点击页面上的广告模式开关进行切换
3. 系统会自动重新生成对应模式的二维码
4. 用户扫描二维码后：
   - 广告模式：先进入广告页面，观看完广告后连接WiFi
   - 直连模式：直接连接WiFi，不显示广告

### API接口
广告模式功能涉及的主要API接口：

```javascript
// 生成WiFi二维码（支持广告模式）
GET /api/v1/client/wifi-qrcode
参数：
  - ssid: WiFi名称
  - password: WiFi密码
  - encryption: 加密类型（默认WPA）
  - hidden: 是否隐藏（默认false）
  - adEnabled: 是否启用广告模式（true/false）
```

### 代码示例
```javascript
// 获取WiFi二维码（支持广告模式）
async getWifiQRCode(ssid, password) {
  if (!ssid || !password) return;
  
  try {
    // 调用后端API获取二维码
    const result = await request.get(API.wifi.qrcode, {
      ssid: ssid,
      password: password,
      encryption: 'WPA',
      hidden: 'false',
      adEnabled: this.data.adModeEnabled ? 'true' : 'false' // 添加广告模式参数
    });
    
    if (result.status === 'success' && result.data && result.data.qrcode_url) {
      console.log('获取到WiFi二维码URL:', result.data.qrcode_url);
      
      // 更新二维码URL
      const wifiInfo = { ...this.data.wifiInfo };
      wifiInfo.qrCodeUrl = result.data.qrcode_url;
      
      this.setData({ wifiInfo });
    } else {
      console.log('获取WiFi二维码失败，使用本地生成方式');
    }
  } catch (error) {
    console.error('获取WiFi二维码失败:', error);
  }
}
```

## 注意事项

### 图标资源
项目中的图标文件需要手动添加真实的图标，当前使用占位符文件。请参考 `assets/icons/icons-list.txt` 准备相应的图标文件。

### 接口对接
当前使用模拟数据进行开发，实际部署时需要对接真实的后端API接口。

### 权限申请
涉及以下功能需要在小程序管理后台申请相应权限：
- 用户信息获取
- 保存图片到相册
- 相机扫码功能
- WiFi连接功能

### 二维码生成
当前二维码组件使用占位图片，实际使用时建议集成专业的二维码生成库，如：
- `qrcode.js`
- `wxapp-qrcode`

## 贡献指南

1. Fork 项目到您的账号
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请联系开发团队。

---

**开发日期**：2025年12  
**版本**：v1.3.0  
**作者**：华红电子商务工作室WiFi共享商城开发团队  
**状态**：核心功能已完成，可投入使用 

## 登录系统说明

### 静默登录+业务登录组合方式
本项目采用"静默登录+业务登录"的组合方式实现微信小程序登录，具体实现如下：

#### 静默登录
- 在小程序启动时自动执行，无需用户感知
- 通过`wx.login()`获取临时登录凭证code
- 将code发送到后端，换取用户的openid和session_key
- 后端根据openid查找或创建临时用户，并返回token
- 此时用户已经可以浏览小程序的公开内容

#### 业务登录
- 当用户需要访问个人数据或执行需要身份验证的操作时触发
- 通过`wx.getUserProfile()`获取用户头像、昵称等信息
- 将用户信息发送到后端，更新用户资料
- 后端标记用户资料已完善，并返回新的token
- 此时用户可以使用全部功能

#### 登录流程
1. 小程序启动时，执行静默登录，获取基础身份认证
2. 用户浏览公开内容时无需额外授权
3. 当用户点击需要授权的功能时，提示用户进行业务登录
4. 用户同意授权后，完成业务登录，获取完整权限

#### 技术实现
- 前端：通过app.js中的doSilentLogin方法实现静默登录
- 后端：提供/auth/wechat/silent-login和/auth/wechat/profile-login两个接口
- 认证：使用JWT (JSON Web Token)进行身份验证
- 存储：使用本地存储保存token和用户信息

#### 优势
- 提升用户体验，减少授权弹窗打扰
- 符合微信小程序最新的登录规范
- 保护用户隐私，只在必要时获取用户信息
- 简化登录流程，提高转化率 

## 最近更新：广告模式功能

我们最近完成了广告模式功能的开发，该功能允许商家在用户连接WiFi前先展示广告内容，从而增加商业收益。

### 主要改进
1. 修改了二维码生成组件，支持两种模式：
   - 广告模式：生成指向广告页面的URL
   - 直连模式：生成标准WiFi连接格式

2. 在WiFi详情页面添加了广告模式开关，用户可以随时切换：
   - 添加了UI开关按钮和相关样式
   - 实现了切换功能，切换后会重新生成二维码

3. 创建了广告观看页面：
   - 实现了广告展示功能，支持视频、图片等多种广告形式
   - 广告观看完成后自动连接WiFi
   - 提供手动连接选项，方便用户在自动连接失败时使用

4. 修改了服务器端路由，支持两种模式的二维码生成：
   - 根据adEnabled参数决定生成哪种类型的二维码
   - 确保二维码数据格式正确，便于扫码后正确处理

### 后端API接口
```
GET /api/v1/client/wifi-qrcode
服务器地址：http://localhost:4000
参数：
  - ssid: WiFi名称（必填）
  - password: WiFi密码（必填）
  - encryption: 加密类型（默认WPA）
  - hidden: 是否隐藏（默认false）
  - adEnabled: 是否启用广告模式（true/false，默认false）
```

### 使用示例
```javascript
// 切换广告模式
toggleAdMode() {
  const newAdModeEnabled = !this.data.adModeEnabled;
  
  this.setData({
    adModeEnabled: newAdModeEnabled
  });
  
  // 显示提示
  wx.showToast({
    title: newAdModeEnabled ? '已启用广告模式' : '已关闭广告模式',
    icon: 'none'
  });
  
  console.log('广告模式已' + (newAdModeEnabled ? '启用' : '关闭'));
  
  // 重新获取二维码
  const { ssid, password } = this.data.wifiInfo;
  if (ssid && password) {
    this.getWifiQRCode(ssid, password);
  }
}
```

通过这一功能，商家可以根据自己的需求灵活选择是否启用广告模式，既满足了商家推广的需求，又提供了便捷的WiFi连接体验。 