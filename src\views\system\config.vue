<template>
  <div class="app-container">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="基础设置" name="basic">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>系统基础设置</span>
          </div>
          <el-form ref="basicForm" :model="basicForm" label-width="120px">
            <el-form-item label="系统名称">
              <el-input v-model="basicForm.siteName" placeholder="请输入系统名称" />
            </el-form-item>
            <el-form-item label="系统Logo">
              <el-upload
                class="logo-uploader"
                action="#"
                :show-file-list="false"
                :http-request="uploadLogo"
                :before-upload="beforeLogoUpload">
                <img v-if="basicForm.siteLogo" :src="basicForm.siteLogo" class="logo">
                <i v-else class="el-icon-plus logo-uploader-icon"></i>
              </el-upload>
              <div class="tip">建议尺寸：200px * 60px</div>
            </el-form-item>
            <el-form-item label="版权信息">
              <el-input v-model="basicForm.copyright" placeholder="请输入版权信息" />
            </el-form-item>
            <el-form-item label="备案号">
              <el-input v-model="basicForm.icp" placeholder="请输入备案号" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveBasicConfig">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="支付设置" name="payment">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>支付方式配置</span>
          </div>
          <el-form ref="paymentForm" :model="paymentForm" label-width="120px">
            <el-form-item label="微信支付">
              <el-switch
                v-model="paymentForm.enableWechat"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
            <el-form-item label="商户ID" v-if="paymentForm.enableWechat === 1">
              <el-input v-model="paymentForm.wechatMchId" placeholder="请输入微信支付商户ID" />
            </el-form-item>
            <el-form-item label="AppID" v-if="paymentForm.enableWechat === 1">
              <el-input v-model="paymentForm.wechatAppId" placeholder="请输入微信支付AppID" />
            </el-form-item>
            <el-form-item label="API密钥" v-if="paymentForm.enableWechat === 1">
              <el-input v-model="paymentForm.wechatApiKey" placeholder="请输入微信支付API密钥" show-password />
            </el-form-item>

            <el-divider content-position="left">支付宝支付</el-divider>
            <el-form-item label="支付宝支付">
              <el-switch
                v-model="paymentForm.enableAlipay"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
              />
            </el-form-item>
            <el-form-item label="AppID" v-if="paymentForm.enableAlipay === 1">
              <el-input v-model="paymentForm.alipayAppId" placeholder="请输入支付宝AppID" />
            </el-form-item>
            <el-form-item label="公钥" v-if="paymentForm.enableAlipay === 1">
              <el-input v-model="paymentForm.alipayPublicKey" placeholder="请输入支付宝公钥" type="textarea" rows="3" />
            </el-form-item>
            <el-form-item label="私钥" v-if="paymentForm.enableAlipay === 1">
              <el-input v-model="paymentForm.alipayPrivateKey" placeholder="请输入支付宝私钥" type="textarea" rows="3" show-password />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="savePaymentConfig">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="物流设置" name="logistics">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>物流配置</span>
          </div>
          <el-form ref="logisticsForm" :model="logisticsForm" label-width="120px">
            <el-form-item label="物流查询接口">
              <el-select v-model="logisticsForm.apiProvider" placeholder="请选择物流查询接口">
                <el-option label="快递鸟" value="kdniao" />
                <el-option label="快递100" value="kd100" />
                <el-option label="阿里云物流" value="aliyun" />
              </el-select>
            </el-form-item>
            <el-form-item label="接口AppID">
              <el-input v-model="logisticsForm.appId" placeholder="请输入接口AppID" />
            </el-form-item>
            <el-form-item label="接口AppKey">
              <el-input v-model="logisticsForm.appKey" placeholder="请输入接口AppKey" show-password />
            </el-form-item>
            <el-form-item label="运费设置">
              <el-radio-group v-model="logisticsForm.feeType">
                <el-radio :label="1">统一运费</el-radio>
                <el-radio :label="2">按地区设置</el-radio>
                <el-radio :label="3">包邮</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="默认运费" v-if="logisticsForm.feeType === 1">
              <el-input-number v-model="logisticsForm.defaultFee" :min="0" :precision="2" :step="0.5" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveLogisticsConfig">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="短信配置" name="sms">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>短信配置</span>
          </div>
          <el-form ref="smsForm" :model="smsForm" label-width="120px">
            <el-form-item label="短信服务商">
              <el-select v-model="smsForm.provider" placeholder="请选择短信服务商">
                <el-option label="阿里云短信" value="aliyun" />
                <el-option label="腾讯云短信" value="tencent" />
              </el-select>
            </el-form-item>
            <el-form-item label="AccessKeyId">
              <el-input v-model="smsForm.accessKeyId" placeholder="请输入AccessKeyId" />
            </el-form-item>
            <el-form-item label="AccessKeySecret">
              <el-input v-model="smsForm.accessKeySecret" placeholder="请输入AccessKeySecret" show-password />
            </el-form-item>
            <el-form-item label="短信签名">
              <el-input v-model="smsForm.signName" placeholder="请输入短信签名" />
            </el-form-item>
            <el-divider content-position="left">短信模板配置</el-divider>
            <el-form-item label="验证码模板ID">
              <el-input v-model="smsForm.templates.verifyCode" placeholder="请输入验证码短信模板ID" />
            </el-form-item>
            <el-form-item label="订单通知模板ID">
              <el-input v-model="smsForm.templates.orderNotify" placeholder="请输入订单通知短信模板ID" />
            </el-form-item>
            <el-form-item label="发货通知模板ID">
              <el-input v-model="smsForm.templates.deliveryNotify" placeholder="请输入发货通知短信模板ID" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveSmsConfig">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getSystemConfig, updateSystemConfig } from '@/api/system'

export default {
  name: 'SystemConfig',
  data () {
    return {
      activeTab: 'basic',
      loading: false,
      basicForm: {
        siteName: 'WiFi共享商业管理系统',
        siteLogo: '',
        copyright: '© 2023 WiFi共享商业管理系统',
        icp: ''
      },
      paymentForm: {
        enableWechat: 0,
        wechatMchId: '',
        wechatAppId: '',
        wechatApiKey: '',
        enableAlipay: 0,
        alipayAppId: '',
        alipayPublicKey: '',
        alipayPrivateKey: ''
      },
      logisticsForm: {
        apiProvider: 'kdniao',
        appId: '',
        appKey: '',
        feeType: 1,
        defaultFee: 10
      },
      smsForm: {
        provider: 'aliyun',
        accessKeyId: '',
        accessKeySecret: '',
        signName: '',
        templates: {
          verifyCode: '',
          orderNotify: '',
          deliveryNotify: ''
        }
      }
    }
  },
  created () {
    this.getConfig()
  },
  methods: {
    getConfig () {
      this.loading = true
      getSystemConfig().then(response => {
        const config = response.data || {}

        // 填充表单数据
        if (config.basic) {
          this.basicForm = { ...this.basicForm, ...config.basic }
        }
        if (config.payment) {
          this.paymentForm = { ...this.paymentForm, ...config.payment }
        }
        if (config.logistics) {
          this.logisticsForm = { ...this.logisticsForm, ...config.logistics }
        }
        if (config.sms) {
          this.smsForm = { ...this.smsForm, ...config.sms }
          // 确保templates对象存在
          if (!this.smsForm.templates) {
            this.smsForm.templates = {
              verifyCode: '',
              orderNotify: '',
              deliveryNotify: ''
            }
          }
        }

        this.loading = false
      }).catch(() => {
        this.$message.error('获取配置失败')
        this.loading = false
      })
    },
    beforeLogoUpload (file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('上传Logo只能是图片格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传Logo大小不能超过 2MB!')
        return false
      }
      return true
    },
    uploadLogo (options) {
      const { file } = options
      // 使用FileReader读取图片并转换为base64
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => {
        this.basicForm.siteLogo = reader.result
        this.$message.success('Logo上传成功')
      }
      reader.onerror = () => {
        this.$message.error('Logo上传失败')
      }
    },
    saveBasicConfig () {
      this.loading = true
      const data = { basic: this.basicForm }
      updateSystemConfig(data).then(response => {
        if (response.code === 200) {
          this.$message.success('基础设置保存成功')
        } else {
          this.$message.error(response.message || '保存失败')
        }
      }).catch(() => {
        this.$message.error('保存失败')
      }).finally(() => {
        this.loading = false
      })
    },
    savePaymentConfig () {
      this.loading = true
      const data = { payment: this.paymentForm }
      updateSystemConfig(data).then(response => {
        if (response.code === 200) {
          this.$message.success('支付设置保存成功')
        } else {
          this.$message.error(response.message || '保存失败')
        }
      }).catch(() => {
        this.$message.error('保存失败')
      }).finally(() => {
        this.loading = false
      })
    },
    saveLogisticsConfig () {
      this.loading = true
      const data = { logistics: this.logisticsForm }
      updateSystemConfig(data).then(response => {
        if (response.code === 200) {
          this.$message.success('物流设置保存成功')
        } else {
          this.$message.error(response.message || '保存失败')
        }
      }).catch(() => {
        this.$message.error('保存失败')
      }).finally(() => {
        this.loading = false
      })
    },
    saveSmsConfig () {
      this.loading = true
      const data = { sms: this.smsForm }
      updateSystemConfig(data).then(response => {
        if (response.code === 200) {
          this.$message.success('短信配置保存成功')
        } else {
          this.$message.error(response.message || '保存失败')
        }
      }).catch(() => {
        this.$message.error('保存失败')
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.box-card {
  margin-bottom: 20px;
}
.logo-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 200px;
  height: 60px;

  &:hover {
    border-color: #409EFF;
  }

  .logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 200px;
  height: 60px;
  line-height: 60px;
  text-align: center;
}
.tip {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}
</style>
