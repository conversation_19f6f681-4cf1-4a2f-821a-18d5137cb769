# 微信小程序静默登录+业务登录实现说明

## 背景介绍

微信小程序登录方式随着微信平台的更新迭代有所变化。早期的 `wx.getUserInfo` API 已被弃用，现在推荐使用 `wx.login` 获取临时登录凭证（code）和 `wx.getUserProfile` 获取用户信息的组合方式。本项目采用"静默登录+业务登录"的组合方式，既提升了用户体验，又符合微信最新的登录规范。

## 登录方案概述

### 静默登录
- **时机**：小程序启动时自动执行，无需用户感知
- **目的**：获取用户基础身份标识（openid），建立基础会话
- **流程**：wx.login获取code → 后端换取openid → 创建/查找用户 → 返回token
- **特点**：无感知，不需要用户授权，不会弹出授权框

### 业务登录
- **时机**：用户需要访问个人数据或执行需要身份验证的操作时
- **目的**：获取用户头像、昵称等信息，完善用户资料
- **流程**：wx.getUserProfile获取用户信息 → 后端更新用户资料 → 返回新token
- **特点**：需要用户主动授权，会弹出授权框

## 详细实现流程

### 一、前端实现

#### 1. 静默登录实现（app.js）

```javascript
// app.js
async doSilentLogin() {
  try {
    // 检查本地是否有token
    const token = wx.getStorageSync(STORAGE_KEYS.TOKEN)
    if (token) {
      // 如果有token，尝试使用token获取用户信息
      this.globalData.token = token
      this.globalData.isLogin = true
      this.globalData.userInfo = wx.getStorageSync(STORAGE_KEYS.USER_INFO) || null
      return
    }

    // 调用微信登录接口获取code
    const loginResult = await new Promise((resolve, reject) => {
      wx.login({
        success: res => resolve(res),
        fail: err => reject(err)
      })
    })

    if (!loginResult.code) {
      console.error('微信登录失败，未获取到code')
      return
    }

    // 使用code进行静默登录
    const res = await userService.silentLogin(loginResult.code)
    
    if (res.status === 'success' && res.data) {
      // 保存登录信息
      wx.setStorageSync(STORAGE_KEYS.TOKEN, res.data.token)
      wx.setStorageSync(STORAGE_KEYS.USER_INFO, res.data.user)
      
      // 更新全局状态
      this.globalData.token = res.data.token
      this.globalData.userInfo = res.data.user
      this.globalData.isLogin = true
      this.globalData.isProfileCompleted = res.data.is_profile_completed
      
      console.log('静默登录成功')
    } else {
      console.error('静默登录失败:', res)
    }
  } catch (error) {
    console.error('静默登录异常:', error)
  }
}
```

#### 2. 业务登录实现（profile.js）

```javascript
// pages/user/profile/profile.js
// 登录
onLogin() {
  // 使用微信登录
  wx.getUserProfile({
    desc: '用于完善用户资料',
    success: (res) => {
      console.log('获取用户信息成功:', res.userInfo)
      
      // 执行业务登录
      this.performProfileLogin(res.userInfo)
    },
    fail: (error) => {
      console.error('获取用户信息失败:', error)
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      })
    }
  })
},

// 执行业务登录
async performProfileLogin(userInfo) {
  try {
    wx.showLoading({ title: '登录中...' })
    
    // 调用业务登录API
    const res = await userService.profileLogin(userInfo)
    
    if (res.status === 'success') {
      // 保存登录信息
      wx.setStorageSync(STORAGE_KEYS.TOKEN, res.data.token)
      wx.setStorageSync(STORAGE_KEYS.USER_INFO, res.data.user)
      
      // 更新全局状态
      app.globalData.token = res.data.token
      app.globalData.userInfo = res.data.user
      app.globalData.isLogin = true
      app.globalData.isProfileCompleted = true
      
      this.setData({
        isLoggedIn: true,
        isProfileCompleted: true,
        userInfo: res.data.user
      })
      
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })
      
      // 加载用户数据
      this.loadUserData()
    } else {
      wx.showToast({
        title: res.message || '登录失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('业务登录失败:', error)
    wx.showToast({
      title: '登录失败',
      icon: 'none'
    })
  } finally {
    wx.hideLoading()
  }
}
```

#### 3. 用户服务接口（user.js）

```javascript
// services/user.js
/**
 * 静默登录（通过wx.login获取code进行登录）
 * @param {String} code 微信登录凭证
 * @returns {Promise} 登录结果
 */
const silentLogin = (code) => {
  return request.post(API.auth.silentLogin, { code })
}

/**
 * 业务登录（完善用户资料）
 * @param {Object} userInfo 用户信息
 * @returns {Promise} 登录结果
 */
const profileLogin = (userInfo) => {
  return request.post(API.auth.profileLogin, { userInfo })
}
```

### 二、后端实现

#### 1. 静默登录接口

```javascript
/**
 * 微信小程序静默登录
 * POST /api/auth/wechat/silent-login
 */
router.post('/wechat/silent-login', async (req, res, next) => {
  try {
    const { code } = req.body;
    
    if (!code) {
      throw new BadRequestError('缺少必要的参数: code');
    }
    
    // 获取微信配置
    const wxConfig = config.wechat || {
      appId: process.env.WX_APP_ID,
      appSecret: process.env.WX_APP_SECRET
    };
    
    let openid, session_key;
    
    try {
      // 调用微信API获取openid和session_key
      const wxApiUrl = `https://api.weixin.qq.com/sns/jscode2session?appid=${wxConfig.appId}&secret=${wxConfig.appSecret}&js_code=${code}&grant_type=authorization_code`;
      const wxResponse = await axios.get(wxApiUrl);
      
      if (wxResponse.data.errcode) {
        logger.error(`微信API调用失败: ${wxResponse.data.errmsg}`);
        return error(res, '微信登录失败', 400);
      }
      
      openid = wxResponse.data.openid;
      session_key = wxResponse.data.session_key;
      
    } catch (wxError) {
      // 在开发环境中使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        logger.warn('使用模拟的openid和session_key');
        openid = `mock_openid_${Date.now()}`;
        session_key = `mock_session_${Date.now()}`;
      } else {
        logger.error(`调用微信API失败: ${wxError.message}`);
        return error(res, '微信登录失败', 500);
      }
    }
    
    // 查找用户
    let user = await db.getOne('SELECT * FROM users WHERE openid = ?', [openid]);
    
    // 如果用户不存在，创建临时用户
    if (!user) {
      const result = await db.query(
        'INSERT INTO users (openid, nickname, avatar, role, status, is_profile_completed, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())',
        [openid, '微信用户', '', 'user', 1, 0]
      );
      
      user = {
        id: result.insertId,
        openid,
        nickname: '微信用户',
        avatar: '',
        role: 'user',
        is_profile_completed: 0
      };
    }
    
    // 生成JWT令牌
    const token = jwt.sign(
      { 
        id: user.id, 
        openid: user.openid, 
        role: user.role,
        session_key: session_key
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    // 保存session_key到数据库
    await db.query(
      'UPDATE users SET session_key = ?, updated_at = NOW() WHERE id = ?',
      [session_key, user.id]
    );
    
    res.json({
      status: 'success',
      data: {
        token,
        is_profile_completed: user.is_profile_completed === 1,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatar: user.avatar,
          openid: user.openid
        }
      }
    });
  } catch (error) {
    logger.error('微信静默登录失败:', error);
    next(error);
  }
});
```

#### 2. 业务登录接口

```javascript
/**
 * 微信小程序业务登录（完善用户资料）
 * POST /api/auth/wechat/profile-login
 */
router.post('/wechat/profile-login', verifyToken, async (req, res, next) => {
  try {
    const { userInfo } = req.body;
    const { id, openid } = req.user;
    
    if (!userInfo) {
      throw new BadRequestError('缺少必要的参数: userInfo');
    }
    
    // 更新用户信息
    await db.query(
      'UPDATE users SET nickname = ?, avatar = ?, gender = ?, is_profile_completed = 1, updated_at = NOW() WHERE id = ?',
      [userInfo.nickName, userInfo.avatarUrl, userInfo.gender, id]
    );
    
    // 获取更新后的用户信息
    const updatedUser = await db.getOne('SELECT * FROM users WHERE id = ?', [id]);
    
    // 生成新的JWT令牌
    const token = jwt.sign(
      { 
        id: updatedUser.id, 
        openid: updatedUser.openid, 
        role: updatedUser.role,
        session_key: updatedUser.session_key
      },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );
    
    res.json({
      status: 'success',
      data: {
        token,
        is_profile_completed: true,
        user: {
          id: updatedUser.id,
          nickname: updatedUser.nickname,
          avatar: updatedUser.avatar,
          role: updatedUser.role,
          phone: updatedUser.phone || ''
        }
      }
    });
  } catch (error) {
    logger.error('微信业务登录失败:', error);
    next(error);
  }
});
```

### 三、数据库设计

在users表中添加了两个关键字段：

1. **session_key**：存储微信返回的会话密钥，用于数据解密
2. **is_profile_completed**：标记用户是否已完善资料（完成业务登录）

```sql
ALTER TABLE users ADD COLUMN session_key VARCHAR(255);
ALTER TABLE users ADD COLUMN is_profile_completed TINYINT(1) NOT NULL DEFAULT 0;
```

## 登录流程图

```
┌─────────────┐     ┌──────────────┐     ┌────────────────┐     ┌────────────────┐
│  小程序启动  │────▶│  wx.login()  │────▶│ /silent-login  │────▶│ 创建/查找用户   │
└─────────────┘     └──────────────┘     └────────────────┘     └───────┬────────┘
                                                                        │
                                                                        ▼
┌─────────────────────┐     ┌────────────────┐     ┌──────────────────────────────┐
│ 用户可浏览公开内容   │◀────│  返回基础token  │◀────│ 生成JWT(包含id,openid,role)  │
└─────────┬───────────┘     └────────────────┘     └──────────────────────────────┘
          │
          │  ┌─────────────────────┐
          └─▶│ 用户点击需授权功能  │
             └──────────┬──────────┘
                        │
                        ▼
┌───────────────────┐     ┌───────────────────┐     ┌────────────────────────┐
│ wx.getUserProfile │────▶│ /profile-login    │────▶│ 更新用户资料           │
└───────────────────┘     └───────────────────┘     └───────────┬────────────┘
                                                                │
                                                                ▼
┌─────────────────────┐     ┌────────────────┐     ┌──────────────────────────────┐
│ 用户可使用全部功能   │◀────│  返回完整token  │◀────│ 生成新JWT(包含完整用户信息)  │
└─────────────────────┘     └────────────────┘     └──────────────────────────────┘
```

## 优势与注意事项

### 优势
1. **提升用户体验**：用户可以无感知浏览公开内容，只在必要时才请求授权
2. **符合微信规范**：遵循微信最新的登录规范和隐私保护要求
3. **灵活的权限控制**：可以根据用户登录状态和资料完善情况提供不同级别的功能
4. **安全性更高**：使用JWT进行身份验证，session_key安全存储

### 注意事项
1. **token过期处理**：需要处理token过期的情况，及时刷新或重新登录
2. **用户拒绝授权**：需要优雅处理用户拒绝授权的情况，提供合理的引导
3. **敏感操作验证**：对于敏感操作，可能需要额外的身份验证
4. **数据安全**：确保用户数据的安全存储和传输

## 常见问题解答

### Q1: 为什么要分为静默登录和业务登录两步？
A1: 这样可以提升用户体验，用户可以先浏览小程序的公开内容，只有在需要使用个人功能时才请求授权，减少用户流失。

### Q2: 静默登录和业务登录的主要区别是什么？
A2: 静默登录只获取用户的openid作为唯一标识，不需要用户授权；业务登录则获取用户的头像、昵称等信息，需要用户主动授权。

### Q3: 如何处理用户拒绝授权的情况？
A3: 当用户拒绝授权时，应该提供友好的提示，并允许用户继续使用不需要授权的功能，同时提供再次授权的入口。

### Q4: 业务登录后，静默登录的token还有用吗？
A4: 业务登录成功后会生成新的token替代静默登录的token，但保留了相同的用户ID和openid，只是增加了用户资料信息。

### Q5: 如何确保token的安全性？
A5: 使用HTTPS传输，设置合理的token过期时间，存储在小程序的本地存储中，并且在敏感操作时进行额外验证。 