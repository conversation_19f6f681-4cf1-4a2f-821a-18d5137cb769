import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

export function getToken () {
  const token = Cookies.get(TokenKey)
  console.log('获取到的token:', token)
  return token
}

export function setToken (token) {
  console.log('设置token:', token)
  return Cookies.set(To<PERSON><PERSON><PERSON>, token, { expires: 7 }) // 设置7天过期
}

export function removeToken () {
  console.log('移除token')
  return Cookies.remove(TokenKey)
}
