(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6d83931a"],{5302:function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"d",(function(){return i})),a.d(e,"e",(function(){return n})),a.d(e,"a",(function(){return o})),a.d(e,"c",(function(){return c}));var s=a("b775");function r(){return Object(s["a"])({url:"/api/v1/admin/platform/overview",method:"get"})}function i(t){return Object(s["a"])({url:"/api/v1/admin/platform/revenue-trend",method:"get",params:t})}function n(t){return Object(s["a"])({url:"/api/v1/admin/platform/user-growth",method:"get",params:t})}function o(){return Object(s["a"])({url:"/api/v1/admin/platform/business-type",method:"get"})}function c(){return Object(s["a"])({url:"/api/v1/admin/platform/region",method:"get"})}},"8ed3":function(t,e,a){},ab43:function(t,e,a){"use strict";var s=a("23e7"),r=a("c65b"),i=a("59ed"),n=a("825a"),o=a("46c4"),c=a("c5cc"),l=a("9bdd"),d=a("2a62"),h=a("2baa"),u=a("f99f"),v=a("c430"),p=!v&&!h("map",(function(){})),f=!v&&!p&&u("map",TypeError),C=v||p||f,m=c((function(){var t=this.iterator,e=n(r(this.next,t)),a=this.done=!!e.done;if(!a)return l(t,this.mapper,[e.value,this.counter++],!0)}));s({target:"Iterator",proto:!0,real:!0,forced:C},{map:function(t){n(this);try{i(t)}catch(e){d(this,"throw",e)}return f?r(f,this,t):new m(o(this),{mapper:t})}})},c6be:function(t,e,a){"use strict";a("8ed3")},f0e7:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"platform-stats-container"},[e("el-card",{staticClass:"stats-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("平台数据概览")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text"},on:{click:t.refreshData}},[e("i",{staticClass:"el-icon-refresh"}),t._v(" 刷新 ")])],1),e("el-row",{staticClass:"overview-cards",attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"overview-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-icon user-icon"},[e("i",{staticClass:"el-icon-user"})]),e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-title"},[t._v("用户总数")]),e("div",{staticClass:"card-value"},[t._v(t._s(t.overview.users.total_users||0))]),e("div",{staticClass:"card-footer"},[e("span",[t._v("今日新增: ")]),e("span",{staticClass:"highlight"},[t._v(t._s(t.overview.users.today_new_users||0))])])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"overview-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-icon wifi-icon"},[e("i",{staticClass:"el-icon-connection"})]),e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-title"},[t._v("WiFi码总数")]),e("div",{staticClass:"card-value"},[t._v(t._s(t.overview.wifi.total_wifi_codes||0))]),e("div",{staticClass:"card-footer"},[e("span",[t._v("今日新增: ")]),e("span",{staticClass:"highlight"},[t._v(t._s(t.overview.wifi.today_new_codes||0))])])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"overview-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-icon order-icon"},[e("i",{staticClass:"el-icon-shopping-cart-full"})]),e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-title"},[t._v("订单总数")]),e("div",{staticClass:"card-value"},[t._v(t._s(t.overview.orders.total_orders||0))]),e("div",{staticClass:"card-footer"},[e("span",[t._v("已支付: ")]),e("span",{staticClass:"highlight"},[t._v(t._s(t.overview.orders.paid_orders||0))])])])])],1),e("el-col",{attrs:{span:6}},[e("el-card",{staticClass:"overview-card",attrs:{shadow:"hover"}},[e("div",{staticClass:"card-icon revenue-icon"},[e("i",{staticClass:"el-icon-money"})]),e("div",{staticClass:"card-content"},[e("div",{staticClass:"card-title"},[t._v("总收益")]),e("div",{staticClass:"card-value"},[t._v("¥"+t._s(t.formatNumber(t.overview.orders.total_revenue||0)))]),e("div",{staticClass:"card-footer"},[e("span",[t._v("今日收益: ")]),e("span",{staticClass:"highlight"},[t._v("¥"+t._s(t.formatNumber(t.overview.orders.today_revenue||0)))])])])])],1)],1),e("el-card",{staticClass:"chart-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("收益趋势")]),e("el-radio-group",{staticStyle:{float:"right"},attrs:{size:"mini"},model:{value:t.revenuePeriod,callback:function(e){t.revenuePeriod=e},expression:"revenuePeriod"}},[e("el-radio-button",{attrs:{label:"7d"}},[t._v("7天")]),e("el-radio-button",{attrs:{label:"30d"}},[t._v("30天")]),e("el-radio-button",{attrs:{label:"90d"}},[t._v("90天")])],1)],1),e("div",{ref:"revenueChart",staticClass:"chart-container"})]),e("el-card",{staticClass:"chart-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("用户增长趋势")]),e("el-radio-group",{staticStyle:{float:"right"},attrs:{size:"mini"},model:{value:t.userPeriod,callback:function(e){t.userPeriod=e},expression:"userPeriod"}},[e("el-radio-button",{attrs:{label:"7d"}},[t._v("7天")]),e("el-radio-button",{attrs:{label:"30d"}},[t._v("30天")]),e("el-radio-button",{attrs:{label:"90d"}},[t._v("90天")])],1)],1),e("div",{ref:"userChart",staticClass:"chart-container"})]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:12}},[e("el-card",{staticClass:"chart-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("业务类型分布")])]),e("div",{ref:"businessChart",staticClass:"chart-container"})])],1),e("el-col",{attrs:{span:12}},[e("el-card",{staticClass:"chart-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("地区分布")])]),e("div",{ref:"regionChart",staticClass:"chart-container"})])],1)],1)],1)],1)},r=[],i=(a("e9f5"),a("ab43"),a("313e")),n=a("5302"),o={name:"PlatformStats",data(){return{loading:!1,overview:{users:{total_users:0,today_new_users:0},wifi:{total_wifi_codes:0,today_new_codes:0},orders:{total_orders:0,paid_orders:0,total_revenue:0,today_revenue:0},teams:{total_teams:0,avg_team_size:0}},revenuePeriod:"7d",userPeriod:"30d",revenueChart:null,userChart:null,businessChart:null,regionChart:null,revenueData:[],userData:[],businessData:[],regionData:[]}},watch:{revenuePeriod(){this.fetchRevenueTrend()},userPeriod(){this.fetchUserGrowth()}},mounted(){this.initData()},methods:{initData(){this.fetchOverview(),this.fetchRevenueTrend(),this.fetchUserGrowth(),this.fetchBusinessTypeStats(),this.fetchRegionStats()},refreshData(){this.initData(),this.$message.success("数据已刷新")},formatNumber(t){return parseFloat(t).toFixed(2)},async fetchOverview(){try{this.loading=!0;const{data:t}=await Object(n["b"])();t&&(this.overview=t)}catch(t){console.error("获取平台概览失败:",t),this.$message.error("获取平台概览失败")}finally{this.loading=!1}},async fetchRevenueTrend(){try{const{data:t}=await Object(n["d"])({period:this.revenuePeriod});this.revenueData=Array.isArray(t)?t:[],this.$nextTick(()=>{this.renderRevenueChart()})}catch(t){console.error("获取收益趋势失败:",t),this.$message.error("获取收益趋势失败"),this.revenueData=[]}},async fetchUserGrowth(){try{const{data:t}=await Object(n["e"])({period:this.userPeriod});this.userData=Array.isArray(t)?t:[],this.$nextTick(()=>{this.renderUserChart()})}catch(t){console.error("获取用户增长趋势失败:",t),this.$message.error("获取用户增长趋势失败"),this.userData=[]}},async fetchBusinessTypeStats(){try{const{data:t}=await Object(n["a"])();this.businessData=Array.isArray(t)?t:[],this.$nextTick(()=>{this.renderBusinessChart()})}catch(t){console.error("获取业务类型统计失败:",t),this.$message.error("获取业务类型统计失败"),this.businessData=[]}},async fetchRegionStats(){try{const{data:t}=await Object(n["c"])();this.regionData=Array.isArray(t)?t:[],this.$nextTick(()=>{this.renderRegionChart()})}catch(t){console.error("获取地区统计失败:",t),this.$message.error("获取地区统计失败"),this.regionData=[]}},renderRevenueChart(){if(!this.$refs.revenueChart)return;if(this.revenueChart||(this.revenueChart=i["a"](this.$refs.revenueChart)),!Array.isArray(this.revenueData)||0===this.revenueData.length){const t={title:{text:"暂无数据",left:"center",top:"center",textStyle:{color:"#999",fontSize:14}}};return void this.revenueChart.setOption(t)}const t=this.revenueData.map(t=>t.date),e=this.revenueData.map(t=>t.platform_amount||0),a=this.revenueData.map(t=>t.leader_amount||0),s=this.revenueData.map(t=>t.user_amount||0),r={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["平台收益","团长收益","用户收益"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:t},yAxis:{type:"value"},series:[{name:"平台收益",type:"line",stack:"Total",data:e},{name:"团长收益",type:"line",stack:"Total",data:a},{name:"用户收益",type:"line",stack:"Total",data:s}]};this.revenueChart.setOption(r)},renderUserChart(){if(!this.$refs.userChart)return;if(this.userChart||(this.userChart=i["a"](this.$refs.userChart)),!Array.isArray(this.userData)||0===this.userData.length){const t={title:{text:"暂无数据",left:"center",top:"center",textStyle:{color:"#999",fontSize:14}}};return void this.userChart.setOption(t)}const t=this.userData.map(t=>t.date),e=this.userData.map(t=>t.new_users||0),a=this.userData.map(t=>t.new_leaders||0),s={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["新增用户","新增团长"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:t},yAxis:{type:"value"},series:[{name:"新增用户",type:"bar",data:e},{name:"新增团长",type:"bar",data:a}]};this.userChart.setOption(s)},renderBusinessChart(){if(!this.$refs.businessChart)return;if(this.businessChart||(this.businessChart=i["a"](this.$refs.businessChart)),!Array.isArray(this.businessData)||0===this.businessData.length){const t={title:{text:"暂无数据",left:"center",top:"center",textStyle:{color:"#999",fontSize:14}}};return void this.businessChart.setOption(t)}const t=this.businessData.map(t=>{const e={wifi_share:"WiFi分享",goods_sale:"商品销售",advertisement:"广告收益"};return e[t.type||t.business_type]||t.name||t.business_type||"未知类型"}),e=this.businessData.map(e=>({value:e.total_revenue||0,name:t[this.businessData.indexOf(e)]})),a={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: ¥{c} ({d}%)"},legend:{orient:"vertical",left:10,data:t},series:[{name:"业务收益",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:e}]};this.businessChart.setOption(a)},renderRegionChart(){if(!this.$refs.regionChart)return;if(this.regionChart||(this.regionChart=i["a"](this.$refs.regionChart)),!Array.isArray(this.regionData)||0===this.regionData.length){const t={title:{text:"暂无数据",left:"center",top:"center",textStyle:{color:"#999",fontSize:14}}};return void this.regionChart.setOption(t)}const t=this.regionData.map(t=>t.region_name||t.region||"未知地区"),e=this.regionData.map(t=>t.total_users||t.user_count||0),a={tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(t){const e=t[0],a=this.regionData[e.dataIndex];return`${e.name}<br/>\n                    用户数量: ${e.value}<br/>\n                    团队数量: ${(null===a||void 0===a?void 0:a.total_teams)||(null===a||void 0===a?void 0:a.team_count)||0}<br/>\n                    总收益: ¥${((null===a||void 0===a?void 0:a.total_revenue)||0).toFixed(2)}`}.bind(this)},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value"},yAxis:{type:"category",data:t},series:[{name:"用户数量",type:"bar",data:e,itemStyle:{color:"#409EFF"}}]};this.regionChart.setOption(a)}}},c=o,l=(a("c6be"),a("2877")),d=Object(l["a"])(c,s,r,!1,null,"5e606dca",null);e["default"]=d.exports}}]);