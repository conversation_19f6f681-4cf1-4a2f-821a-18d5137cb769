(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-028d69b8"],{"13b0":function(t,e,a){},"13d5":function(t,e,a){"use strict";var s=a("23e7"),r=a("d58f").left,i=a("a640"),n=a("1212"),l=a("9adc"),o=!l&&n>79&&n<83,c=o||!i("reduce");s({target:"Array",proto:!0,forced:c},{reduce:function(t){var e=arguments.length;return r(this,t,e,e>1?arguments[1]:void 0)}})},8558:function(t,e,a){"use strict";var s=a("cfe9"),r=a("b5db"),i=a("c6b6"),n=function(t){return r.slice(0,t.length)===t};t.exports=function(){return n("Bun/")?"BUN":n("Cloudflare-Workers")?"CLOUDFLARE":n("Deno/")?"DENO":n("Node.js/")?"NODE":s.Bun&&"string"==typeof Bun.version?"BUN":s.Deno&&"object"==typeof Deno.version?"DENO":"process"===i(s.process)?"NODE":s.window&&s.document?"BROWSER":"REST"}()},9485:function(t,e,a){"use strict";var s=a("23e7"),r=a("2266"),i=a("59ed"),n=a("825a"),l=a("46c4"),o=a("2a62"),c=a("f99f"),u=a("2ba4"),d=a("d039"),p=TypeError,h=d((function(){[].keys().reduce((function(){}),void 0)})),f=!h&&c("reduce",p);s({target:"Iterator",proto:!0,real:!0,forced:h||f},{reduce:function(t){n(this);try{i(t)}catch(d){o(this,"throw",d)}var e=arguments.length<2,a=e?void 0:arguments[1];if(f)return u(f,this,e?[t]:[t,a]);var s=l(this),c=0;if(r(s,(function(s){e?(e=!1,a=s):a=t(a,s,c),c++}),{IS_RECORD:!0}),e)throw new p("Reduce of empty iterator with no initial value");return a}})},"9adc":function(t,e,a){"use strict";var s=a("8558");t.exports="NODE"===s},a640:function(t,e,a){"use strict";var s=a("d039");t.exports=function(t,e){var a=[][t];return!!a&&s((function(){a.call(null,e||function(){return 1},1)}))}},b6df:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{staticClass:"detail-header"},[e("el-page-header",{attrs:{content:(t.userInfo.nickname||"用户")+" 的交易记录"},on:{back:t.goBack}})],1),e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("交易记录")]),e("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text",icon:"el-icon-view"},on:{click:t.viewWalletDetail}},[t._v(" 查看钱包详情 ")])],1),e("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.queryParams,size:"small"}},[e("el-form-item",{attrs:{label:"交易类型"}},[e("el-select",{attrs:{placeholder:"请选择类型",clearable:""},model:{value:t.queryParams.type,callback:function(e){t.$set(t.queryParams,"type",e)},expression:"queryParams.type"}},[e("el-option",{attrs:{label:"收入",value:"income"}}),e("el-option",{attrs:{label:"支出",value:"expense"}})],1)],1),e("el-form-item",{attrs:{label:"业务类型"}},[e("el-select",{attrs:{placeholder:"请选择业务类型",clearable:""},model:{value:t.queryParams.business_type,callback:function(e){t.$set(t.queryParams,"business_type",e)},expression:"queryParams.business_type"}},[e("el-option",{attrs:{label:"WiFi分享",value:"wifi_share"}}),e("el-option",{attrs:{label:"商品销售",value:"goods_sale"}}),e("el-option",{attrs:{label:"广告点击",value:"advertisement"}}),e("el-option",{attrs:{label:"管理员调整",value:"admin_adjust"}})],1)],1),e("el-form-item",{attrs:{label:"交易日期"}},[e("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},on:{change:t.handleDateChange},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),e("el-form-item",[e("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleQuery}},[t._v("查询")]),e("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.transactionList,border:""}},[e("el-table-column",{attrs:{prop:"id",label:"交易ID",width:"80"}}),e("el-table-column",{attrs:{prop:"type",label:"类型",width:"100"},scopedSlots:t._u([{key:"default",fn:function(a){return["income"===a.row.type?e("el-tag",{attrs:{type:"success"}},[t._v("收入")]):e("el-tag",{attrs:{type:"warning"}},[t._v("支出")])]}}])}),e("el-table-column",{attrs:{prop:"amount",label:"金额",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{class:"income"===a.row.type?"income-amount":"expense-amount"},[t._v(" "+t._s("income"===a.row.type?"+":"-")+"¥"+t._s(t.formatNumber(a.row.amount))+" ")])]}}])}),e("el-table-column",{attrs:{prop:"description",label:"描述","min-width":"200"}}),e("el-table-column",{attrs:{prop:"business_type",label:"业务类型",width:"120"},scopedSlots:t._u([{key:"default",fn:function(a){return["wifi_share"===a.row.business_type?e("el-tag",{attrs:{type:"primary"}},[t._v("WiFi分享")]):"goods_sale"===a.row.business_type?e("el-tag",{attrs:{type:"success"}},[t._v("商品销售")]):"advertisement"===a.row.business_type?e("el-tag",{attrs:{type:"warning"}},[t._v("广告点击")]):"admin_adjust"===a.row.business_type?e("el-tag",{attrs:{type:"danger"}},[t._v("管理员调整")]):e("el-tag",{attrs:{type:"info"}},[t._v("其他")])]}}])}),e("el-table-column",{attrs:{prop:"business_id",label:"关联ID",width:"100"}}),e("el-table-column",{attrs:{prop:"created_at",label:"交易时间",width:"160"}})],1),e("div",{staticClass:"pagination-container"},[e("el-pagination",{attrs:{background:"","current-page":t.queryParams.page,"page-sizes":[10,20,50,100],"page-size":t.queryParams.limit,layout:"total, sizes, prev, pager, next, jumper",total:t.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),e("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("交易统计")])]),e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-title"},[t._v("总交易次数")]),e("div",{staticClass:"stat-value"},[t._v(t._s(t.total||0))])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-title"},[t._v("收入总额")]),e("div",{staticClass:"stat-value income"},[t._v("¥"+t._s(t.formatNumber(t.incomeTotal)))])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-title"},[t._v("支出总额")]),e("div",{staticClass:"stat-value expense"},[t._v("¥"+t._s(t.formatNumber(t.expenseTotal)))])])]),e("el-col",{attrs:{span:6}},[e("div",{staticClass:"stat-item"},[e("div",{staticClass:"stat-title"},[t._v("净收益")]),e("div",{staticClass:"stat-value"},[t._v("¥"+t._s(t.formatNumber(t.incomeTotal-t.expenseTotal)))])])])],1)],1)],1)},r=[],i=(a("14d9"),a("13d5"),a("e9f5"),a("910d"),a("9485"),a("b933")),n={name:"WalletTransactions",data(){return{userId:null,userInfo:{},loading:!1,queryParams:{page:1,limit:20,type:"",business_type:"",start_date:"",end_date:""},dateRange:[],transactionList:[],total:0,incomeTotal:0,expenseTotal:0}},created(){this.userId=this.$route.params.userId,this.fetchUserInfo(),this.fetchTransactions()},methods:{async fetchUserInfo(){if(this.userId)try{const{data:t}=await Object(i["d"])(this.userId);this.userInfo=t.user}catch(t){console.error("获取用户信息失败:",t)}},async fetchTransactions(){if(this.userId){this.loading=!0;try{const{data:t}=await Object(i["c"])(this.userId,this.queryParams);this.transactionList=t.list||[],this.total=t.pagination&&t.pagination.total||t.total||0,this.calculateTotals()}catch(t){console.error("获取交易记录失败:",t),this.transactionList=[],this.total=0,this.$message.error("获取交易记录失败")}finally{this.loading=!1}}},calculateTotals(){this.incomeTotal=this.transactionList.filter(t=>"income"===t.type).reduce((t,e)=>t+parseFloat(e.amount||0),0),this.expenseTotal=this.transactionList.filter(t=>"expense"===t.type).reduce((t,e)=>t+parseFloat(e.amount||0),0)},handleDateChange(t){t?(this.queryParams.start_date=t[0],this.queryParams.end_date=t[1]):(this.queryParams.start_date="",this.queryParams.end_date="")},handleQuery(){this.queryParams.page=1,this.fetchTransactions()},resetQuery(){this.dateRange=[],this.queryParams={page:1,limit:20,type:"",business_type:"",start_date:"",end_date:""},this.fetchTransactions()},handleSizeChange(t){this.queryParams.limit=t,this.fetchTransactions()},handleCurrentChange(t){this.queryParams.page=t,this.fetchTransactions()},goBack(){this.$router.push("/wallet/list")},viewWalletDetail(){this.$router.push("/wallet/detail/"+this.userId)},formatNumber(t){return parseFloat(t||0).toFixed(2)}}},l=n,o=(a("fe51"),a("2877")),c=Object(o["a"])(l,s,r,!1,null,"dadba06c",null);e["default"]=c.exports},b933:function(t,e,a){"use strict";a.d(e,"e",(function(){return r})),a.d(e,"d",(function(){return i})),a.d(e,"a",(function(){return n})),a.d(e,"c",(function(){return l})),a.d(e,"b",(function(){return o}));var s=a("b775");function r(t){return Object(s["a"])({url:"/api/v1/admin/wallet/list",method:"get",params:t})}function i(t){return Object(s["a"])({url:"/api/v1/admin/wallet/detail/"+t,method:"get"})}function n(t,e){return Object(s["a"])({url:"/api/v1/admin/wallet/adjust/"+t,method:"post",data:e})}function l(t,e){return Object(s["a"])({url:"/api/v1/admin/wallet/transactions/"+t,method:"get",params:e})}function o(t){return Object(s["a"])({url:"/api/v1/admin/wallet/adjust",method:"post",data:t})}},d58f:function(t,e,a){"use strict";var s=a("59ed"),r=a("7b0b"),i=a("44ad"),n=a("07fa"),l=TypeError,o="Reduce of empty array with no initial value",c=function(t){return function(e,a,c,u){var d=r(e),p=i(d),h=n(d);if(s(a),0===h&&c<2)throw new l(o);var f=t?h-1:0,m=t?-1:1;if(c<2)while(1){if(f in p){u=p[f],f+=m;break}if(f+=m,t?f<0:h<=f)throw new l(o)}for(;t?f>=0:h>f;f+=m)f in p&&(u=a(u,p[f],f,d));return u}};t.exports={left:c(!1),right:c(!0)}},fe51:function(t,e,a){"use strict";a("13b0")}}]);