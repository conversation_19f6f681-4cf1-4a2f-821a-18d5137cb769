(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-63f1def8"],{"50e4":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{staticClass:"filter-container"},[e("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-plus"},on:{click:t.handleCreate}},[t._v("新增标签")])],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{data:t.list,"element-loading-text":"正在加载...",border:"",fit:"","highlight-current-row":""}},[e("el-table-column",{attrs:{label:"ID",prop:"id",align:"center",width:"80"}}),e("el-table-column",{attrs:{label:"标签名称",prop:"name",align:"center","min-width":"120"}}),e("el-table-column",{attrs:{label:"标签描述",prop:"description",align:"center","min-width":"200"}}),e("el-table-column",{attrs:{label:"创建时间",align:"center",width:"160"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("span",[t._v(t._s(a.created_at))])]}}])}),e("el-table-column",{attrs:{label:"操作",align:"center",width:"200","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(e){return t.handleUpdate(a)}}},[t._v("编辑")]),e("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.handleDelete(a)}}},[t._v("删除")])]}}])})],1),e("el-dialog",{attrs:{title:"create"===t.dialogStatus?"创建标签":"编辑标签",visible:t.dialogVisible,width:"500px"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"标签名称",prop:"name"}},[e("el-input",{attrs:{placeholder:"请输入标签名称"},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1),e("el-form-item",{attrs:{label:"标签描述",prop:"description"}},[e("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入标签描述"},model:{value:t.form.description,callback:function(e){t.$set(t.form,"description",e)},expression:"form.description"}})],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.dialogVisible=!1}}},[t._v("取消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确定")])],1)],1)],1)},r=[],n=a("92c2"),o={name:"UserTag",data(){return{list:[],total:0,listLoading:!0,dialogVisible:!1,dialogStatus:"create",form:{id:void 0,name:"",description:""},rules:{name:[{required:!0,message:"请输入标签名称",trigger:"blur"},{max:50,message:"标签名称长度不能超过50个字符",trigger:"blur"}],description:[{max:255,message:"标签描述长度不能超过255个字符",trigger:"blur"}]}}},created(){this.getList()},methods:{getList(){this.listLoading=!0,Object(n["i"])().then(t=>{this.list=t.data||[],this.listLoading=!1}).catch(()=>{this.listLoading=!1,this.$message.error("获取标签列表失败")})},resetForm(){this.form={id:void 0,name:"",description:""}},handleCreate(){this.resetForm(),this.dialogStatus="create",this.dialogVisible=!0,this.$nextTick(()=>{this.$refs.form&&this.$refs.form.clearValidate()})},handleUpdate(t){this.form={id:t.id,name:t.name,description:t.description},this.dialogStatus="update",this.dialogVisible=!0,this.$nextTick(()=>{this.$refs.form&&this.$refs.form.clearValidate()})},handleDelete(t){this.$confirm("确认要删除这个标签吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(n["d"])(t.id).then(t=>{this.$message.success("删除成功"),this.getList()}).catch(()=>{this.$message.error("删除失败")})}).catch(()=>{})},submitForm(){this.$refs.form.validate(t=>{t&&("create"===this.dialogStatus?Object(n["c"])(this.form).then(t=>{this.$message.success("创建成功"),this.dialogVisible=!1,this.getList()}).catch(()=>{this.$message.error("创建失败")}):Object(n["m"])(this.form.id,this.form).then(t=>{this.$message.success("更新成功"),this.dialogVisible=!1,this.getList()}).catch(()=>{this.$message.error("更新失败")}))})}}},s=o,d=a("2877"),c=Object(d["a"])(s,i,r,!1,null,null,null);e["default"]=c.exports},"92c2":function(t,e,a){"use strict";a.d(e,"g",(function(){return s})),a.d(e,"f",(function(){return d})),a.d(e,"k",(function(){return c})),a.d(e,"l",(function(){return l})),a.d(e,"c",(function(){return u})),a.d(e,"m",(function(){return m})),a.d(e,"d",(function(){return h})),a.d(e,"i",(function(){return g})),a.d(e,"h",(function(){return _})),a.d(e,"a",(function(){return b})),a.d(e,"b",(function(){return v})),a.d(e,"e",(function(){return w})),a.d(e,"j",(function(){return x}));a("14d9"),a("e9f5"),a("ab43");var i=a("b775");const r=!1,n="wifi_admin_user_tags",o=[{id:1,name:"VIP用户",description:"重要VIP客户",created_at:"2023-06-10 10:30:45"},{id:2,name:"新用户",description:"注册不满30天的用户",created_at:"2023-06-08 14:20:30"},{id:3,name:"商家",description:"拥有商铺的用户",created_at:"2023-06-05 09:15:00"},{id:4,name:"活跃用户",description:"近30天有登录的用户",created_at:"2023-06-01 16:40:20"}];function s(t){return r?new Promise(e=>{setTimeout(()=>{const a=[],i=10*t.pageSize||100;for(let e=1;e<=Math.min(t.pageSize||10,i);e++){const i=(t.pageNum-1)*(t.pageSize||10),r=i+e;a.push({id:r,openid:"oMKLx5M2xxxxxxxx"+r,nickname:["张三","李四","王五","赵六","刘七"][e%5]||"用户"+r,avatar:"/img/default-avatar.png",gender:e%3,phone:"1380013800"+e,balance:parseFloat((500*Math.random()).toFixed(2)),team_id:e%3===0?1:e%3===1?2:null,is_leader:e%7===0?1:0,level:e%5,status:e%10===0?0:1,created_at:`2023-05-${String(e%30+1).padStart(2,"0")} 10:20:30`,updated_at:`2023-06-${String(e%28+1).padStart(2,"0")} 15:30:45`})}e({code:200,data:{list:a,total:i},message:"获取用户列表成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/list",method:"get",params:t})}function d(t){return r?new Promise(e=>{setTimeout(()=>{const a=parseInt(t),i={id:a,openid:"oMKLx5M2xxxxxxxx"+t,nickname:["","张三","李四","王五","赵六"][a]||"用户"+t,avatar:"/img/default-avatar.png",gender:a%2===0?2:1,phone:"1380013800"+t,balance:parseFloat((500*Math.random()).toFixed(2)),team_id:a<=2?1:3===a?2:null,parent_id:2===a?1:null,is_leader:[1,3].includes(a)?1:0,level:[1,3].includes(a)?2:2===a?1:0,status:4===a?0:1,created_at:`2023-05-0${a>4?"1":a} 10:20:30`,updated_at:`2023-05-0${a>4?"1":a} 15:30:45`};let r=null;i.team_id&&(r={id:i.team_id,name:"团队"+i.team_id,member_count:Math.floor(20*Math.random())+5,wifi_count:Math.floor(10*Math.random())+2,total_profit:parseFloat((5e3*Math.random()).toFixed(2))});const n=[{id:10*a+1,title:i.nickname+"的WiFi-1",name:`Wifi_${a}_1`,use_count:Math.floor(100*Math.random())+10,created_at:"2023-06-01 09:30:00"},{id:10*a+2,title:i.nickname+"的WiFi-2",name:`Wifi_${a}_2`,use_count:Math.floor(100*Math.random())+10,created_at:"2023-06-02 14:20:00"}],o=[{id:100*a+1,order_no:`WF${Date.now()}${a}01`,total_amount:parseFloat((200*Math.random()+50).toFixed(2)),status:Math.floor(4*Math.random())+1,created_at:"2023-06-01 16:45:30"},{id:100*a+2,order_no:`WF${Date.now()}${a}02`,total_amount:parseFloat((200*Math.random()+50).toFixed(2)),status:Math.floor(4*Math.random())+1,created_at:"2023-06-02 10:15:20"}];e({code:200,data:{user_info:i,team_info:r,wifi_list:n,order_list:o},message:"获取用户详情成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/detail/"+t,method:"get"})}function c(t,e){return r?new Promise(t=>{setTimeout(()=>{t({code:200,data:{},message:"更新用户成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/update/"+t,method:"put",data:e})}function l(t,e){return r?new Promise(t=>{setTimeout(()=>{t({code:200,data:{},message:"更新用户状态成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/status/"+t,method:"put",data:e})}function u(t){return r?new Promise(e=>{setTimeout(()=>{const a=p(),i=a.length>0?Math.max(...a.map(t=>t.id))+1:1,r={id:i,name:t.name,description:t.description||"",created_at:(new Date).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")};a.push(r),f(a),e({code:200,data:{id:i},message:"创建用户标签成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/tag/create",method:"post",data:t})}function m(t,e){return r?new Promise(a=>{setTimeout(()=>{const i=p(),r=i.findIndex(e=>e.id===parseInt(t));-1!==r?(i[r].name=e.name,i[r].description=e.description||i[r].description,f(i),a({code:200,message:"更新用户标签成功"})):a({code:404,message:"标签不存在"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/tag/update/"+t,method:"put",data:e})}function h(t){return r?new Promise(e=>{setTimeout(()=>{const a=p(),i=a.findIndex(e=>e.id===parseInt(t));-1!==i?(a.splice(i,1),f(a),e({code:200,message:"删除用户标签成功"})):e({code:404,message:"标签不存在"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/tag/delete/"+t,method:"delete"})}function p(){try{const t=localStorage.getItem(n);return t?JSON.parse(t):o}catch(t){return console.warn("读取用户标签数据失败，使用默认数据:",t),o}}function f(t){try{localStorage.setItem(n,JSON.stringify(t))}catch(e){console.error("保存用户标签数据失败:",e)}}function g(){return r?new Promise(t=>{setTimeout(()=>{const e=p();t({code:200,data:[...e],message:"获取用户标签列表成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/tag/list",method:"get"})}function _(){return r?new Promise(t=>{setTimeout(()=>{t({code:200,data:{total_users:1256,active_users:1180,leader_users:45,today_users:23,month_users:187,total_balance:125680.5,avg_balance:106.51},message:"获取用户统计成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/stats",method:"get"})}function b(t,e){return r?new Promise(t=>{setTimeout(()=>{t({code:200,data:{before_balance:100,after_balance:"add"===e.type?100+parseFloat(e.amount):100-parseFloat(e.amount)},message:"余额调整成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/balance/"+t,method:"post",data:e})}function v(t){return r?new Promise(t=>{setTimeout(()=>{t({code:200,data:{},message:"批量操作成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/batch",method:"post",data:t})}function w(t){return r?new Promise(t=>{setTimeout(()=>{t({code:200,data:{total_orders:Math.floor(50*Math.random())+10,total_amount:parseFloat((5e3*Math.random()+1e3).toFixed(2)),avg_order_amount:parseFloat((200*Math.random()+50).toFixed(2)),last_order_time:"2023-06-15 14:30:20",favorite_category:"数码产品",monthly_stats:[{month:"2023-01",orders:3,amount:450},{month:"2023-02",orders:5,amount:680.5},{month:"2023-03",orders:2,amount:320},{month:"2023-04",orders:4,amount:590.3},{month:"2023-05",orders:6,amount:780.2},{month:"2023-06",orders:3,amount:420}]},message:"获取消费统计成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/consumption-stats/"+t,method:"get"})}function x(t){return r?new Promise(t=>{setTimeout(()=>{t({code:200,data:{total_wifi:Math.floor(20*Math.random())+5,total_scans:Math.floor(1e3*Math.random())+100,avg_scans_per_wifi:Math.floor(50*Math.random())+10,most_popular_wifi:{id:1,title:"星巴克WiFi",scan_count:156},recent_activity:[{date:"2023-06-15",scans:23},{date:"2023-06-14",scans:18},{date:"2023-06-13",scans:31},{date:"2023-06-12",scans:25},{date:"2023-06-11",scans:19}]},message:"获取WiFi统计成功"})},300)}):Object(i["a"])({url:"/api/v1/admin/user/wifi-stats/"+t,method:"get"})}},ab43:function(t,e,a){"use strict";var i=a("23e7"),r=a("c65b"),n=a("59ed"),o=a("825a"),s=a("46c4"),d=a("c5cc"),c=a("9bdd"),l=a("2a62"),u=a("2baa"),m=a("f99f"),h=a("c430"),p=!h&&!u("map",(function(){})),f=!h&&!p&&m("map",TypeError),g=h||p||f,_=d((function(){var t=this.iterator,e=o(r(this.next,t)),a=this.done=!!e.done;if(!a)return c(t,this.mapper,[e.value,this.counter++],!0)}));i({target:"Iterator",proto:!0,real:!0,forced:g},{map:function(t){o(this);try{n(t)}catch(e){l(this,"throw",e)}return f?r(f,this,t):new _(s(this),{mapper:t})}})}}]);