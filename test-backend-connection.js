const axios = require('axios');

async function testBackendConnection() {
  console.log('🔍 测试后端API连接...\n');
  
  const backendURL = 'http://localhost:4000';
  
  try {
    // 1. 测试基础连接
    console.log('1️⃣ 测试基础连接...');
    try {
      const healthResponse = await axios.get(`${backendURL}/`, { timeout: 5000 });
      console.log('✅ 后端服务器连接成功');
      console.log('响应:', healthResponse.data);
    } catch (err) {
      console.log('❌ 后端服务器连接失败:', err.message);
      console.log('请检查后端服务是否在4000端口运行');
      return;
    }
    
    // 2. 测试登录API
    console.log('\n2️⃣ 测试登录API...');
    try {
      const loginResponse = await axios.post(`${backendURL}/api/v1/admin/auth/admin-login`, {
        username: 'mrx0927',
        password: 'hh20250701'
      }, { timeout: 10000 });
      
      console.log('✅ 登录API测试成功');
      console.log('状态码:', loginResponse.status);
      console.log('响应数据:', loginResponse.data);
    } catch (err) {
      console.log('❌ 登录API测试失败');
      if (err.response) {
        console.log('状态码:', err.response.status);
        console.log('错误信息:', err.response.data);
      } else {
        console.log('错误:', err.message);
      }
    }
    
  } catch (error) {
    console.log('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testBackendConnection();
