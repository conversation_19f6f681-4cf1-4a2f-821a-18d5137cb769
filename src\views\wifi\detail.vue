<template>
  <div class="app-container">
    <div v-loading="loading" class="wifi-detail">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>WiFi码详情</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">ID:</span>
              <span class="detail-value">{{ detail.id }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">标题:</span>
              <span class="detail-value">{{ detail.title }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">WiFi名称:</span>
              <span class="detail-value">{{ detail.name }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">WiFi密码:</span>
              <span class="detail-value">{{ detail.password }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">商户名称:</span>
              <span class="detail-value">{{ detail.merchant_name }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">使用次数:</span>
              <span class="detail-value">{{ detail.use_count }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">状态:</span>
              <span class="detail-value">
                <el-tag :type="detail.status === 1 ? 'success' : 'info'">
                  {{ detail.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">创建时间:</span>
              <span class="detail-value">{{ detail.created_at }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <span class="detail-label">更新时间:</span>
              <span class="detail-value">{{ detail.updated_at }}</span>
            </div>
          </el-col>
        </el-row>
        <div class="qrcode-container">
          <div class="detail-label">WiFi二维码:</div>
          <div class="qrcode-wrapper">
            <qrcode-generator
              ref="qrcodeGenerator"
              :wifi-info="wifiInfo"
              :width="200"
              :file-name="'wifi-qrcode-' + detail.id"
              :title="detail.title || 'WiFi二维码'"
              :print-info="{ merchant: detail.merchant_name }"
              @generated="onQrcodeGenerated"
            />
          </div>
          <div v-if="qrcodeGenerationFailed" class="qrcode-error">
            <el-alert
              title="二维码生成失败"
              type="error"
              description="服务器未返回二维码数据，已自动为您生成新的二维码"
              show-icon
              :closable="false"
            />
          </div>
          <div v-if="qrcodeDataUrl" class="qrcode-info">
            <p>此二维码可用于自动连接WiFi，无需手动输入密码</p>
            <p>用户只需用手机相机扫描二维码，即可快速连接网络</p>
          </div>
        </div>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px;">
        <div slot="header" class="clearfix">
          <span>操作</span>
        </div>
        <div class="action-buttons">
          <el-button type="primary" @click="handleEdit">编辑</el-button>
          <el-button v-if="detail.status === 1" type="warning" @click="handleStatusChange(0)">禁用</el-button>
          <el-button v-else type="success" @click="handleStatusChange(1)">启用</el-button>
          <el-button type="danger" @click="handleDelete">删除</el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getWifiDetail, updateWifiStatus, deleteWifi } from '@/api/wifi'
import QrcodeGenerator from '@/components/qrcode/index'

export default {
  name: 'WiFiDetail',
  components: {
    QrcodeGenerator
  },
  data () {
    return {
      loading: true,
      detail: {},
      wifiId: null,
      qrcodeError: false,
      qrcodeDataUrl: '',
      qrcodeGenerationFailed: false
    }
  },
  computed: {
    // 计算WiFi信息对象，用于生成二维码
    wifiInfo() {
      if (!this.detail || !this.detail.name || !this.detail.password) {
        return null
      }
      return {
        ssid: this.detail.name,
        password: this.detail.password,
        hidden: false
      }
    }
  },
  created () {
    // 获取路由参数中的ID，并进行有效性检查
    const id = this.$route.params.id
    if (!id || isNaN(id)) {
      this.$message.error('无效的WiFi ID')
      this.goBack()
      return
    }
    this.wifiId = parseInt(id)
    this.fetchData()
  },
  methods: {
    fetchData () {
      if (!this.wifiId || isNaN(this.wifiId)) {
        this.$message.error('无效的WiFi ID')
        this.goBack()
        return
      }
      
      this.loading = true
      this.qrcodeError = false
      this.qrcodeGenerationFailed = false
      
      getWifiDetail(this.wifiId)
        .then(response => {
          console.log('WiFi详情API返回数据:', response)
          
          // 检查并解析数据
          if (response) {
            if (response.status === 'success' && response.data) {
              // 后端返回标准格式 {status, data, message}
              this.detail = response.data
            } else if (response.code === 200 && response.data) {
              // 前端mock数据格式 {code, data, message}
              this.detail = response.data
            } else {
              console.error('WiFi详情数据结构异常:', response)
              this.$message.warning('获取WiFi详情数据格式错误，尝试使用本地数据')
              // 创建默认数据
              this.createDefaultData()
            }
            console.log('最终设置的WiFi详情数据:', this.detail)
          } else {
            console.error('WiFi详情返回空数据')
            this.$message.warning('获取WiFi详情失败，使用本地数据')
            this.createDefaultData()
          }
          
          // 检查二维码数据
          if (!this.detail.qrcode) {
            console.log('服务器未返回二维码数据，将自动生成')
            this.qrcodeGenerationFailed = true
          }
        })
        .catch(error => {
          console.error('获取WiFi详情失败:', error)
          this.$message.warning('获取WiFi详情失败，使用本地数据')
          this.createDefaultData()
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 创建默认数据（当API失败时）
    createDefaultData() {
      this.detail = {
        id: this.wifiId,
        title: `WiFi-${this.wifiId}`,
        name: 'WiFi-SSID',
        password: '12345678',
        merchant_name: '默认商户',
        qrcode: '',
        use_count: 0,
        user_id: 0,
        status: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      this.qrcodeGenerationFailed = true
    },
    // 二维码生成成功回调
    onQrcodeGenerated(dataUrl) {
      this.qrcodeDataUrl = dataUrl
      console.log('二维码生成成功，数据长度:', dataUrl.length)
      
      // 如果服务器没有保存二维码，可以在这里上传
      if (this.qrcodeGenerationFailed && this.detail.id) {
        // 这里可以添加上传二维码到服务器的代码
        console.log('可以将生成的二维码上传到服务器')
      }
    },
    handleEdit () {
      this.$router.push(`/wifi/edit/${this.wifiId}`)
    },
    handleStatusChange (status) {
      updateWifiStatus(this.wifiId, { status }).then(response => {
        this.$message.success('状态更新成功')
        this.detail.status = status
      }).catch(() => {
        this.$message.error('状态更新失败')
      })
    },
    handleDelete () {
      this.$confirm('确认要删除这个WiFi码吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteWifi(this.wifiId).then(response => {
          this.$message.success('删除成功')
          this.goBack()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {
        // 取消删除
      })
    },
    goBack () {
      this.$router.push('/wifi/list')
    }
  }
}
</script>

<style scoped>
.wifi-detail {
  margin-bottom: 20px;
}
.detail-item {
  margin-bottom: 15px;
}
.detail-label {
  font-weight: bold;
  margin-right: 10px;
}
.detail-value {
  color: #606266;
}
.qrcode-container {
  margin-top: 20px;
  text-align: center;
}
.qrcode-wrapper {
  position: relative;
  display: inline-block;
  margin: 10px 0;
}
.qrcode-error {
  margin-top: 10px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}
.qrcode-info {
  margin-top: 15px;
  color: #909399;
  font-size: 14px;
}
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>
