// app.js
const userService = require('./services/user')
const config = require('./config/config')
const { STORAGE_KEYS } = require('./utils/constants')

App({
  /**
   * 当小程序初始化完成时，会触发 onLaunch（全局只触发一次）
   */
  onLaunch: function () {
    console.log('小程序启动')
    
    // 初始化全局数据
    this.globalData = {
      userInfo: null,
      isLoggedIn: false,
      token: wx.getStorageSync('token') || '',
      tempOrderGoods: [], // 初始化临时订单商品数组
      imageBaseUrl: 'https://example.com/images' // 图片基础URL
    }
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 初始化API基础URL
    this.globalData.apiBase = `${config.api.baseUrl}${config.api.clientPath}`;
    // 设置图片服务器基础URL
    this.globalData.baseUrl = config.api.baseUrl;
    this.globalData.imageBaseUrl = config.imageServer.baseUrl;
    
    console.log('API基础URL初始化完成:', this.globalData.apiBase);
    console.log('图片服务器URL初始化完成:', this.globalData.imageBaseUrl);
    
    // 获取系统信息
    this.getSystemInfo()
    
    // 初始化购物车数据
    this.initCartData()
    
    // 启用自动静默登录，让已注册用户可以直接登录
    this.silentLogin()
  },

  /**
   * 当小程序启动，或从后台进入前台显示，会触发 onShow
   */
  onShow: function (options) {
    console.log('小程序显示')
    // 更新购物车数据
    this.updateCartBadge()
  },

  /**
   * 当小程序从前台进入后台，会触发 onHide
   */
  onHide: function () {
    console.log('小程序隐藏')
  },

  /**
   * 当小程序发生脚本错误，或者 api 调用失败时，会触发 onError 并带上错误信息
   */
  onError: function (msg) {
    console.error('小程序错误：', msg)
  },

  /**
   * 执行微信登录
   */
  async doLogin() {
    try {
      // 检查本地是否有token
      const token = wx.getStorageSync(STORAGE_KEYS.TOKEN)
      if (token) {
        // 如果有token，尝试使用token获取用户信息
        this.globalData.token = token
        this.globalData.isLogin = true
        this.globalData.isLoggedIn = true
        this.globalData.userInfo = wx.getStorageSync(STORAGE_KEYS.USER_INFO) || null
        return
      }

      // 调用微信登录接口获取code
      const loginResult = await new Promise((resolve, reject) => {
        wx.login({
          success: res => resolve(res),
          fail: err => reject(err)
        })
      })

      if (!loginResult.code) {
        console.error('微信登录失败，未获取到code')
        return
      }

      console.log('获取到微信登录凭证:', loginResult.code)

      try {
        // 使用code进行登录（不带用户信息）
        const res = await userService.wechatLogin(loginResult.code)
        
        if (res.status === 'success' && res.data) {
          // 保存登录信息
          wx.setStorageSync(STORAGE_KEYS.TOKEN, res.data.token)
          
          // 更新全局状态
          this.globalData.token = res.data.token
          this.globalData.isLogin = true
          this.globalData.isLoggedIn = true
          
          // 如果有用户信息，保存用户信息
          if (res.data.user) {
            wx.setStorageSync(STORAGE_KEYS.USER_INFO, res.data.user)
            this.globalData.userInfo = res.data.user
            
            // 用户资料是否完整取决于是否有昵称和头像
            const hasNickname = !!(res.data.user.nickname && res.data.user.nickname !== '微信用户')
            const hasAvatar = !!(res.data.user.avatar)
            this.globalData.isProfileCompleted = hasNickname && hasAvatar
            
            // 检查是否为降级用户（拒绝授权）
            this.globalData.isDemote = res.data.user.is_demote === true
          } else {
            this.globalData.isProfileCompleted = false
            this.globalData.isDemote = false
          }
          
          console.log('微信登录成功')
          return res
        } else {
          console.error('微信登录失败，服务器返回:', res)
        }
      } catch (apiError) {
        console.error('调用登录API失败:', apiError)
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none',
          duration: 2000
        })
      }
    } catch (error) {
      console.error('微信登录异常:', error)
    }
  },
  
  /**
   * 静默登录，不需要用户授权，但可以获取用户的基本信息
   * @param {Boolean} isDemote 是否为降级登录（用户拒绝授权）
   * @returns {Promise} 登录结果
   */
  async silentLogin(isDemote = false) {
    try {
      // 如果已经登录且有用户信息，不需要再次登录
      if (this.globalData.isLogin && this.globalData.userInfo) {
        console.log('用户已完整登录，无需静默登录')
        return {
          status: 'success',
          data: {
            token: this.globalData.token,
            user: this.globalData.userInfo
          }
        }
      }
      
      console.log('尝试静默登录...')
      
      // 调用微信登录接口获取code
      const loginResult = await new Promise((resolve, reject) => {
        wx.login({
          success: res => resolve(res),
          fail: err => reject(err)
        })
      })

      if (!loginResult.code) {
        console.error('微信登录失败，未获取到code')
        return Promise.reject(new Error('微信登录失败，未获取到code'))
      }

      console.log('获取到微信登录凭证:', loginResult.code)
      
      // 调用后端登录接口
      const loginParams = {
        code: loginResult.code
      }
      
      // 如果是降级登录，添加标记
      if (isDemote) {
        loginParams.isDemote = true;
        console.log('标记为降级登录，用户拒绝授权')
      }
      
      const res = await userService.wechatLogin(loginResult.code, null, isDemote)
      
      if (res.status === 'success' && res.data) {
        // 保存登录信息
        wx.setStorageSync(STORAGE_KEYS.TOKEN, res.data.token)
        
        // 更新全局状态
        this.globalData.token = res.data.token
        this.globalData.isLogin = true
        this.globalData.isLoggedIn = true
        
        // 如果有用户信息，保存用户信息
        if (res.data.user) {
          wx.setStorageSync(STORAGE_KEYS.USER_INFO, res.data.user)
          this.globalData.userInfo = res.data.user
          
          // 使用后端返回的资料完整性标记
          const isProfileCompleted = res.data.user.isProfileCompleted === true;
          const isDemote = res.data.user.is_demote === true;
          
          this.globalData.isProfileCompleted = isProfileCompleted;
          this.globalData.isDemote = isDemote;
          
          console.log('静默登录成功，用户信息:', 
            JSON.stringify(this.globalData.userInfo), 
            '资料完整:', isProfileCompleted, 
            '降级用户:', isDemote);
          
          // 通知页面登录状态变化
          if (this.loginStateCallback) {
            this.loginStateCallback(true)
          }
        } else {
          this.globalData.isProfileCompleted = false
          this.globalData.isDemote = isDemote
          
          console.log('静默登录成功，但未获取到用户信息，需要用户主动授权')
          
          // 提示用户需要授权获取信息
          if (!isDemote) {
            wx.showModal({
              title: '提示',
              content: '为了提供更好的服务，请点击"我的"页面的登录按钮，授权获取您的微信头像和昵称',
              showCancel: false,
              confirmText: '我知道了'
            })
          }
        }
        
        return res
      } else {
        console.log('静默登录失败，需要用户手动登录')
        return Promise.reject(new Error('静默登录失败'))
      }
    } catch (error) {
      console.error('静默登录失败:', error)
      return Promise.reject(error)
    }
  },

  /**
   * 执行完整登录（包含用户信息）
   * @param {Object} userInfo 用户信息
   * @returns {Promise} 登录结果
   */
  async doLoginWithUserInfo(userInfo) {
    try {
      // 调用微信登录接口获取code
      const loginResult = await new Promise((resolve, reject) => {
        wx.login({
          success: res => resolve(res),
          fail: err => reject(err)
        })
      })

      if (!loginResult.code) {
        console.error('微信登录失败，未获取到code')
        return Promise.reject(new Error('微信登录失败，未获取到code'))
      }

      console.log('获取到微信登录凭证:', loginResult.code)
      console.log('获取到用户信息:', JSON.stringify(userInfo))

      // 使用code和用户信息进行完整登录
      try {
        // 确保userInfo包含所有必要字段
        const userInfoToSend = {
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          gender: userInfo.gender,
          country: userInfo.country || '',
          province: userInfo.province || '',
          city: userInfo.city || ''
        };
        
        console.log('准备发送到后端的用户信息:', JSON.stringify(userInfoToSend));
        
        const res = await userService.wechatLogin(loginResult.code, userInfoToSend)
        
        if (res.status === 'success' && res.data) {
          // 保存登录信息
          wx.setStorageSync(STORAGE_KEYS.TOKEN, res.data.token)
          
          // 确保用户数据中的头像是有效的
          const userData = res.data.user;
          if (userData) {
            // 确保头像URL是完整的
            if (!userData.avatar && userInfo && userInfo.avatarUrl) {
              userData.avatar = userInfo.avatarUrl;
              console.log('使用前端提供的头像:', userData.avatar);
            } else if (!userData.avatar) {
              // 使用微信默认头像
              userData.avatar = 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132';
              console.log('使用默认头像');
            }
            
            // 保存处理后的用户信息
            wx.setStorageSync(STORAGE_KEYS.USER_INFO, userData);
          }
          
          // 更新全局状态
          this.globalData.token = res.data.token
          this.globalData.userInfo = userData || res.data.user
          this.globalData.isLogin = true
          this.globalData.isLoggedIn = true
          this.globalData.isProfileCompleted = true
          this.globalData.isDemote = false // 完整登录不是降级模式
          
          console.log('完整登录成功，用户信息:', JSON.stringify(this.globalData.userInfo))
          
          // 通知页面登录状态变化
          if (this.loginStateCallback) {
            this.loginStateCallback(true)
          }
          
          return res
        } else {
          console.error('完整登录失败，服务器返回:', res)
          return Promise.reject(new Error(res.message || '登录失败'))
        }
      } catch (apiError) {
        console.error('调用登录API失败:', apiError)
        
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none',
          duration: 2000
        })
        
        return Promise.reject(apiError)
      }
    } catch (error) {
      console.error('完整登录异常:', error)
      return Promise.reject(error)
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const token = wx.getStorageSync(STORAGE_KEYS.TOKEN)
    const userInfo = wx.getStorageSync(STORAGE_KEYS.USER_INFO)
    
    if (token && userInfo) {
      this.globalData.isLogin = true
      this.globalData.isLoggedIn = true
      this.globalData.userInfo = userInfo
      this.globalData.token = token
      
      // 用户资料是否完整取决于是否有昵称和头像
      const hasNickname = !!(userInfo.nickname && userInfo.nickname !== '微信用户')
      const hasAvatar = !!(userInfo.avatar)
      this.globalData.isProfileCompleted = hasNickname && hasAvatar
      
      // 检查是否为降级用户
      this.globalData.isDemote = userInfo.is_demote === true
    } else {
      this.globalData.isLogin = false
      this.globalData.isLoggedIn = false
      this.globalData.isProfileCompleted = false
      this.globalData.isDemote = false
    }
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
        // 适配不同屏幕尺寸
        this.globalData.windowHeight = res.windowHeight
        this.globalData.windowWidth = res.windowWidth
      }
    })
  },

  /**
   * 初始化购物车数据
   */
  initCartData() {
    try {
      // 尝试从本地存储获取购物车数据
      const cartItems = wx.getStorageSync(STORAGE_KEYS.CART_ITEMS) || []
      this.globalData.cartItems = cartItems
      this.globalData.cartCount = this.calculateCartCount(cartItems)
    } catch (error) {
      console.error('初始化购物车数据失败:', error)
      this.globalData.cartItems = []
      this.globalData.cartCount = 0
    }
  },

  /**
   * 更新购物车徽章
   */
  updateCartBadge() {
    // 检查是否登录
    if (!this.globalData.isLoggedIn) {
      console.log('未登录，不更新购物车徽章')
      return
    }

    // 添加随机参数防止缓存
    const timestamp = new Date().getTime();

    // 使用API获取最新购物车数据
    const request = require('./utils/request')
    request.get(`/api/v1/client/cart/list?_t=${timestamp}`)
      .then(res => {
        console.log('购物车徽章数据:', res.data)
        
        let cartItems = []
        
        if (res && res.data) {
          if (res.data.list && Array.isArray(res.data.list)) {
            cartItems = res.data.list
          } else if (Array.isArray(res.data)) {
            cartItems = res.data
          }
        }
        
        // 将最新数据保存到本地存储和全局变量
        wx.setStorageSync(STORAGE_KEYS.CART_ITEMS, cartItems)
      this.globalData.cartItems = cartItems
        
        // 计算商品总数
        const cartCount = this.calculateCartCount(cartItems)
      this.globalData.cartCount = cartCount
      
      // 设置tabBar徽章
      if (cartCount > 0) {
        wx.setTabBarBadge({
          index: 2, // 购物车是第三个tab，索引为2
          text: cartCount.toString()
        }).catch(err => {
          console.log('设置购物车徽章失败，可能是tabBar未渲染完成:', err)
        })
      } else {
        wx.removeTabBarBadge({
          index: 2
        }).catch(err => {
          console.log('移除购物车徽章失败，可能是tabBar未渲染完成:', err)
        })
      }
      })
      .catch(err => {
        console.error('获取购物车数据失败:', err)
      })
  },

  /**
   * 计算购物车商品总数量
   */
  calculateCartCount(cartItems) {
    if (!Array.isArray(cartItems)) return 0
    return cartItems.reduce((total, item) => total + (item.quantity || 1), 0)
  },

  /**
   * 全局数据
   */
  globalData: {
    isLogin: false,
    isLoggedIn: false, // 与isLogin保持一致，为了兼容性
    isProfileCompleted: false,
    isDemote: false, // 是否为降级用户（拒绝授权）
    userInfo: null,
    token: null,
    systemInfo: null,
    windowHeight: 0,
    windowWidth: 0,
    // API基础地址
    apiBase: '', // 初始化为空，在onLaunch中设置
    // 图片服务器基础URL
    baseUrl: '', // 初始化为空，在onLaunch中设置
    imageBaseUrl: '', // 初始化为空，在onLaunch中设置
    // 购物车数据
    cartItems: [],
    cartCount: 0,
    tempOrderGoods: [], // 临时订单商品数组
    imageBaseUrl: 'https://example.com/images' // 图片基础URL
  }
}) 