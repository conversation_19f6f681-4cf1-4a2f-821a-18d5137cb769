<template>
  <div class="app-container">
    <div v-loading="loading" class="bill-detail">
      <!-- 调试信息 -->
      <div v-if="!loading && Object.keys(detail).length === 0" style="text-align: center; padding: 50px;">
        <el-alert 
          title="数据加载失败" 
          type="warning" 
          description="无法获取账单详情，请检查网络连接或稍后重试" 
          show-icon>
        </el-alert>
        <el-button type="primary" @click="fetchData" style="margin-top: 20px;">重新加载</el-button>
      </div>
      
      <el-card v-else class="box-card">
        <div slot="header" class="clearfix">
          <span>分润账单详情</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-section">
              <h3 class="section-title">基本信息</h3>
              <div class="info-item">
                <span class="label">分润ID：</span>
                <span class="value">{{ detail.id }}</span>
              </div>
              <div class="info-item">
                <span class="label">分润金额：</span>
                <span class="value profit-amount">{{ detail.amount }} 元</span>
              </div>
              <div class="info-item">
                <span class="label">分润类型：</span>
                <span class="value">
                  <el-tag :type="getSourceTypeTag(detail.source_type)">{{ getSourceTypeLabel(detail.source_type) }}</el-tag>
                </span>
              </div>
              <div class="info-item">
                <span class="label">来源ID：</span>
                <span class="value">{{ detail.source_id }}</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ detail.created_at }}</span>
              </div>
              <div class="info-item">
                <span class="label">备注：</span>
                <span class="value">{{ detail.remark || '-' }}</span>
              </div>
            </div>
          </el-col>

          <el-col :span="12">
            <div class="info-section">
              <h3 class="section-title">用户信息</h3>
              <div class="user-header">
                <el-avatar :size="64" :src="userInfo.avatar">
                  <img src="https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png"/>
                </el-avatar>
                <h4 class="nickname">{{ userInfo.nickname }}</h4>
                <div class="user-role">
                  <el-tag v-if="userInfo.is_leader === 1" type="warning">团长</el-tag>
                  <el-tag v-else type="info">成员</el-tag>
                </div>
              </div>
              <div class="info-item">
                <span class="label">用户ID：</span>
                <span class="value">{{ userInfo.id }}</span>
              </div>
              <div class="info-item">
                <span class="label">手机号：</span>
                <span class="value">{{ userInfo.phone || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="label">账户余额：</span>
                <span class="value">{{ userInfo.balance || 0 }} 元</span>
              </div>
              <div class="info-item">
                <el-button type="text" @click="viewUser">查看用户详情</el-button>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 分润来源详情 -->
        <div v-if="detail.source_type === 1" class="source-detail">
          <h3 class="section-title">WiFi码分润详情</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="WiFi标题">{{ wifiInfo.title }}</el-descriptions-item>
            <el-descriptions-item label="WiFi名称">{{ wifiInfo.name }}</el-descriptions-item>
            <el-descriptions-item label="商户名称">{{ wifiInfo.merchant_name }}</el-descriptions-item>
            <el-descriptions-item label="使用次数">{{ wifiInfo.use_count }}</el-descriptions-item>
          </el-descriptions>
          <div class="action-btn">
            <el-button type="primary" @click="viewWifi">查看WiFi码详情</el-button>
          </div>
        </div>

        <div v-else-if="detail.source_type === 2" class="source-detail">
          <h3 class="section-title">订单分润详情</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="订单号">{{ orderInfo.order_no }}</el-descriptions-item>
            <el-descriptions-item label="订单金额">{{ orderInfo.total_amount }} 元</el-descriptions-item>
            <el-descriptions-item label="订单状态">
              <el-tag :type="getOrderStatusType(orderInfo.status)">{{ getOrderStatusText(orderInfo.status) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="下单时间">{{ orderInfo.created_at }}</el-descriptions-item>
          </el-descriptions>
          <div class="action-btn">
            <el-button type="primary" @click="viewOrder">查看订单详情</el-button>
          </div>
        </div>

        <div v-else-if="detail.source_type === 3" class="source-detail">
          <h3 class="section-title">广告分润详情</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="广告标题">{{ adInfo.title }}</el-descriptions-item>
            <el-descriptions-item label="广告位">{{ adInfo.space_name }}</el-descriptions-item>
            <el-descriptions-item label="点击次数">{{ adInfo.click_count }}</el-descriptions-item>
            <el-descriptions-item label="展示次数">{{ adInfo.view_count }}</el-descriptions-item>
          </el-descriptions>
          <div class="action-btn">
            <el-button type="primary" @click="viewAd">查看广告详情</el-button>
          </div>
        </div>

        <!-- 分润计算明细 -->
        <div class="profit-detail">
          <h3 class="section-title">分润计算明细</h3>
          <el-table :data="profitDetailList" border style="width: 100%">
            <el-table-column prop="role" label="角色" width="120" align="center" />
            <el-table-column prop="rate" label="分润比例" width="120" align="center">
              <template slot-scope="{row}">{{ row.rate }}%</template>
            </el-table-column>
            <el-table-column prop="amount" label="分润金额" align="center">
              <template slot-scope="{row}">{{ row.amount }} 元</template>
            </el-table-column>
            <el-table-column prop="user_info" label="分润用户" align="center" />
          </el-table>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getProfitBillDetail } from '@/api/profit'

export default {
  name: 'ProfitBillDetail',
  data () {
    return {
      loading: true,
      billId: null,
      detail: {},
      userInfo: {},
      wifiInfo: {},
      orderInfo: {},
      adInfo: {},
      profitDetailList: []
    }
  },
  created () {
    this.billId = parseInt(this.$route.params.id)
    this.fetchData()
  },
  methods: {
    fetchData () {
      this.loading = true
      console.log('开始获取账单详情，ID:', this.billId)
      
      getProfitBillDetail(this.billId).then(response => {
        console.log('API返回数据:', response)
        
        const { detail, user_info, source_info, profit_detail } = response.data
        this.detail = detail
        this.userInfo = user_info

        // 根据分润类型处理不同的来源信息
        if (detail.source_type === 1) {
          this.wifiInfo = source_info || {}
        } else if (detail.source_type === 2) {
          this.orderInfo = source_info || {}
        } else if (detail.source_type === 3) {
          this.adInfo = source_info || {}
        }

        // 处理分润明细
        this.profitDetailList = profit_detail || []

        this.loading = false
        console.log('数据加载完成')
      }).catch(error => {
        console.error('获取账单详情失败:', error)
        this.$message.error('获取账单详情失败：' + (error.message || '未知错误'))
        this.loading = false
      })
    },
    getSourceTypeLabel (type) {
      const map = {
        1: 'WiFi分润',
        2: '商品分润',
        3: '广告分润'
      }
      return map[type] || '未知'
    },
    getSourceTypeTag (type) {
      const map = {
        1: 'primary',
        2: 'success',
        3: 'warning'
      }
      return map[type] || 'info'
    },
    getOrderStatusType (status) {
      const statusMap = {
        0: 'info',
        1: 'primary',
        2: 'warning',
        3: 'success',
        4: 'danger'
      }
      return statusMap[status] || 'info'
    },
    getOrderStatusText (status) {
      const statusMap = {
        0: '待支付',
        1: '待发货',
        2: '待收货',
        3: '已完成',
        4: '已取消'
      }
      return statusMap[status] || '未知状态'
    },
    goBack () {
      this.$router.push('/profit/bill')
    },
    viewUser () {
      this.$router.push(`/user/detail/${this.userInfo.id}`)
    },
    viewWifi () {
      this.$router.push(`/wifi/detail/${this.wifiInfo.id}`)
    },
    viewOrder () {
      this.$router.push(`/mall/order/detail/${this.orderInfo.id}`)
    },
    viewAd () {
      this.$message.info('广告详情查看功能暂未实现')
    }
  }
}
</script>

<style scoped>
.section-title {
  font-size: 16px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
  margin-top: 20px;
  margin-bottom: 15px;
}
.info-section {
  margin-bottom: 20px;
}
.info-item {
  margin-bottom: 10px;
  display: flex;
}
.label {
  font-weight: bold;
  width: 100px;
  color: #606266;
}
.value {
  flex: 1;
  color: #303133;
}
.profit-amount {
  color: #67c23a;
  font-weight: bold;
}
.user-header {
  text-align: center;
  margin-bottom: 20px;
}
.nickname {
  margin: 10px 0;
}
.user-role {
  margin-bottom: 10px;
}
.source-detail {
  margin-top: 20px;
}
.action-btn {
  margin-top: 15px;
  text-align: center;
}
.profit-detail {
  margin-top: 30px;
}
</style>
