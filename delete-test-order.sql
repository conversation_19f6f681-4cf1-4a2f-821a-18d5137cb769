-- 查看所有订单
SELECT id, order_no, user_id, status, total_amount, created_at 
FROM orders 
ORDER BY created_at DESC 
LIMIT 10;

-- 删除金额为1065.00的测试订单
DELETE FROM order_goods WHERE order_id IN (SELECT id FROM orders WHERE total_amount = 1065.00);
DELETE FROM order_items WHERE order_id IN (SELECT id FROM orders WHERE total_amount = 1065.00);
DELETE FROM orders WHERE total_amount = 1065.00;

-- 确认删除结果
SELECT id, order_no, user_id, status, total_amount, created_at 
FROM orders 
ORDER BY created_at DESC 
LIMIT 5;
