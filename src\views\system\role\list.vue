<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button type="primary" @click="handleCreate">添加角色</el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="roleList"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="角色名称" width="150" />
      <el-table-column prop="code" label="角色编码" width="150" />
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180" />
      <el-table-column label="操作" width="200" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleUpdate(scope.row)"
          >编辑</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加/编辑角色对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
      <el-form
        ref="roleForm"
        :model="roleForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="roleForm.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色编码" prop="code">
          <el-input v-model="roleForm.code" placeholder="请输入角色编码（如：admin）" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            placeholder="请输入角色描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch
            v-model="roleForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item label="权限分配">
          <el-tree
            ref="permissionTree"
            :data="permissionList"
            :props="{
              label: 'name',
              children: 'children'
            }"
            show-checkbox
            node-key="id"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRoleList, createRole, updateRole, deleteRole } from '@/api/system'

export default {
  name: 'RoleList',
  data () {
    return {
      loading: false,
      roleList: [],
      dialogVisible: false,
      dialogTitle: '',
      roleForm: {
        id: undefined,
        name: '',
        code: '',
        description: '',
        status: 1,
        permissions: []
      },
      rules: {
        name: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入角色编码', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
          { pattern: /^[a-z_]+$/, message: '只能包含小写字母和下划线', trigger: 'blur' }
        ],
        description: [
          { max: 255, message: '长度不能超过 255 个字符', trigger: 'blur' }
        ]
      },
      permissionList: [
        {
          id: 1,
          name: 'WiFi码管理',
          children: [
            { id: 11, name: 'WiFi码查看' },
            { id: 12, name: 'WiFi码创建' },
            { id: 13, name: 'WiFi码编辑' },
            { id: 14, name: 'WiFi码删除' }
          ]
        },
        {
          id: 2,
          name: '用户管理',
          children: [
            { id: 21, name: '用户查看' },
            { id: 22, name: '用户编辑' },
            { id: 23, name: '用户标签管理' }
          ]
        },
        {
          id: 3,
          name: '商城管理',
          children: [
            {
              id: 31,
              name: '商品管理',
              children: [
                { id: 311, name: '商品查看' },
                { id: 312, name: '商品创建' },
                { id: 313, name: '商品编辑' },
                { id: 314, name: '商品删除' }
              ]
            },
            {
              id: 32,
              name: '订单管理',
              children: [
                { id: 321, name: '订单查看' },
                { id: 322, name: '订单处理' }
              ]
            }
          ]
        },
        {
          id: 4,
          name: '分润管理',
          children: [
            { id: 41, name: '分润规则设置' },
            { id: 42, name: '分润账单查看' },
            { id: 43, name: '提现管理' }
          ]
        },
        {
          id: 5,
          name: '广告管理',
          children: [
            { id: 51, name: '广告位管理' },
            { id: 52, name: '广告内容管理' }
          ]
        },
        {
          id: 6,
          name: '系统设置',
          children: [
            { id: 61, name: '基础设置' },
            { id: 62, name: '角色管理' },
            { id: 63, name: '账号管理' },
            { id: 64, name: '日志管理' }
          ]
        }
      ]
    }
  },
  created () {
    this.getRoleList()
  },
  methods: {
    getRoleList () {
      this.loading = true
      getRoleList()
        .then(response => {
          if (response.code === 200) {
            this.roleList = response.data.list || []
          } else {
            this.$message.error(response.message || '获取角色列表失败')
          }
        })
        .catch(() => {
          this.$message.error('获取角色列表失败')
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleCreate () {
      this.dialogTitle = '添加角色'
      this.roleForm = {
        id: undefined,
        name: '',
        code: '',
        description: '',
        status: 1,
        permissions: []
      }
      this.dialogVisible = true
      // 清除树选择
      this.$nextTick(() => {
        this.$refs.permissionTree && this.$refs.permissionTree.setCheckedKeys([])
      })
    },
    handleUpdate (row) {
      this.dialogTitle = '编辑角色'
      this.roleForm = Object.assign({}, row)
      this.dialogVisible = true

      // 根据已有权限设置树选择
      this.$nextTick(() => {
        const permissionIds = row.permissions || []
        this.$refs.permissionTree && this.$refs.permissionTree.setCheckedKeys(permissionIds)
      })
    },
    handleDelete (row) {
      if (row.id === 1) {
        this.$message.warning('超级管理员角色不能删除')
        return
      }

      this.$confirm('确认要删除该角色吗？删除后不可恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRole(row.id)
          .then(response => {
            if (response.code === 200) {
              this.$message.success('删除成功')
              this.getRoleList()
            } else {
              this.$message.error(response.message || '删除失败')
            }
          })
          .catch(error => {
            this.$message.error(error.message || '删除失败')
          })
      }).catch(() => {})
    },
    submitForm () {
      this.$refs.roleForm.validate(valid => {
        if (valid) {
          // 获取选中的权限ID
          const checkedKeys = this.$refs.permissionTree.getCheckedKeys()
          const halfCheckedKeys = this.$refs.permissionTree.getHalfCheckedKeys()
          this.roleForm.permissions = [...checkedKeys, ...halfCheckedKeys]

          const submitData = { ...this.roleForm }
          
          if (this.roleForm.id) {
            // 更新
            updateRole(this.roleForm.id, submitData)
              .then(response => {
                if (response.code === 200) {
                  this.$message.success('更新成功')
                  this.dialogVisible = false
                  this.getRoleList()
                } else {
                  this.$message.error(response.message || '更新失败')
                }
              })
              .catch(error => {
                this.$message.error(error.message || '更新失败')
              })
          } else {
            // 创建
            createRole(submitData)
              .then(response => {
                if (response.code === 200) {
                  this.$message.success('创建成功')
                  this.dialogVisible = false
                  this.getRoleList()
                } else {
                  this.$message.error(response.message || '创建失败')
                }
              })
              .catch(error => {
                this.$message.error(error.message || '创建失败')
              })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
}
</style>
