# 团队管理功能说明

## 功能概述

团队管理功能将联盟申请和团队管理整合在一起，提供了完整的团队管理解决方案。

## 功能特点

### 1. 联盟申请管理
- 查看所有联盟入驻申请
- 审核申请（批准/拒绝）
- 批准后自动创建团队并设置申请人为团长

### 2. 团队管理
- 团队列表展示
- 创建新团队
- 编辑团队信息
- 删除团队
- 团队详情查看

### 3. 团队成员管理
- 查看团队成员列表
- 添加新成员到团队
- 移除团队成员（团长除外）
- 成员角色管理（团长/普通成员）

## 数据库设计

### team表（团队表）
```sql
CREATE TABLE team (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '团队名称',
  leader_id INT NOT NULL COMMENT '团长用户ID',
  member_count INT DEFAULT 1 COMMENT '团队成员数量',
  status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  level INT DEFAULT 1 COMMENT '团队等级',
  total_income DECIMAL(10, 2) DEFAULT 0.00 COMMENT '团队总收益',
  month_income DECIMAL(10, 2) DEFAULT 0.00 COMMENT '本月收益',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)
```

### team_member表（团队成员表）
```sql
CREATE TABLE team_member (
  id INT PRIMARY KEY AUTO_INCREMENT,
  team_id INT NOT NULL COMMENT '团队ID',
  user_id INT NOT NULL COMMENT '用户ID',
  role VARCHAR(20) DEFAULT 'member' COMMENT '角色：leader-团长，member-成员',
  joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

## API接口

### 管理端接口

1. **团队列表**
   - GET `/api/v1/admin/team/list`
   - 参数：page, limit, name, leader, status

2. **团队详情**
   - GET `/api/v1/admin/team/detail/:id`

3. **创建团队**
   - POST `/api/v1/admin/team/create`
   - 参数：name, leader_id, status

4. **更新团队**
   - PUT `/api/v1/admin/team/update/:id`
   - 参数：name, status, level

5. **删除团队**
   - DELETE `/api/v1/admin/team/delete/:id`

6. **团队成员列表**
   - GET `/api/v1/admin/team/:id/members`
   - 参数：page, limit

7. **添加团队成员**
   - POST `/api/v1/admin/team/:id/members`
   - 参数：user_id

8. **移除团队成员**
   - DELETE `/api/v1/admin/team/:id/members/:userId`

9. **获取可选团长列表**
   - GET `/api/v1/admin/team/available-leaders`

## 使用说明

### 1. 初始化数据库
运行以下命令创建团队相关数据表：
```bash
cd wifi-share-server
node init-team-tables.js
```

### 2. 访问团队管理
1. 登录管理后台
2. 在左侧菜单中点击"团队管理"
3. 可以查看团队列表、创建新团队、管理团队成员等

### 3. 联盟申请处理流程
1. 用户在小程序端提交联盟入驻申请
2. 管理员在"用户管理"->"联盟申请"中查看申请
3. 审核通过后，系统自动：
   - 创建新团队
   - 设置申请人为团长
   - 更新用户状态为团长

## 注意事项

1. 一个用户只能是一个团队的团长
2. 团长不能被移除出团队
3. 删除团队会同时删除所有成员关系
4. 团队成员数量会自动更新

## 后续优化建议

1. 添加团队业绩统计功能
2. 实现团队等级自动升级机制
3. 添加团队公告功能
4. 实现团队内部分润机制
5. 添加团队活跃度统计 