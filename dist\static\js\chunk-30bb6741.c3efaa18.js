(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-30bb6741"],{"214f":function(e,t,n){},"274d":function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"b",(function(){return l})),n.d(t,"a",(function(){return s}));var a=n("b775");function i(e){return Object(a["a"])({url:"/api/v1/admin/region/list",method:"get",params:e})}function l(e){return Object(a["a"])({url:"/api/v1/admin/region/detail/"+e,method:"get"})}function s(e){return Object(a["a"])({url:"/api/v1/admin/region/delete/"+e,method:"delete"})}},"330e":function(e,t,n){"use strict";n("34b0")},"333d":function(e,t,n){"use strict";var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[t("el-pagination",e._b({attrs:{background:e.background,"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,"page-sizes":e.pageSizes,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1))],1)},i=[],l={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get(){return this.page},set(e){this.$emit("update:page",e)}},pageSize:{get(){return this.limit},set(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange(e){this.$emit("pagination",{page:this.currentPage,limit:e})},handleCurrentChange(e){this.$emit("pagination",{page:e,limit:this.pageSize})}}},s=l,r=(n("330e"),n("2877")),o=Object(r["a"])(s,a,i,!1,null,"11252b03",null);t["a"]=o.exports},"34b0":function(e,t,n){},6724:function(e,t,n){"use strict";n("8d41");const a={bind(e,t){e.addEventListener("click",n=>{const a=Object.assign({},t.value),i=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},a),l=i.ele;if(l){l.style.position="relative",l.style.overflow="hidden";const e=l.getBoundingClientRect();let t=l.querySelector(".waves-ripple");switch(t?t.className="waves-ripple":(t=document.createElement("span"),t.className="waves-ripple",t.style.height=t.style.width=Math.max(e.width,e.height)+"px",l.appendChild(t)),i.type){case"center":t.style.top=e.height/2-t.offsetHeight/2+"px",t.style.left=e.width/2-t.offsetWidth/2+"px";break;default:t.style.top=(n.pageY-e.top-t.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",t.style.left=(n.pageX-e.left-t.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return t.style.backgroundColor=i.color,t.className="waves-ripple z-active",!1}},!1)}};var i=a;const l=function(e){e.directive("waves",i)};window.Vue&&(window.waves=i,Vue.use(l)),i.install=l;t["a"]=i},"8d41":function(e,t,n){},d2c7:function(e,t,n){"use strict";n("214f")},e587:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"region-list"},[t("div",{staticClass:"filter-container"},[t("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"地区名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleFilter.apply(null,arguments)}},model:{value:e.listQuery.name,callback:function(t){e.$set(e.listQuery,"name",t)},expression:"listQuery.name"}}),t("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleFilter}},[e._v(" 搜索 ")]),t("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit"},on:{click:e.handleCreate}},[e._v(" 添加地区 ")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],key:e.tableKey,staticStyle:{width:"100%"},attrs:{data:e.list,border:"",fit:"","highlight-current-row":""}},[t("el-table-column",{attrs:{label:"ID",prop:"id",sortable:"custom",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function({row:n}){return[t("span",[e._v(e._s(n.id))])]}}])}),t("el-table-column",{attrs:{label:"地区名称",width:"150px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:n}){return[t("span",[e._v(e._s(n.name))])]}}])}),t("el-table-column",{attrs:{label:"地区代码",width:"120px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:n}){return[t("span",[e._v(e._s(n.code))])]}}])}),t("el-table-column",{attrs:{label:"父级地区",width:"150px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:n}){return[t("span",[e._v(e._s(n.parent_name||"无"))])]}}])}),t("el-table-column",{attrs:{label:"级别",width:"100px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:n}){return[t("el-tag",{attrs:{type:1===n.level?"primary":2===n.level?"success":"info"}},[e._v(" "+e._s(e.getLevelText(n.level))+" ")])]}}])}),t("el-table-column",{attrs:{label:"状态",width:"100px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:n}){return[t("el-tag",{attrs:{type:1===n.status?"success":"danger"}},[e._v(" "+e._s(1===n.status?"启用":"禁用")+" ")])]}}])}),t("el-table-column",{attrs:{label:"创建时间",width:"180px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:n}){return[t("span",[e._v(e._s(n.created_at))])]}}])}),t("el-table-column",{attrs:{label:"操作",align:"center",width:"230","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function({row:n}){return[t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.handleUpdate(n)}}},[e._v(" 编辑 ")]),t("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(t){return e.handleDetail(n)}}},[e._v(" 详情 ")]),"deleted"!==n.status?t("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.handleDelete(n)}}},[e._v(" 删除 ")]):e._e()]}}])})],1),t("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){return e.$set(e.listQuery,"page",t)},"update:limit":function(t){return e.$set(e.listQuery,"limit",t)},pagination:e.getList}})],1)},i=[],l=(n("14d9"),n("274d")),s=n("6724"),r=n("333d"),o={name:"RegionList",components:{Pagination:r["a"]},directives:{waves:s["a"]},data(){return{tableKey:0,list:[],total:0,listLoading:!0,listQuery:{page:1,limit:20,name:void 0}}},created(){this.getList()},methods:{getList(){this.listLoading=!0,Object(l["c"])(this.listQuery).then(e=>{this.list=e.data.items,this.total=e.data.total,this.listLoading=!1})},handleFilter(){this.listQuery.page=1,this.getList()},handleCreate(){this.$router.push("/region/create")},handleUpdate(e){this.$router.push("/region/edit/"+e.id)},handleDetail(e){this.$router.push("/region/detail/"+e.id)},handleDelete(e){this.$confirm("确定删除该地区吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{Object(l["a"])(e.id).then(()=>{this.$message({type:"success",message:"删除成功!"}),this.getList()})})},getLevelText(e){const t={1:"省级",2:"市级",3:"区县级"};return t[e]||"未知"}}},u=o,c=(n("d2c7"),n("2877")),d=Object(c["a"])(u,a,i,!1,null,"58c7013a",null);t["default"]=d.exports}}]);