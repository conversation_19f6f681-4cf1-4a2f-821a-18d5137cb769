import request from '@/utils/request'

// 本地模拟数据，用于开发测试
const useMock = false  // 禁用模拟数据

// 使用本地存储管理分润规则数据
const PROFIT_RULES_STORAGE_KEY = 'wifi_admin_profit_rules'
const BILL_LIST_STORAGE_KEY = 'wifi_admin_profit_bills'
const WITHDRAW_LIST_STORAGE_KEY = 'wifi_admin_withdraw_list'

// 默认分润规则
const defaultProfitRules = {
  wifi_share: {
    name: 'WiFi分享',
    user_rate: 70,  // 用户分润比例
    platform_rate: 30,  // 平台分润比例
    status: 1  // 启用状态
  },
  goods_sale: {
    name: '商品销售',
    user_rate: 10,  // 用户分润比例
    leader_rate: 5,  // 团长分润比例
    platform_rate: 85,  // 平台分润比例
    status: 1  // 启用状态
  },
  advertisement: {
    name: '广告点击',
    user_rate: 20,  // 用户分润比例
    leader_rate: 10,  // 团长分润比例
    platform_rate: 70,  // 平台分润比例
    status: 1  // 启用状态
  },
  updated_at: '2025-07-08 16:30:00'
}

// 获取分润规则数据（从本地存储读取，如果没有则使用默认数据）
function getProfitRulesData() {
  try {
    const stored = localStorage.getItem(PROFIT_RULES_STORAGE_KEY)
    return stored ? JSON.parse(stored) : defaultProfitRules
  } catch (error) {
    console.warn('读取分润规则数据失败，使用默认数据:', error)
    return defaultProfitRules
  }
}

// 保存分润规则数据到本地存储
function saveProfitRulesData(data) {
  try {
    localStorage.setItem(PROFIT_RULES_STORAGE_KEY, JSON.stringify(data))
  } catch (error) {
    console.error('保存分润规则数据失败:', error)
  }
}

// 默认账单数据
const defaultBillList = [
  {
    id: 101,
    bill_no: 'SB20250708001',
    type: 'wifi_share',  // wifi_share, goods_sale, advertisement
    amount: 0.60,
    share_amount: 0.42,
    platform_amount: 0.18,
    user_id: 1001,
    user_name: '张三',
    user_phone: '138****1001',
    source_id: 1,
    source_type: 'wifi',
    status: 1,  // 1待结算，2已结算，3已取消
    remark: 'WiFi码分享使用收益',
    settle_time: null,
    created_at: '2025-07-08 10:15:22'
  },
  {
    id: 102,
    bill_no: 'SB20250708002',
    type: 'wifi_share',
    amount: 0.40,
    share_amount: 0.28,
    platform_amount: 0.12,
    user_id: 1002,
    user_name: '李四',
    user_phone: '138****1002',
    source_id: 2,
    source_type: 'wifi',
    status: 2,
    remark: 'WiFi码分享使用收益',
    settle_time: '2025-07-08 16:32:45',
    created_at: '2025-07-08 09:27:18'
  },
  {
    id: 103,
    bill_no: 'SB20250708003',
    type: 'goods_sale',
    amount: 25.00,
    share_amount: 2.50,
    platform_amount: 22.50,
    user_id: 1003,
    user_name: '王五',
    user_phone: '138****1003',
    source_id: 1,
    source_type: 'order',
    status: 1,
    remark: '商品销售分润',
    settle_time: null,
    created_at: '2025-07-08 14:52:36'
  },
  {
    id: 104,
    bill_no: 'SB20250708004',
    type: 'advertisement',
    amount: 5.60,
    share_amount: 1.12,
    platform_amount: 4.48,
    user_id: 1001,
    user_name: '张三',
    user_phone: '138****1001',
    source_id: 3,
    source_type: 'ad_click',
    status: 2,
    remark: '广告点击收益',
    settle_time: '2025-07-08 18:21:05',
    created_at: '2025-07-08 16:05:43'
  }
]

// 获取账单数据（从本地存储读取，如果没有则使用默认数据）
function getBillListData() {
  try {
    const stored = localStorage.getItem(BILL_LIST_STORAGE_KEY)
    return stored ? JSON.parse(stored) : defaultBillList
  } catch (error) {
    console.warn('读取账单数据失败，使用默认数据:', error)
    return defaultBillList
  }
}

// 保存账单数据到本地存储
function saveBillListData(data) {
  try {
    localStorage.setItem(BILL_LIST_STORAGE_KEY, JSON.stringify(data))
  } catch (error) {
    console.error('保存账单数据失败:', error)
  }
}

// 默认提现数据
const defaultWithdrawList = [
  {
    id: 1,
    withdraw_no: 'W2025070800001',
    user_id: 1001,
    user_name: '张三',
    user_phone: '138****1001',
    amount: 200.00,
    status: 0,  // 0待审核，1审核通过待打款，2审核拒绝，3已打款
    type: 1,  // 1支付宝，2银行卡
    account_type: '支付宝',
    account_name: '张三',
    account_no: '<EMAIL>',
    bank_name: null,
    created_at: '2025-07-08 10:12:33',
    audit_time: null,
    audit_user: null,
    audit_remark: null,
    pay_time: null,
    pay_remark: null
  },
  {
    id: 2,
    withdraw_no: 'W2025070800002',
    user_id: 1002,
    user_name: '李四',
    user_phone: '138****1002',
    amount: 500.00,
    status: 1,
    type: 2,
    account_type: '银行卡',
    account_name: '李四',
    account_no: '6222xxxxxxx',
    bank_name: '工商银行',
    created_at: '2025-07-08 09:45:21',
    audit_time: '2025-07-08 11:23:45',
    audit_user: 'admin',
    audit_remark: '审核通过',
    pay_time: null,
    pay_remark: null
  },
  {
    id: 3,
    withdraw_no: 'W2025070800003',
    user_id: 1003,
    user_name: '王五',
    user_phone: '138****1003',
    amount: 100.00,
    status: 2,
    type: 1,
    account_type: '支付宝',
    account_name: '王五',
    account_no: '<EMAIL>',
    bank_name: null,
    created_at: '2025-07-07 16:32:18',
    audit_time: '2025-07-08 09:15:30',
    audit_user: 'admin',
    audit_remark: '金额不足，请重新提交',
    pay_time: null,
    pay_remark: null
  },
  {
    id: 4,
    withdraw_no: 'W2025070800004',
    user_id: 1004,
    user_name: '赵六',
    user_phone: '138****1004',
    amount: 300.00,
    status: 3,
    type: 2,
    account_type: '银行卡',
    account_name: '赵六',
    account_no: '6217xxxxxxx',
    bank_name: '招商银行',
    created_at: '2025-07-07 14:56:42',
    audit_time: '2025-07-07 16:28:35',
    audit_user: 'admin',
    audit_remark: '审核通过',
    pay_time: '2025-07-08 10:35:12',
    pay_remark: '打款成功'
  }
]

// 获取提现数据（从本地存储读取，如果没有则使用默认数据）
function getWithdrawListData() {
  try {
    const stored = localStorage.getItem(WITHDRAW_LIST_STORAGE_KEY)
    return stored ? JSON.parse(stored) : defaultWithdrawList
  } catch (error) {
    console.warn('读取提现数据失败，使用默认数据:', error)
    return defaultWithdrawList
  }
}

// 保存提现数据到本地存储
function saveWithdrawListData(data) {
  try {
    localStorage.setItem(WITHDRAW_LIST_STORAGE_KEY, JSON.stringify(data))
  } catch (error) {
    console.error('保存提现数据失败:', error)
  }
}

// 获取分润规则
export function getProfitRules () {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前分润规则数据
        const profitRules = getProfitRulesData()
        resolve({
          code: 200,
          data: profitRules,
          message: '获取成功'
        })
      }, 200)
    })
  }

  return request({
    url: '/api/v1/admin/income/rules',
    method: 'get'
  })
}

// 更新分润规则
export function updateProfitRules (data) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前分润规则数据
        const profitRules = getProfitRulesData()
        
        // 合并更新数据
        const updatedRules = Object.assign(profitRules, data, {
          updated_at: new Date().toISOString().replace('T', ' ').slice(0, 19)
        })
        
        // 保存到本地存储
        saveProfitRulesData(updatedRules)
        
        resolve({
          code: 200,
          message: '更新成功'
        })
      }, 500)
    })
  }

  return request({
    url: '/api/v1/admin/income/rules/update',
    method: 'post',
    data
  })
}

// 获取分润账单列表
export function getProfitBillList (query) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前分润账单数据
        const mockBillList = getBillListData()
        
        const page = parseInt(query.page) || 1
        const limit = parseInt(query.limit) || 10
        
        // 过滤逻辑
        let filteredData = [...mockBillList]
        
        // 按用户搜索
        if (query.keyword) {
          filteredData = filteredData.filter(item => 
            item.user_name.includes(query.keyword) ||
            item.user_phone.includes(query.keyword) ||
            item.bill_no.includes(query.keyword)
          )
        }
        
        // 按类型筛选
        if (query.type) {
          filteredData = filteredData.filter(item => item.type === query.type)
        }
        
        // 按状态筛选
        if (query.status !== undefined && query.status !== '') {
          filteredData = filteredData.filter(item => item.status === parseInt(query.status))
        }
        
        // 按日期筛选
        if (query.start_date && query.end_date) {
          filteredData = filteredData.filter(item => {
            const itemDate = new Date(item.created_at)
            const startDate = new Date(query.start_date)
            const endDate = new Date(query.end_date)
            endDate.setHours(23, 59, 59, 999)
            return itemDate >= startDate && itemDate <= endDate
          })
        }
        
        // 分页
        const total = filteredData.length
        const start = (page - 1) * limit
        const end = start + limit
        const list = filteredData.slice(start, end)
        
        // 计算统计数据
        const totalAmount = filteredData.reduce((sum, item) => sum + item.amount, 0)
        const totalShareAmount = filteredData.reduce((sum, item) => sum + item.share_amount, 0)
        const totalPlatformAmount = filteredData.reduce((sum, item) => sum + item.platform_amount, 0)
        
        resolve({
          code: 200,
          data: {
            list,
            total,
            page,
            limit,
            stats: {
              total_amount: totalAmount.toFixed(2),
              total_share_amount: totalShareAmount.toFixed(2),
              total_platform_amount: totalPlatformAmount.toFixed(2)
            }
          },
          message: '获取成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/income/bill/list',
    method: 'get',
    params: query
  })
}

// 获取分润账单详情
export function getProfitBillDetail (id) {
  if (useMock) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
    try {
      // 从本地存储获取账单列表
      const billList = getBillListData()
      
      // 查找指定ID的账单
      const bill = billList.find(item => item.id === parseInt(id))
      
      if (!bill) {
        reject(new Error('账单不存在'))
        return
      }

      // 构造返回数据
      const response = {
        code: 200,
        data: {
          detail: {
            id: bill.id,
            amount: bill.amount,
            source_type: bill.type === 'wifi_share' ? 1 : (bill.type === 'goods_sale' ? 2 : 3),
            source_id: bill.bill_no,
            created_at: bill.created_at,
            remark: bill.remark
          },
          user_info: {
            id: bill.user_id,
            nickname: bill.user_name,
            phone: bill.user_phone,
            avatar: '',
            is_leader: Math.random() > 0.5 ? 1 : 0,  // 模拟数据
            balance: (Math.random() * 1000).toFixed(2)  // 模拟数据
          },
          source_info: bill.type === 'wifi_share' ? {
            title: 'WiFi示例',
            name: 'Test_WiFi_' + bill.id,
            merchant_name: '示例商户',
            use_count: Math.floor(Math.random() * 100)
          } : (bill.type === 'goods_sale' ? {
            order_no: bill.bill_no,
            total_amount: bill.amount,
            status: 1,
            created_at: bill.created_at
          } : {
            title: '广告示例',
            space_name: '首页广告位',
            click_count: Math.floor(Math.random() * 1000),
            view_count: Math.floor(Math.random() * 5000)
          }),
          profit_detail: [
            {
              role: '分享者',
              rate: 70,
              amount: bill.share_amount,
              user_info: `${bill.user_name}(${bill.user_phone})`
            },
            {
              role: '平台',
              rate: 30,
              amount: bill.platform_amount,
              user_info: '系统平台'
            }
          ]
        },
        message: 'success'
      }

        resolve(response)
      } catch (error) {
        reject(error)
      }
    }, 200)
    })
  }

  return request({
    url: `/api/v1/admin/income/bill/detail/${id}`,
    method: 'get'
  })
}

// 结算分润账单
export function settleProfitBill (id, data) {
  if (useMock) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 获取当前分润账单数据
        const mockBillList = getBillListData()
        const index = mockBillList.findIndex(item => item.id === parseInt(id))
        if (index > -1) {
          mockBillList[index].status = 2
          mockBillList[index].settle_time = new Date().toISOString().replace('T', ' ').slice(0, 19)
          
          // 保存到本地存储
          saveBillListData(mockBillList)
          
          resolve({
            code: 200,
            message: '结算成功'
          })
        } else {
          reject(new Error('账单不存在'))
        }
      }, 500)
    })
  }

  return request({
    url: `/api/v1/admin/income/bill/settle/${id}`,
    method: 'post'
  })
}

// 获取提现申请列表
export function getWithdrawList (query) {
  if (useMock) {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 获取当前提现申请数据
        const mockWithdrawList = getWithdrawListData()
        
        const page = parseInt(query.page) || 1
        const limit = parseInt(query.limit) || 10
        
        // 过滤逻辑
        let filteredData = [...mockWithdrawList]
        
        // 按用户搜索
        if (query.keyword) {
          filteredData = filteredData.filter(item => 
            item.user_name.includes(query.keyword) ||
            item.user_phone.includes(query.keyword) ||
            item.withdraw_no.includes(query.keyword)
          )
        }
        
        // 按状态筛选
        if (query.status !== undefined && query.status !== '') {
          filteredData = filteredData.filter(item => item.status === parseInt(query.status))
        }
        
        // 按日期筛选
        if (query.start_date && query.end_date) {
          filteredData = filteredData.filter(item => {
            const itemDate = new Date(item.created_at)
            const startDate = new Date(query.start_date)
            const endDate = new Date(query.end_date)
            endDate.setHours(23, 59, 59, 999)
            return itemDate >= startDate && itemDate <= endDate
          })
        }
        
        // 分页
        const total = filteredData.length
        const start = (page - 1) * limit
        const end = start + limit
        const list = filteredData.slice(start, end)
        
        resolve({
          code: 200,
          data: {
            list,
            total,
            page,
            limit
          },
          message: '获取成功'
        })
      }, 300)
    })
  }

  return request({
    url: '/api/v1/admin/withdraw/list',
    method: 'get',
    params: query
  })
}

// 获取提现申请详情
export function getWithdrawDetail (id) {
  if (useMock) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 获取当前提现申请数据
        const mockWithdrawList = getWithdrawListData()
        const withdraw = mockWithdrawList.find(item => item.id === parseInt(id))
        if (withdraw) {
          // 模拟详细信息和用户信息
          const detailData = {
            detail: {
              ...withdraw,
              card_holder: withdraw.account_name,
              bank_name: withdraw.bank_name || '支付宝',
              card_number: withdraw.account_no,
              bank_branch: withdraw.bank_name === '工商银行' ? '北京市朝阳区支行' : null,
              transfer_time: withdraw.pay_time,
              transaction_id: withdraw.status === 3 ? 'TRX202412270001' : null,
              remark: withdraw.audit_remark || withdraw.pay_remark || null
            },
            user_info: {
              id: withdraw.user_id,
              nickname: withdraw.user_name,
              phone: withdraw.user_phone,
              avatar: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
              balance: 1580.50,
              is_leader: withdraw.user_id === 101 ? 1 : 0
            }
          }
          
          resolve({
            code: 200,
            data: detailData,
            message: '获取成功'
          })
        } else {
          reject(new Error('提现申请不存在'))
        }
      }, 200)
    })
  }

  return request({
    url: `/api/v1/admin/withdraw/detail/${id}`,
    method: 'get'
  })
}

// 审核提现申请
export function auditWithdraw (id, data) {
  if (useMock) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 获取当前提现申请数据
        const mockWithdrawList = getWithdrawListData()
        const index = mockWithdrawList.findIndex(item => item.id === parseInt(id))
        if (index > -1) {
          mockWithdrawList[index].status = data.status
          mockWithdrawList[index].audit_time = new Date().toISOString().replace('T', ' ').slice(0, 19)
          mockWithdrawList[index].audit_user = 'admin'
          mockWithdrawList[index].audit_remark = data.remark || (data.status === 1 ? '审核通过' : '审核拒绝')
          
          // 保存到本地存储
          saveWithdrawListData(mockWithdrawList)
          
          resolve({
            code: 200,
            message: '审核成功'
          })
        } else {
          reject(new Error('提现申请不存在'))
        }
      }, 500)
    })
  }

  return request({
    url: `/api/v1/admin/withdraw/audit/${id}`,
    method: 'post',
    data
  })
}

// 确认提现打款
export function confirmWithdraw (id, data) {
  if (useMock) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 获取当前提现申请数据
        const mockWithdrawList = getWithdrawListData()
        const index = mockWithdrawList.findIndex(item => item.id === parseInt(id))
        if (index > -1) {
          mockWithdrawList[index].status = 3
          mockWithdrawList[index].pay_time = new Date().toISOString().replace('T', ' ').slice(0, 19)
          mockWithdrawList[index].pay_remark = data.remark || '已打款'
          mockWithdrawList[index].transaction_id = data.transaction_id
          
          // 保存到本地存储
          saveWithdrawListData(mockWithdrawList)
          
          resolve({
            code: 200,
            message: '打款成功'
          })
        } else {
          reject(new Error('提现申请不存在'))
        }
      }, 500)
    })
  }

  return request({
    url: `/api/v1/admin/withdraw/confirm/${id}`,
    method: 'post',
    data
  })
}
