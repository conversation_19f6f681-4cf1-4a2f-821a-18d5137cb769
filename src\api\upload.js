import request from '@/utils/request'

// 本地模拟数据，用于开发测试
const useMock = false  // 禁用模拟数据

// 生成随机文件名
function generateRandomFileName(originalName) {
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 10000)
  const ext = originalName.split('.').pop()
  return `${timestamp}_${random}.${ext}`
}

// 文件上传
export function uploadFile(file) {
  if (useMock) {
    return new Promise((resolve) => {
      // 模拟上传延迟
      setTimeout(() => {
        // 创建一个本地的文件URL（在实际项目中，这应该是服务器返回的URL）
        const reader = new FileReader()
        reader.onload = (e) => {
          const fileUrl = e.target.result
          const fileName = generateRandomFileName(file.name)
          
          resolve({
            code: 200,
            data: {
              url: fileUrl,
              filename: fileName,
              size: file.size,
              type: file.type
            },
            message: '上传成功'
          })
        }
        reader.readAsDataURL(file)
      }, 500)
    })
  }

  // 真实的上传逻辑
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/api/v1/admin/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
} 