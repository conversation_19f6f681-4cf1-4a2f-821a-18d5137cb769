import request from '@/utils/request'

/**
 * 获取联盟入驻申请列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function fetchAllianceList(params) {
  return request({
    url: '/api/v1/admin/alliance/list',
    method: 'get',
    params
  })
}

/**
 * 获取联盟入驻申请详情
 * @param {number|string} id 申请ID
 * @returns {Promise}
 */
export function fetchAllianceDetail(id) {
  return request({
    url: `/api/v1/admin/alliance/detail/${id}`,
    method: 'get'
  })
}

/**
 * 审核联盟入驻申请
 * @param {number|string} id 申请ID
 * @param {Object} data 审核数据
 * @returns {Promise}
 */
export function auditAlliance(id, data) {
  return request({
    url: `/api/v1/admin/alliance/audit/${id}`,
    method: 'post',
    data
  })
}

// 删除联盟申请
export function deleteAlliance(id) {
  // 尝试使用DELETE方法，如果失败则使用POST方法
  return request({
    url: `/api/v1/admin/alliance/delete/${id}`,
    method: 'delete'
  }).catch(error => {
    // 如果DELETE方法失败，尝试使用POST方法
    if (error.response && error.response.status === 404) {
      return request({
        url: `/api/v1/admin/alliance/delete/${id}`,
        method: 'post'
      })
    }
    throw error
  })
} 