# WiFi共享管理后台日志配置说明

## 日志系统概述

WiFi共享管理后台现已添加完整的日志系统，可以记录应用运行过程中的各种信息，包括请求、错误和系统状态等。日志文件存储在项目根目录下的`logs`文件夹中。

## 日志文件说明

系统会生成以下几种日志文件：

1. **wifi-share-admin.log** - 综合日志，包含所有级别的日志信息
2. **info.log** - 信息级别日志，记录正常操作和状态信息
3. **warn.log** - 警告级别日志，记录潜在问题和异常情况
4. **error.log** - 错误级别日志，记录系统错误和异常

使用PM2管理时，还会生成：

1. **wifi-share-admin-out.log** - PM2标准输出日志
2. **wifi-share-admin-error.log** - PM2错误输出日志

## 使用PM2管理应用

### 安装PM2

```bash
npm install -g pm2
```

### 启动应用

```bash
# 使用配置文件启动
pm2 start ecosystem.config.js

# 或直接启动
pm2 start server.js --name wifi-share-admin
```

### 查看日志

```bash
# 查看实时日志
pm2 logs wifi-share-admin

# 查看特定日志文件
cat logs/wifi-share-admin.log
# 或
tail -f logs/wifi-share-admin.log
```

### 管理应用

```bash
# 查看应用状态
pm2 status

# 重启应用
pm2 restart wifi-share-admin

# 停止应用
pm2 stop wifi-share-admin

# 删除应用
pm2 delete wifi-share-admin
```

## 在宝塔面板中配置

1. 在宝塔面板中，进入"软件商店" -> "PM2管理器"
2. 点击"添加项目"
3. 填写以下信息：
   - 项目名称：wifi-share-admin
   - 启动文件：server.js
   - 项目目录：/www/wwwroot/wifi-share-admin（根据实际路径调整）
   - 运行用户：www
   - 启动参数：--max-old-space-size=1024
4. 点击"提交"

## 在宝塔面板中查看日志

1. 在PM2管理器中找到wifi-share-admin项目
2. 点击"管理" -> "查看日志"
3. 可以查看PM2的标准输出和错误输出

也可以通过宝塔面板的文件管理器，直接查看`logs`目录下的日志文件。

## 日志格式说明

日志采用JSON格式，包含以下字段：

- **timestamp**: 日志记录时间
- **level**: 日志级别（info/warn/error）
- **message**: 日志消息内容
- **method**: HTTP请求方法（如GET、POST等）
- **url**: 请求URL
- **userAgent**: 用户代理信息
- **ip**: 客户端IP地址

## 故障排查

如果应用无法正常启动或运行，请检查：

1. 查看error.log文件，了解具体错误信息
2. 确保Node.js版本兼容（推荐v14+）
3. 确保项目依赖已正确安装（npm install）
4. 确保dist目录存在（npm run build）
5. 检查端口是否被占用（默认8081）

## 日志轮转

为防止日志文件过大，建议配置日志轮转：

```bash
# 安装PM2日志轮转模块
pm2 install pm2-logrotate

# 配置日志轮转
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 7
pm2 set pm2-logrotate:compress false
pm2 set pm2-logrotate:dateFormat YYYY-MM-DD_HH-mm-ss
```

这将使日志文件在达到10MB时自动轮转，并保留最近7个日志文件。
