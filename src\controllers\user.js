/**
 * 用户控制器
 */
const { success, error, unauthorized } = require('../utils/response');
const UserModel = require('../models/user');
const logger = require('../utils/logger');
const config = require('../../config');

/**
 * 获取用户信息
 */
const getUserInfo = async (req, res) => {
  try {
    const userId = req.user ? req.user.id : null;
    
    if (!userId) {
      logger.warn('获取用户信息失败: 未提供用户ID');
      return unauthorized(res, '请先登录');
    }
    
    logger.info(`获取用户信息: ID=${userId}`);
    
    // 获取用户信息
    const user = await UserModel.findById(userId);
    
    if (!user) {
      logger.warn(`获取用户信息失败: 未找到用户(ID=${userId})`);
      return error(res, '用户不存在', 404);
    }
    
    logger.info(`成功获取用户信息: ID=${userId}`);
    
    // 处理用户头像URL
    let avatarUrl = user.avatar || '';
    
    // 如果头像不是http开头的URL，且不是空白，添加域名
    if (avatarUrl && !avatarUrl.startsWith('http') && !avatarUrl.startsWith('/assets/')) {
      const baseUrl = config.server.baseUrl || `http://${req.headers.host}`;
      avatarUrl = avatarUrl.startsWith('/') 
        ? `${baseUrl}${avatarUrl}`
        : `${baseUrl}/${avatarUrl}`;
      
      logger.info(`处理后的头像URL: ${avatarUrl}`);
    }
    
    // 返回格式化后的用户信息
    return success(res, {
      id: user.id,
      openid: user.openid,
      nickname: user.nickname || '微信用户',
      avatar: avatarUrl,
      gender: user.gender || 0,
      phone: user.phone || '',
      is_leader: !!user.is_leader,
      is_demote: !!user.is_demote, // 确保返回降级用户标记
      points: user.points || 0,
      balance: user.balance || 0,
      created_at: user.created_at,
      updated_at: user.updated_at
    });
  } catch (err) {
    logger.error(`获取用户信息失败: ${err.message}`);
    return error(res, '获取用户信息失败', 500);
  }
};

/**
 * 更新用户信息
 */
const updateUserInfo = async (req, res) => {
  try {
    const userId = req.user ? req.user.id : null;
    
    if (!userId) {
      return unauthorized(res, '请先登录');
    }
    
    const updateData = req.body;
    logger.info(`收到用户信息更新请求: ID=${userId}, 数据:${JSON.stringify(updateData)}`);
    
    // 验证更新数据
    if (!updateData || Object.keys(updateData).length === 0) {
      logger.warn(`更新用户信息失败: 未提供更新数据, ID=${userId}`);
      return error(res, '未提供更新数据', 400);
    }
    
    // 不允许更新的字段
    const restrictedFields = ['id', 'openid', 'password', 'role', 'balance', 'points'];
    
    // 过滤掉不允许更新的字段
    restrictedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        delete updateData[field];
        logger.warn(`尝试更新受限字段: ${field}`);
      }
    });
    
    // 如果提供了用户信息，取消降级标记
    if (updateData.nickname || updateData.avatar || updateData.gender !== undefined) {
      // 将布尔值转换为整数 (0/1)
      updateData.is_demote = 0;
      logger.info('用户提供了个人信息，取消降级标记 (is_demote=0)');
    }
    
    // 处理头像URL - 如果是完整URL，保留原样
    if (updateData.avatar && updateData.avatar.startsWith('http')) {
      logger.info(`用户头像是完整URL，保持不变: ${updateData.avatar}`);
    }
    
    // 确保布尔值字段被转换为整数
    if (updateData.is_demote !== undefined) {
      updateData.is_demote = updateData.is_demote ? 1 : 0;
      logger.info(`将is_demote布尔值转换为整数: ${updateData.is_demote}`);
    }
    
    // 更新用户信息
    logger.info(`开始更新用户信息: ID=${userId}, 数据:${JSON.stringify(updateData)}`);
    const result = await UserModel.update(userId, updateData);
    
    if (!result) {
      logger.error(`更新用户信息失败: ID=${userId}, 数据库操作失败`);
      return error(res, '更新用户信息失败', 500);
    }
    
    // 获取更新后的用户信息
    const updatedUser = await UserModel.findById(userId);
    logger.info(`更新用户信息成功: ID=${userId}`);
    
    return success(res, updatedUser, '更新用户信息成功');
  } catch (err) {
    logger.error(`更新用户信息失败: ${err.message}, 堆栈: ${err.stack}`);
    return error(res, '更新用户信息失败', 500);
  }
};

/**
 * 绑定手机号
 */
const bindPhone = async (req, res) => {
  try {
    const userId = req.user.id;
    const { phone, code } = req.body;
    
    // 实际应用中这里需要验证短信验证码
    // 此处简化处理，假设验证码已通过
    
    const result = await UserModel.bindPhone(userId, phone);
    
    if (result) {
      return success(res, null, '绑定手机号成功');
    } else {
      return error(res, '绑定手机号失败', 400);
    }
  } catch (err) {
    logger.error(`绑定手机号失败: ${err.message}`);
    return error(res, '绑定手机号失败', 500);
  }
};

/**
 * 获取用户列表（管理端）
 */
const getUserList = async (req, res) => {
  try {
    const { page = 1, limit = 10, keyword = '' } = req.query;
    
    const result = await UserModel.list({ page, limit, keyword });
    
    return success(res, result, '获取用户列表成功');
  } catch (err) {
    logger.error(`获取用户列表失败: ${err.message}`);
    return error(res, '获取用户列表失败', 500);
  }
};

/**
 * 获取用户详情（管理端）
 */
const getUserDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    const user = await UserModel.findById(id);
    if (!user) {
      return error(res, '用户不存在', 404);
    }
    
    return success(res, user, '获取用户详情成功');
  } catch (err) {
    logger.error(`获取用户详情失败: ${err.message}`);
    return error(res, '获取用户详情失败', 500);
  }
};

/**
 * 设置用户状态（管理端）
 */
const setUserStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    if (status !== 0 && status !== 1) {
      return error(res, '无效的状态值', 400);
    }
    
    const result = await UserModel.setStatus(id, status);
    
    if (result) {
      return success(res, null, '设置用户状态成功');
    } else {
      return error(res, '设置用户状态失败，用户可能不存在', 400);
    }
  } catch (err) {
    logger.error(`设置用户状态失败: ${err.message}`);
    return error(res, '设置用户状态失败', 500);
  }
};

module.exports = {
  getUserInfo,
  updateUserInfo,
  bindPhone,
  getUserList,
  getUserDetail,
  setUserStatus,
  getUserProfile: async (req, res) => {
    return success(res, { message: '用户控制器已设置' });
  }
}; 