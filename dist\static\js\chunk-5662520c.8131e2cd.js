(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5662520c"],{"02d0":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"app-container"},[e("div",{staticClass:"filter-container"},[e("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"商品名称/标题",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleFilter.apply(null,arguments)}},model:{value:t.listQuery.keyword,callback:function(e){t.$set(t.listQuery,"keyword",e)},expression:"listQuery.keyword"}}),e("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"分类",clearable:""},model:{value:t.listQuery.category_id,callback:function(e){t.$set(t.listQuery,"category_id",e)},expression:"listQuery.category_id"}},t._l(t.categoryOptions,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),e("el-select",{staticClass:"filter-item",staticStyle:{width:"130px"},attrs:{placeholder:"状态",clearable:""},model:{value:t.listQuery.status,callback:function(e){t.$set(t.listQuery,"status",e)},expression:"listQuery.status"}},t._l(t.statusOptions,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),e("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleFilter}},[t._v("搜索")]),e("el-button",{staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-plus"},on:{click:t.handleCreate}},[t._v("新增商品")])],1),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],attrs:{data:t.list,"element-loading-text":"正在加载...",border:"",fit:"","highlight-current-row":""}},[e("el-table-column",{attrs:{label:"ID",prop:"id",align:"center",width:"80"}}),e("el-table-column",{attrs:{label:"封面",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("el-image",{staticStyle:{width:"60px",height:"60px"},attrs:{src:t.formatImageUrl(a.cover),"preview-src-list":[t.formatImageUrl(a.cover)],fit:"cover"}},[e("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[e("i",{staticClass:"el-icon-picture-outline"})])])]}}])}),e("el-table-column",{attrs:{label:"标题",prop:"title",align:"center","min-width":"180"}}),e("el-table-column",{attrs:{label:"分类",prop:"categoryName",align:"center",width:"120"}}),e("el-table-column",{attrs:{label:"价格",align:"center",width:"120"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("span",[t._v(t._s(a.price)+" 元")])]}}])}),e("el-table-column",{attrs:{label:"库存",prop:"stock",align:"center",width:"80"}}),e("el-table-column",{attrs:{label:"销量",prop:"sales",align:"center",width:"80"}}),e("el-table-column",{attrs:{label:"推荐",align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[a.isRecommend?e("el-tag",{attrs:{type:"success"}},[t._v("是")]):e("el-tag",{attrs:{type:"info"}},[t._v("否")])]}}])}),e("el-table-column",{attrs:{label:"状态",align:"center",width:"100"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("el-tag",{attrs:{type:1===a.status?"success":"info"}},[t._v(" "+t._s(1===a.status?"上架":"下架")+" ")])]}}])}),e("el-table-column",{attrs:{label:"操作",align:"center",width:"280","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function({row:a}){return[e("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.handleView(a)}}},[t._v("查看")]),e("el-button",{attrs:{type:"success",size:"mini"},on:{click:function(e){return t.handleUpdate(a)}}},[t._v("编辑")]),1===a.status?e("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(e){return t.handleStatusChange(a,0)}}},[t._v("下架")]):e("el-button",{attrs:{size:"mini",type:"info"},on:{click:function(e){return t.handleStatusChange(a,1)}}},[t._v("上架")]),e("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(e){return t.handleDelete(a)}}},[t._v("删除")])]}}])})],1),e("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.listQuery.page,limit:t.listQuery.limit},on:{"update:page":function(e){return t.$set(t.listQuery,"page",e)},"update:limit":function(e){return t.$set(t.listQuery,"limit",e)},pagination:t.getList}})],1)},l=[],n=(a("14d9"),a("c40e")),s=a("333d"),r={name:"GoodsList",components:{Pagination:s["a"]},data(){return{list:null,total:0,listLoading:!0,listQuery:{page:1,limit:10,keyword:void 0,category_id:void 0,status:void 0},statusOptions:[{label:"上架",value:1},{label:"下架",value:0}],categoryOptions:[{label:"数码产品",value:1},{label:"家居用品",value:2},{label:"美妆护肤",value:3},{label:"食品饮料",value:4}]}},created(){this.getList()},methods:{formatImageUrl:n["c"],getList(){this.listLoading=!0,Object(n["e"])(this.listQuery).then(t=>{console.log("商品列表响应:",t),t.data&&t.data.list?(this.list=t.data.list,this.total=t.data.pagination?t.data.pagination.total:0,console.log("处理后的商品列表:",this.list)):t.data&&Array.isArray(t.data)?(this.list=t.data,this.total=t.data.length,console.log("处理后的商品列表(测试数据):",this.list)):(this.list=[],this.total=0,console.warn("未识别的数据格式:",t)),this.listLoading=!1}).catch(t=>{console.error("获取商品列表失败:",t),this.list=[],this.total=0,this.listLoading=!1})},handleFilter(){this.listQuery.page=1,this.getList()},handleCreate(){this.$router.push("/mall/goods/create")},handleUpdate(t){this.$router.push("/mall/goods/edit/"+t.id)},handleView(t){this.$router.push("/mall/goods/detail/"+t.id)},handleStatusChange(t,e){Object(n["g"])(t.id,{status:e}).then(t=>{this.$message.success("状态更新成功"),this.getList()}).catch(()=>{this.$message.error("状态更新失败")})},handleDelete(t){console.log("准备删除商品:",t),t&&t.id?this.$confirm("确认要删除这个商品吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{console.log("正在调用删除API，商品ID: "+t.id),Object(n["b"])(t.id).then(t=>{console.log("删除商品成功，响应:",t),this.$message.success("删除成功"),this.getList()}).catch(t=>{console.error("删除商品失败:",t),t.response&&404===t.response.status?(this.$message.error("商品不存在或已被删除"),this.getList()):this.$message.error("删除失败: "+(t.message||"未知错误"))})}).catch(()=>{console.log("用户取消了删除操作")}):this.$message.error("无效的商品ID")}}},o=r,u=a("2877"),c=Object(u["a"])(o,i,l,!1,null,null,null);e["default"]=c.exports},"330e":function(t,e,a){"use strict";a("34b0")},"333d":function(t,e,a){"use strict";var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"pagination-container",class:{hidden:t.hidden}},[e("el-pagination",t._b({attrs:{background:t.background,"current-page":t.currentPage,"page-size":t.pageSize,layout:t.layout,"page-sizes":t.pageSizes,total:t.total},on:{"update:currentPage":function(e){t.currentPage=e},"update:current-page":function(e){t.currentPage=e},"update:pageSize":function(e){t.pageSize=e},"update:page-size":function(e){t.pageSize=e},"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}},"el-pagination",t.$attrs,!1))],1)},l=[],n={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get(){return this.page},set(t){this.$emit("update:page",t)}},pageSize:{get(){return this.limit},set(t){this.$emit("update:limit",t)}}},methods:{handleSizeChange(t){this.$emit("pagination",{page:this.currentPage,limit:t})},handleCurrentChange(t){this.$emit("pagination",{page:t,limit:this.pageSize})}}},s=n,r=(a("330e"),a("2877")),o=Object(r["a"])(s,i,l,!1,null,"11252b03",null);e["a"]=o.exports},"34b0":function(t,e,a){},c40e:function(t,e,a){"use strict";a.d(e,"e",(function(){return l})),a.d(e,"d",(function(){return n})),a.d(e,"a",(function(){return s})),a.d(e,"f",(function(){return r})),a.d(e,"g",(function(){return o})),a.d(e,"b",(function(){return u})),a.d(e,"c",(function(){return c}));var i=a("b775");function l(t){return Object(i["a"])({url:"/api/v1/admin/goods/list",method:"get",params:t})}function n(t){return Object(i["a"])({url:"/api/v1/admin/goods/detail/"+t,method:"get"})}function s(t){return Object(i["a"])({url:"/api/v1/admin/goods/create",method:"post",data:t})}function r(t,e){return Object(i["a"])({url:"/api/v1/admin/goods/update/"+t,method:"put",data:e})}function o(t,e){return Object(i["a"])({url:"/api/v1/admin/goods/status/"+t,method:"put",data:e})}function u(t){return console.log(`删除商品 ID: ${t}, 请求URL: /api/v1/admin/goods/delete/${t}`),Object(i["a"])({url:"/api/v1/admin/goods/delete/"+t,method:"delete"})}function c(t){return t?t.startsWith("http://")||t.startsWith("https://")||t.startsWith("/")?t:"/uploads/"+t:"/uploads/default-goods.jpg"}}}]);