const axios = require('axios');

// 测试后端API是否正常工作
async function testBackendAPI() {
  const baseURL = 'http://localhost:4000';
  
  console.log('🔍 测试后端API连接...');
  
  try {
    // 1. 测试基础连接
    console.log('\n1. 测试基础连接...');
    const healthResponse = await axios.get(`${baseURL}/`);
    console.log('✅ 基础连接成功:', healthResponse.data);
    
    // 2. 测试管理员登录API
    console.log('\n2. 测试管理员登录API...');
    const loginResponse = await axios.post(`${baseURL}/api/v1/admin/auth/admin-login`, {
      username: 'mrx0927',
      password: 'hh20250701'
    });
    
    if (loginResponse.data.code === 200) {
      console.log('✅ 管理员登录成功');
      console.log('Token:', loginResponse.data.data.token.substring(0, 20) + '...');
      
      // 3. 测试需要认证的API
      console.log('\n3. 测试需要认证的API...');
      const token = loginResponse.data.data.token;
      
      const userListResponse = await axios.get(`${baseURL}/api/v1/admin/user/list`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('✅ 用户列表API测试成功');
      console.log('用户数量:', userListResponse.data.data?.total || 0);
      
    } else {
      console.log('❌ 管理员登录失败:', loginResponse.data.message);
    }
    
  } catch (error) {
    console.log('❌ API测试失败:');
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('错误信息:', error.response.data);
    } else if (error.request) {
      console.log('网络错误: 无法连接到后端服务器');
      console.log('请确保后端服务器运行在 http://localhost:4000');
    } else {
      console.log('错误:', error.message);
    }
  }
}

// 运行测试
testBackendAPI();
