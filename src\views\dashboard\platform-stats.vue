<template>
  <div class="platform-stats-container">
    <el-card class="stats-card">
      <div slot="header" class="clearfix">
        <span>平台数据概览</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshData">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>

      <!-- 数据概览卡片 -->
      <el-row :gutter="20" class="overview-cards">
        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-icon user-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="card-content">
              <div class="card-title">用户总数</div>
              <div class="card-value">{{ overview.users.total_users || 0 }}</div>
              <div class="card-footer">
                <span>今日新增: </span>
                <span class="highlight">{{ overview.users.today_new_users || 0 }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-icon wifi-icon">
              <i class="el-icon-connection"></i>
            </div>
            <div class="card-content">
              <div class="card-title">WiFi码总数</div>
              <div class="card-value">{{ overview.wifi.total_wifi_codes || 0 }}</div>
              <div class="card-footer">
                <span>今日新增: </span>
                <span class="highlight">{{ overview.wifi.today_new_codes || 0 }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-icon order-icon">
              <i class="el-icon-shopping-cart-full"></i>
            </div>
            <div class="card-content">
              <div class="card-title">订单总数</div>
              <div class="card-value">{{ overview.orders.total_orders || 0 }}</div>
              <div class="card-footer">
                <span>已支付: </span>
                <span class="highlight">{{ overview.orders.paid_orders || 0 }}</span>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card shadow="hover" class="overview-card">
            <div class="card-icon revenue-icon">
              <i class="el-icon-money"></i>
            </div>
            <div class="card-content">
              <div class="card-title">总收益</div>
              <div class="card-value">¥{{ formatNumber(overview.orders.total_revenue || 0) }}</div>
              <div class="card-footer">
                <span>今日收益: </span>
                <span class="highlight">¥{{ formatNumber(overview.orders.today_revenue || 0) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 收益趋势图 -->
      <el-card class="chart-card">
        <div slot="header" class="clearfix">
          <span>收益趋势</span>
          <el-radio-group v-model="revenuePeriod" size="mini" style="float: right">
            <el-radio-button label="7d">7天</el-radio-button>
            <el-radio-button label="30d">30天</el-radio-button>
            <el-radio-button label="90d">90天</el-radio-button>
          </el-radio-group>
        </div>
        <div class="chart-container" ref="revenueChart"></div>
      </el-card>

      <!-- 用户增长趋势图 -->
      <el-card class="chart-card">
        <div slot="header" class="clearfix">
          <span>用户增长趋势</span>
          <el-radio-group v-model="userPeriod" size="mini" style="float: right">
            <el-radio-button label="7d">7天</el-radio-button>
            <el-radio-button label="30d">30天</el-radio-button>
            <el-radio-button label="90d">90天</el-radio-button>
          </el-radio-group>
        </div>
        <div class="chart-container" ref="userChart"></div>
      </el-card>

      <!-- 业务类型分布 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>业务类型分布</span>
            </div>
            <div class="chart-container" ref="businessChart"></div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="clearfix">
              <span>地区分布</span>
            </div>
            <div class="chart-container" ref="regionChart"></div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getPlatformOverview, getRevenueTrend, getUserGrowth, getBusinessTypeStats, getRegionStats } from '@/api/platform'

export default {
  name: 'PlatformStats',
  data () {
    return {
      loading: false,
      overview: {
        users: {
          total_users: 0,
          today_new_users: 0
        },
        wifi: {
          total_wifi_codes: 0,
          today_new_codes: 0
        },
        orders: {
          total_orders: 0,
          paid_orders: 0,
          total_revenue: 0,
          today_revenue: 0
        },
        teams: {
          total_teams: 0,
          avg_team_size: 0
        }
      },
      revenuePeriod: '7d',
      userPeriod: '30d',
      revenueChart: null,
      userChart: null,
      businessChart: null,
      regionChart: null,
      revenueData: [],
      userData: [],
      businessData: [],
      regionData: []
    }
  },
  watch: {
    revenuePeriod () {
      this.fetchRevenueTrend()
    },
    userPeriod () {
      this.fetchUserGrowth()
    }
  },
  mounted () {
    this.initData()
  },
  methods: {
    initData () {
      this.fetchOverview()
      this.fetchRevenueTrend()
      this.fetchUserGrowth()
      this.fetchBusinessTypeStats()
      this.fetchRegionStats()
    },
    refreshData () {
      this.initData()
      this.$message.success('数据已刷新')
    },
    formatNumber (num) {
      return parseFloat(num).toFixed(2)
    },
    async fetchOverview () {
      try {
        this.loading = true
        const { data } = await getPlatformOverview()

        // 直接使用后端返回的数据结构
        if (data) {
          this.overview = data
        }
      } catch (error) {
        console.error('获取平台概览失败:', error)
        this.$message.error('获取平台概览失败')
      } finally {
        this.loading = false
      }
    },
    async fetchRevenueTrend () {
      try {
        const { data } = await getRevenueTrend({ period: this.revenuePeriod })
        this.revenueData = Array.isArray(data) ? data : []
        this.$nextTick(() => {
          this.renderRevenueChart()
        })
      } catch (error) {
        console.error('获取收益趋势失败:', error)
        this.$message.error('获取收益趋势失败')
        this.revenueData = []
      }
    },
    async fetchUserGrowth () {
      try {
        const { data } = await getUserGrowth({ period: this.userPeriod })
        this.userData = Array.isArray(data) ? data : []
        this.$nextTick(() => {
          this.renderUserChart()
        })
      } catch (error) {
        console.error('获取用户增长趋势失败:', error)
        this.$message.error('获取用户增长趋势失败')
        this.userData = []
      }
    },
    async fetchBusinessTypeStats () {
      try {
        const { data } = await getBusinessTypeStats()
        this.businessData = Array.isArray(data) ? data : []
        this.$nextTick(() => {
          this.renderBusinessChart()
        })
      } catch (error) {
        console.error('获取业务类型统计失败:', error)
        this.$message.error('获取业务类型统计失败')
        this.businessData = []
      }
    },
    async fetchRegionStats () {
      try {
        const { data } = await getRegionStats()
        this.regionData = Array.isArray(data) ? data : []
        this.$nextTick(() => {
          this.renderRegionChart()
        })
      } catch (error) {
        console.error('获取地区统计失败:', error)
        this.$message.error('获取地区统计失败')
        this.regionData = []
      }
    },
    renderRevenueChart () {
      if (!this.$refs.revenueChart) {
        return
      }

      if (!this.revenueChart) {
        this.revenueChart = echarts.init(this.$refs.revenueChart)
      }

      if (!Array.isArray(this.revenueData) || this.revenueData.length === 0) {
        // 显示空数据提示
        const option = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#999',
              fontSize: 14
            }
          }
        }
        this.revenueChart.setOption(option)
        return
      }

      const dates = this.revenueData.map(item => item.date)
      const platformRevenue = this.revenueData.map(item => item.platform_amount || 0)
      const leaderRevenue = this.revenueData.map(item => item.leader_amount || 0)
      const userRevenue = this.revenueData.map(item => item.user_amount || 0)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['平台收益', '团长收益', '用户收益']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '平台收益',
            type: 'line',
            stack: 'Total',
            data: platformRevenue
          },
          {
            name: '团长收益',
            type: 'line',
            stack: 'Total',
            data: leaderRevenue
          },
          {
            name: '用户收益',
            type: 'line',
            stack: 'Total',
            data: userRevenue
          }
        ]
      }
      
      this.revenueChart.setOption(option)
    },
    renderUserChart () {
      if (!this.$refs.userChart) {
        return
      }

      if (!this.userChart) {
        this.userChart = echarts.init(this.$refs.userChart)
      }

      if (!Array.isArray(this.userData) || this.userData.length === 0) {
        // 显示空数据提示
        const option = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#999',
              fontSize: 14
            }
          }
        }
        this.userChart.setOption(option)
        return
      }

      const dates = this.userData.map(item => item.date)
      const newUsers = this.userData.map(item => item.new_users || 0)
      const newLeaders = this.userData.map(item => item.new_leaders || 0)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['新增用户', '新增团长']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '新增用户',
            type: 'bar',
            data: newUsers
          },
          {
            name: '新增团长',
            type: 'bar',
            data: newLeaders
          }
        ]
      }
      
      this.userChart.setOption(option)
    },
    renderBusinessChart () {
      if (!this.$refs.businessChart) {
        return
      }

      if (!this.businessChart) {
        this.businessChart = echarts.init(this.$refs.businessChart)
      }

      if (!Array.isArray(this.businessData) || this.businessData.length === 0) {
        // 显示空数据提示
        const option = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#999',
              fontSize: 14
            }
          }
        }
        this.businessChart.setOption(option)
        return
      }

      const businessTypes = this.businessData.map(item => {
        const typeMap = {
          wifi_share: 'WiFi分享',
          goods_sale: '商品销售',
          advertisement: '广告收益'
        }
        return typeMap[item.type || item.business_type] || item.name || item.business_type || '未知类型'
      })

      const revenueData = this.businessData.map(item => ({
        value: item.total_revenue || 0,
        name: businessTypes[this.businessData.indexOf(item)]
      }))

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: businessTypes
        },
        series: [
          {
            name: '业务收益',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: revenueData
          }
        ]
      }

      this.businessChart.setOption(option)
    },
    renderRegionChart () {
      if (!this.$refs.regionChart) {
        return
      }

      if (!this.regionChart) {
        this.regionChart = echarts.init(this.$refs.regionChart)
      }

      if (!Array.isArray(this.regionData) || this.regionData.length === 0) {
        // 显示空数据提示
        const option = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#999',
              fontSize: 14
            }
          }
        }
        this.regionChart.setOption(option)
        return
      }

      const regions = this.regionData.map(item => item.region_name || item.region || '未知地区')
      const userCounts = this.regionData.map(item => item.total_users || item.user_count || 0)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const item = params[0]
            const regionItem = this.regionData[item.dataIndex]
            return `${item.name}<br/>
                    用户数量: ${item.value}<br/>
                    团队数量: ${regionItem?.total_teams || regionItem?.team_count || 0}<br/>
                    总收益: ¥${(regionItem?.total_revenue || 0).toFixed(2)}`
          }.bind(this)
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: regions
        },
        series: [
          {
            name: '用户数量',
            type: 'bar',
            data: userCounts,
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }

      this.regionChart.setOption(option)
    }
  }
}
</script>

<style scoped>
.platform-stats-container {
  padding: 20px;
}

.stats-card {
  margin-bottom: 20px;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
  display: flex;
  align-items: center;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.card-icon i {
  font-size: 30px;
  color: white;
}

.user-icon {
  background-color: #409EFF;
}

.wifi-icon {
  background-color: #67C23A;
}

.order-icon {
  background-color: #E6A23C;
}

.revenue-icon {
  background-color: #F56C6C;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #909399;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin: 5px 0;
}

.card-footer {
  font-size: 12px;
  color: #909399;
}

.highlight {
  color: #F56C6C;
  font-weight: bold;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
}
</style>
