(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7d3b2848"],{"330e":function(e,t,a){"use strict";a("34b0")},"333d":function(e,t,a){"use strict";var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[t("el-pagination",e._b({attrs:{background:e.background,"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,"page-sizes":e.pageSizes,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1))],1)},n=[],i={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get(){return this.page},set(e){this.$emit("update:page",e)}},pageSize:{get(){return this.limit},set(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange(e){this.$emit("pagination",{page:this.currentPage,limit:e})},handleCurrentChange(e){this.$emit("pagination",{page:e,limit:this.pageSize})}}},s=i,r=(a("330e"),a("2877")),o=Object(r["a"])(s,l,n,!1,null,"11252b03",null);t["a"]=o.exports},"34b0":function(e,t,a){},"3de7":function(e,t,a){"use strict";a("431e")},"431e":function(e,t,a){},6724:function(e,t,a){"use strict";a("8d41");const l={bind(e,t){e.addEventListener("click",a=>{const l=Object.assign({},t.value),n=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},l),i=n.ele;if(i){i.style.position="relative",i.style.overflow="hidden";const e=i.getBoundingClientRect();let t=i.querySelector(".waves-ripple");switch(t?t.className="waves-ripple":(t=document.createElement("span"),t.className="waves-ripple",t.style.height=t.style.width=Math.max(e.width,e.height)+"px",i.appendChild(t)),n.type){case"center":t.style.top=e.height/2-t.offsetHeight/2+"px",t.style.left=e.width/2-t.offsetWidth/2+"px";break;default:t.style.top=(a.pageY-e.top-t.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",t.style.left=(a.pageX-e.left-t.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return t.style.backgroundColor=n.color,t.className="waves-ripple z-active",!1}},!1)}};var n=l;const i=function(e){e.directive("waves",n)};window.Vue&&(window.waves=n,Vue.use(i)),n.install=i;t["a"]=n},"8d41":function(e,t,a){},af1c:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e._self._c;return t("div",{staticClass:"wallet-list"},[t("div",{staticClass:"filter-container"},[t("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"用户名"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleFilter.apply(null,arguments)}},model:{value:e.listQuery.username,callback:function(t){e.$set(e.listQuery,"username",t)},expression:"listQuery.username"}}),t("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"手机号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleFilter.apply(null,arguments)}},model:{value:e.listQuery.phone,callback:function(t){e.$set(e.listQuery,"phone",t)},expression:"listQuery.phone"}}),t("el-select",{staticClass:"filter-item",staticStyle:{width:"120px"},attrs:{placeholder:"状态",clearable:""},model:{value:e.listQuery.status,callback:function(t){e.$set(e.listQuery,"status",t)},expression:"listQuery.status"}},e._l(e.statusOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),t("el-button",{directives:[{name:"waves",rawName:"v-waves"}],staticClass:"filter-item",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleFilter}},[e._v(" 搜索 ")])],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],key:e.tableKey,staticStyle:{width:"100%"},attrs:{data:e.list,border:"",fit:"","highlight-current-row":""}},[t("el-table-column",{attrs:{label:"用户ID",prop:"user_id",sortable:"custom",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("span",[e._v(e._s(a.user_id))])]}}])}),t("el-table-column",{attrs:{label:"用户名",width:"150px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("span",[e._v(e._s(a.username))])]}}])}),t("el-table-column",{attrs:{label:"手机号",width:"150px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("span",[e._v(e._s(a.phone))])]}}])}),t("el-table-column",{attrs:{label:"钱包余额",width:"150px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("span",{staticClass:"balance"},[e._v(e._s(e.formatCurrency(a.balance)))])]}}])}),t("el-table-column",{attrs:{label:"冻结金额",width:"150px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("span",{staticClass:"frozen"},[e._v(e._s(e.formatCurrency(a.frozen_amount)))])]}}])}),t("el-table-column",{attrs:{label:"累计收入",width:"150px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("span",{staticClass:"income"},[e._v(e._s(e.formatCurrency(a.total_income)))])]}}])}),t("el-table-column",{attrs:{label:"累计支出",width:"150px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("span",{staticClass:"expense"},[e._v(e._s(e.formatCurrency(a.total_expense)))])]}}])}),t("el-table-column",{attrs:{label:"状态",width:"100px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("el-tag",{attrs:{type:1===a.status?"success":"danger"}},[e._v(" "+e._s(1===a.status?"正常":"冻结")+" ")])]}}])}),t("el-table-column",{attrs:{label:"更新时间",width:"180px",align:"center"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("span",[e._v(e._s(a.updated_at))])]}}])}),t("el-table-column",{attrs:{label:"操作",align:"center",width:"230","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.handleDetail(a)}}},[e._v(" 详情 ")]),t("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(t){return e.handleAdjust(a)}}},[e._v(" 调整余额 ")]),t("el-button",{attrs:{size:"mini",type:"info"},on:{click:function(t){return e.handleTransactions(a)}}},[e._v(" 交易记录 ")])]}}])})],1),t("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.listQuery.page,limit:e.listQuery.limit},on:{"update:page":function(t){return e.$set(e.listQuery,"page",t)},"update:limit":function(t){return e.$set(e.listQuery,"limit",t)},pagination:e.getList}}),t("el-dialog",{attrs:{title:"调整钱包余额",visible:e.dialogFormVisible},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[t("el-form",{ref:"dataForm",staticStyle:{width:"400px","margin-left":"50px"},attrs:{rules:e.rules,model:e.temp,"label-position":"left","label-width":"100px"}},[t("el-form-item",{attrs:{label:"用户名"}},[t("span",[e._v(e._s(e.temp.username))])]),t("el-form-item",{attrs:{label:"当前余额"}},[t("span",[e._v(e._s(e.formatCurrency(e.temp.balance)))])]),t("el-form-item",{attrs:{label:"调整类型",prop:"adjust_type"}},[t("el-select",{staticClass:"filter-item",attrs:{placeholder:"请选择调整类型"},model:{value:e.temp.adjust_type,callback:function(t){e.$set(e.temp,"adjust_type",t)},expression:"temp.adjust_type"}},e._l(e.adjustTypeOptions,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"调整金额",prop:"amount"}},[t("el-input",{attrs:{type:"number",placeholder:"请输入调整金额"},model:{value:e.temp.amount,callback:function(t){e.$set(e.temp,"amount",e._n(t))},expression:"temp.amount"}})],1),t("el-form-item",{attrs:{label:"调整原因",prop:"reason"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入调整原因"},model:{value:e.temp.reason,callback:function(t){e.$set(e.temp,"reason",t)},expression:"temp.reason"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(t){e.dialogFormVisible=!1}}},[e._v(" 取消 ")]),t("el-button",{attrs:{type:"primary"},on:{click:e.adjustBalance}},[e._v(" 确认 ")])],1)],1)],1)},n=[],i=(a("14d9"),a("b933")),s=a("6724"),r=a("333d"),o={name:"WalletList",components:{Pagination:r["a"]},directives:{waves:s["a"]},data(){return{tableKey:0,list:[],total:0,listLoading:!0,listQuery:{page:1,limit:20,username:void 0,phone:void 0,status:void 0},statusOptions:[{label:"正常",value:1},{label:"冻结",value:0}],adjustTypeOptions:[{label:"增加余额",value:"increase"},{label:"减少余额",value:"decrease"},{label:"冻结金额",value:"freeze"},{label:"解冻金额",value:"unfreeze"}],temp:{user_id:void 0,username:"",balance:0,adjust_type:"",amount:0,reason:""},dialogFormVisible:!1,rules:{adjust_type:[{required:!0,message:"请选择调整类型",trigger:"change"}],amount:[{required:!0,message:"请输入调整金额",trigger:"blur"},{type:"number",min:.01,message:"金额必须大于0",trigger:"blur"}],reason:[{required:!0,message:"请输入调整原因",trigger:"blur"}]}}},created(){this.getList()},methods:{getList(){this.listLoading=!0,Object(i["e"])(this.listQuery).then(e=>{this.list=e.data.items||[],this.total=e.data.pagination&&e.data.pagination.total||e.data.total||0,this.listLoading=!1}).catch(e=>{console.error("获取钱包列表失败:",e),this.list=[],this.total=0,this.listLoading=!1,this.$message.error("获取钱包列表失败")})},handleFilter(){this.listQuery.page=1,this.getList()},handleDetail(e){this.$router.push("/wallet/detail/"+e.user_id)},handleTransactions(e){this.$router.push("/wallet/transactions/"+e.user_id)},handleAdjust(e){this.temp={user_id:e.user_id,username:e.username,balance:e.balance,adjust_type:"",amount:0,reason:""},this.dialogFormVisible=!0,this.$nextTick(()=>{this.$refs["dataForm"].clearValidate()})},adjustBalance(){this.$refs["dataForm"].validate(e=>{e&&Object(i["b"])(this.temp).then(()=>{this.dialogFormVisible=!1,this.$notify({title:"成功",message:"钱包余额调整成功",type:"success",duration:2e3}),this.getList()})})},formatCurrency(e){return"¥ "+parseFloat(e).toFixed(2)}}},u=o,c=(a("3de7"),a("2877")),d=Object(c["a"])(u,l,n,!1,null,"1b569fa2",null);t["default"]=d.exports},b933:function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"d",(function(){return i})),a.d(t,"a",(function(){return s})),a.d(t,"c",(function(){return r})),a.d(t,"b",(function(){return o}));var l=a("b775");function n(e){return Object(l["a"])({url:"/api/v1/admin/wallet/list",method:"get",params:e})}function i(e){return Object(l["a"])({url:"/api/v1/admin/wallet/detail/"+e,method:"get"})}function s(e,t){return Object(l["a"])({url:"/api/v1/admin/wallet/adjust/"+e,method:"post",data:t})}function r(e,t){return Object(l["a"])({url:"/api/v1/admin/wallet/transactions/"+e,method:"get",params:t})}function o(e){return Object(l["a"])({url:"/api/v1/admin/wallet/adjust",method:"post",data:e})}}}]);