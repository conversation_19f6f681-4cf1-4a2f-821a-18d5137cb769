<template>
  <div class="team-form-container">
    <el-card>
      <div slot="header">
        {{ isEdit ? '编辑团队' : '创建团队' }}
      </div>
      
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" v-loading="loading">
        <el-form-item label="团队名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入团队名称" maxlength="50"></el-input>
        </el-form-item>
        
        <el-form-item label="选择团长" prop="leader_id" v-if="!isEdit">
          <el-select 
            v-model="form.leader_id" 
            placeholder="请选择团长"
            filterable
            remote
            :remote-method="searchUsers"
            :loading="userLoading">
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="`${user.nickname} (${user.phone})`"
              :value="user.id">
              <div class="user-option">
                <el-avatar :size="24" :src="user.avatar || '/img/default-avatar.png'"></el-avatar>
                <span class="user-info">{{ user.nickname }} - {{ user.phone }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="团队简介" prop="description">
          <el-input 
            type="textarea" 
            v-model="form.description" 
            placeholder="请输入团队简介"
            :rows="4"
            maxlength="200"
            show-word-limit>
          </el-input>
        </el-form-item>
        
        <el-form-item label="分润比例" prop="profit_ratio">
          <el-input-number 
            v-model="form.profit_ratio" 
            :min="0" 
            :max="100" 
            :precision="2"
            :step="0.1">
          </el-input-number>
          <span class="input-suffix">%</span>
        </el-form-item>
        
        <el-form-item label="团队状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSubmit">{{ isEdit ? '保存修改' : '创建团队' }}</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { createTeam, updateTeam, getTeamDetail } from '@/api/team'
import { searchUsers } from '@/api/user'

export default {
  name: 'TeamForm',
  data() {
    return {
      loading: false,
      userLoading: false,
      isEdit: false,
      teamId: null,
      form: {
        name: '',
        leader_id: null,
        description: '',
        profit_ratio: 10,
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入团队名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        leader_id: [
          { required: true, message: '请选择团长', trigger: 'change' }
        ],
        profit_ratio: [
          { required: true, message: '请设置分润比例', trigger: 'blur' }
        ]
      },
      userList: []
    }
  },
  created() {
    this.teamId = this.$route.params.id
    this.isEdit = !!this.teamId
    if (this.isEdit) {
      this.fetchTeamDetail()
    }
  },
  methods: {
    // 获取团队详情
    async fetchTeamDetail() {
      this.loading = true
      try {
        const res = await getTeamDetail(this.teamId)
        if (res.code === 0) {
          const data = res.data
          this.form = {
            name: data.name,
            leader_id: data.leader_id,
            description: data.description || '',
            profit_ratio: data.profit_ratio || 10,
            status: data.status
          }
          // 如果是编辑模式，加载团长信息
          if (data.leader_id) {
            this.userList = [{
              id: data.leader_id,
              nickname: data.leader_name,
              phone: data.leader_phone,
              avatar: data.leader_avatar
            }]
          }
        }
      } catch (error) {
        this.$message.error('获取团队信息失败')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索用户
    async searchUsers(query) {
      if (query) {
        this.userLoading = true
        try {
          const res = await searchUsers({ keyword: query })
          if (res.code === 0) {
            this.userList = res.data.list || []
          }
        } catch (error) {
          this.$message.error('搜索用户失败')
        } finally {
          this.userLoading = false
        }
      }
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.loading = true
          try {
            let res
            if (this.isEdit) {
              res = await updateTeam(this.teamId, this.form)
            } else {
              res = await createTeam(this.form)
            }
            
            if (res.code === 0) {
              this.$message.success(this.isEdit ? '修改成功' : '创建成功')
              this.$router.push('/team/list')
            }
          } catch (error) {
            this.$message.error(this.isEdit ? '修改失败' : '创建失败')
          } finally {
            this.loading = false
          }
        }
      })
    },
    
    // 取消
    handleCancel() {
      this.$router.back()
    }
  }
}
</script>

<style lang="scss" scoped>
.team-form-container {
  padding: 20px;
  
  .el-card {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .user-option {
    display: flex;
    align-items: center;
    
    .user-info {
      margin-left: 10px;
    }
  }
  
  .input-suffix {
    margin-left: 10px;
    color: #909399;
  }
}
</style> 