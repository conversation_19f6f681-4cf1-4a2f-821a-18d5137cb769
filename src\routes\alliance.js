const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middlewares/auth');
const { success, error } = require('../utils/response');
const db = require('../database');

/**
 * 联盟团长申请接口
 * POST /api/v1/client/alliance/apply
 */
router.post('/apply', verifyToken, async (req, res) => {
  try {
    const { name, contact, phone, email, area, description } = req.body;
    const userId = req.user.id;
    
    // 简单验证
    if (!name || !contact || !phone) {
      return error(res, '缺少必要参数', 400);
    }
    
    // 检查用户是否已经申请过
    const [existingApply] = await db.query(
      'SELECT * FROM team_apply WHERE user_id = ?',
      [userId]
    );
    
    if (existingApply && existingApply.length > 0) {
      return error(res, '您已提交过申请，请勿重复提交', 400);
    }
    
    // 插入申请记录
    const result = await db.query(
      `INSERT INTO team_apply
       (user_id, name, contact, phone, email, area, description, status, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, 0, NOW())`,
      [userId, name, contact, phone, email, area, description]
    );
    
    console.log('团长申请提交成功:', {
      userId,
      name,
      contact,
      phone,
      email,
      area,
      description
    });
    console.log('数据库插入结果:', result);

    return success(res, {
      id: result.insertId,
      status: 'pending'
    }, '申请已提交');
  } catch (err) {
    console.error('团长申请提交失败:', err);
    return error(res, '申请提交失败: ' + err.message, 500);
  }
});

/**
 * 查询团长申请状态
 * GET /api/v1/client/alliance/status
 */
router.get('/status', verifyToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 查询申请记录
    const [result] = await db.query(
      'SELECT * FROM team_apply WHERE user_id = ? ORDER BY created_at DESC LIMIT 1',
      [userId]
    );
    
    if (!result || result.length === 0) {
      return success(res, { 
        hasApplied: false,
        status: null
      }, '尚未提交申请');
    }
    
    const application = result[0];
    let status;
    
    // 状态转换：0=待审核，1=已通过，2=已拒绝
    switch(application.status) {
      case 0:
        status = 'pending';
        break;
      case 1:
        status = 'approved';
        break;
      case 2:
        status = 'rejected';
        break;
      default:
        status = 'unknown';
    }
    
    return success(res, { 
      hasApplied: true,
      status,
      appliedAt: application.created_at,
      updatedAt: application.updated_at
    }, '获取申请状态成功');
  } catch (err) {
    console.error('获取团长申请状态失败:', err);
    return error(res, '获取申请状态失败: ' + err.message, 500);
  }
});

module.exports = router; 