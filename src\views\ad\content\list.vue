<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="广告标题">
          <el-input v-model="queryParams.title" placeholder="广告标题" clearable></el-input>
        </el-form-item>
        <el-form-item label="广告位">
          <el-select v-model="queryParams.spaceId" placeholder="广告位" clearable>
            <el-option
              v-for="item in spaceOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="状态" clearable>
            <el-option label="上线" :value="1"></el-option>
            <el-option label="下线" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="primary" @click="handleCreate">添加广告</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="loading"
      :data="adList"
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="title" label="广告标题" width="200" />
      <el-table-column prop="space_name" label="广告位" width="150" />
      <el-table-column label="图片" width="150">
        <template slot-scope="scope">
          <el-image
            style="width: 100px; height: 60px"
            :src="formatImageUrl(scope.row.image)"
            :preview-src-list="[formatImageUrl(scope.row.image)]"
            fit="cover">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="url" label="链接" width="150">
        <template slot-scope="scope">
          <el-link type="primary" :href="scope.row.url" target="_blank">{{ scope.row.url }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="投放时间" width="300">
        <template slot-scope="scope">
          <span>{{ scope.row.start_time }} 至 {{ scope.row.end_time }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="view_count" label="曝光量" width="100" />
      <el-table-column prop="click_count" label="点击量" width="100" />
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
            {{ scope.row.status === 1 ? '上线' : '下线' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" align="center">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleUpdate(scope.row)"
          >编辑</el-button>
          <el-button
            size="mini"
            :type="scope.row.status === 1 ? 'warning' : 'success'"
            @click="handleChangeStatus(scope.row)"
          >{{ scope.row.status === 1 ? '下线' : '上线' }}</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { getAdContentList, updateAdContentStatus, deleteAdContent, formatImageUrl } from '@/api/advertisement'
import { getAdSpaceList } from '@/api/advertisement'

export default {
  name: 'AdContentList',
  data () {
    return {
      loading: false,
      total: 0,
      queryParams: {
        page: 1,
        limit: 10,
        title: undefined,
        spaceId: undefined,
        status: undefined
      },
      adList: [],
      spaceOptions: []
    }
  },
  created () {
    // 获取广告位选项
    this.getAdSpaces()
    this.getList()
  },
  methods: {
    formatImageUrl,
    getAdSpaces() {
      getAdSpaceList({ limit: 100 })
        .then(response => {
          console.log('广告位选项响应:', response)
          if (response.status === 'success' && response.data && response.data.list) {
            this.spaceOptions = response.data.list
          } else {
            this.$message.error(response.message || '获取广告位选项失败')
          }
        })
        .catch(error => {
          console.error('获取广告位选项错误:', error)
          this.$message.error('获取广告位选项失败')
        })
    },
    getList () {
      this.loading = true
      getAdContentList(this.queryParams)
        .then(response => {
          console.log('广告内容列表响应:', response)
          if (response.status === 'success') {
            // 为每个广告添加广告位名称
            this.adList = (response.data.list || []).map(ad => {
              const space = this.spaceOptions.find(s => s.id === ad.space_id)
              return {
                ...ad,
                space_name: ad.space_name || (space ? space.name : '未知广告位')
              }
            })
            this.total = response.data.total || 0
          } else {
            this.$message.error(response.message || '获取广告列表失败')
          }
        })
        .catch(error => {
          console.error('获取广告列表错误:', error)
          this.$message.error('获取广告列表失败')
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleQuery () {
      this.queryParams.page = 1
      this.getList()
    },
    resetQuery () {
      this.queryParams = {
        page: 1,
        limit: 10,
        title: undefined,
        spaceId: undefined,
        status: undefined
      }
      this.getList()
    },
    handleSizeChange (val) {
      this.queryParams.limit = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.queryParams.page = val
      this.getList()
    },
    handleCreate () {
      this.$router.push('/ad/content/create')
    },
    handleUpdate (row) {
      this.$router.push(`/ad/content/edit/${row.id}`)
    },
    handleChangeStatus (row) {
      const newStatus = row.status === 1 ? 0 : 1
      const statusText = newStatus === 1 ? '上线' : '下线'

      this.$confirm(`确认要${statusText}该广告吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updateAdContentStatus(row.id, { status: newStatus })
          .then(response => {
            console.log(`${statusText}广告响应:`, response)
            if (response.status === 'success') {
              this.$message.success(`${statusText}成功`)
              this.getList()
            } else {
              this.$message.error(response.message || `${statusText}失败`)
            }
          })
          .catch(error => {
            console.error(`${statusText}广告错误:`, error)
            this.$message.error(`${statusText}失败`)
          })
      }).catch(() => {})
    },
    handleDelete (row) {
      this.$confirm('确认要删除该广告吗？删除后不可恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAdContent(row.id)
          .then(response => {
            console.log('删除广告响应:', response)
            if (response.status === 'success') {
              this.$message.success('删除成功')
              this.getList()
            } else {
              this.$message.error(response.message || '删除失败')
            }
          })
          .catch(error => {
            console.error('删除广告错误:', error)
            this.$message.error('删除失败')
          })
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
}
.pagination-container {
  margin-top: 15px;
  text-align: right;
}
.el-image {
  object-fit: cover;
  border-radius: 4px;
}
</style>
